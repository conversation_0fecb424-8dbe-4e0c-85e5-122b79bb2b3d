package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeGroupFeeType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeGroupFeeTypeRepository extends JpaRepository<FeeGroupFeeType, Long> {
  List<FeeGroupFeeType> findAllByOrgSlug(String orgSlug);

  Optional<FeeGroupFeeType> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeGroupFeeType> findByFeeGroupIdAndOrgSlug(UUID feeGroupId, String orgSlug);

  Optional<FeeGroupFeeType> findByFeeGroupIdAndFeeTypeIdAndOrgSlug(
      UUID feeGroupId, UUID feeTypeId, String orgSlug);
}
