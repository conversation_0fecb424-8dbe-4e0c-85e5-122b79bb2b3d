package com.wexl.erp.leaves.repository;

import com.wexl.erp.leaves.model.LeaveStatus;
import com.wexl.erp.leaves.model.StudentLeaveRequest;
import com.wexl.retail.model.Student;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentLeaveRequestRepository extends JpaRepository<StudentLeaveRequest, Long> {

  List<StudentLeaveRequest> findByOrgSlugAndStudentOrderByAppliedDateDescStatusAsc(
      String orgSlug, Student student);

  List<StudentLeaveRequest> findByStatus(LeaveStatus status);

  @Query(
      value =
          "select slr.* from students s join users u on u.id=s.user_id join student_leave_requests slr on slr.student_id = s.id where s.section_id in (:sectionIds) order by slr.applied_date desc, CASE WHEN slr.status = 'PENDING' THEN 0 ELSE 1 END",
      nativeQuery = true)
  List<StudentLeaveRequest> findBySectionIds(List<Long> sectionIds);

  Optional<StudentLeaveRequest> findByIdAndStudent(Long id, Student student);

  List<StudentLeaveRequest> findByStudentIdInOrderByAppliedDateDescStatusAsc(List<Long> students);

  List<StudentLeaveRequest> findAllByOrgSlugOrderByAppliedDateDescStatusAsc(String orgSlug);
}
