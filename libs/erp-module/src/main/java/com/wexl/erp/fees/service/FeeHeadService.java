package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.FeeGroupFeeTypeRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeeMasterStudentRepository;
import com.wexl.erp.fees.service.rules.RuleDto;
import com.wexl.erp.fees.service.rules.RuleParamType;
import com.wexl.retail.model.Student;
import com.wexl.retail.util.ValidationUtils;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeHeadService {

  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;
  private final FeeHeadRepository feeHeadRepository;
  private final FeeMasterStudentRepository feeMasterStudentRepository;
  private final FeeRuleEngine feeRuleEngine;
  private final FeeService feeService;
  private final ValidationUtils validationUtils;

  public void createFeeHeads(UUID feeMasterId, FeeDto.FeeMasterRequest request, String orgSlug) {
    var feeGroup = feeService.getFeeGroupById(request.feeGroupId(), orgSlug);
    var feeMaster = feeService.getFeeMasterById(String.valueOf(feeMasterId), orgSlug);
    var students = feeRuleEngine.getStudentsForRule(buildRuleParam(request, feeMaster, orgSlug));
    var feeGroupFeeTypes =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug);

    if (students.isEmpty()) {
      return;
    }

    students.forEach(
        student -> processStudentFeeHeads(student, feeMaster, feeGroupFeeTypes, orgSlug));

    if (ScopeType.CUSTOM.equals(request.scopeType())) {
      saveFeeMasterStudents(feeMaster, students);
    }
  }

  private void processStudentFeeHeads(
      Student student,
      FeeMaster feeMaster,
      List<FeeGroupFeeType> feeGroupFeeTypes,
      String orgSlug) {
    for (FeeGroupFeeType feeGroupFeeType : feeGroupFeeTypes) {
      var feeHead = buildFeeHead(feeMaster, student, feeGroupFeeType, orgSlug);
      if (!checkIfAlreadyExists(student, feeMaster, feeHead.getFeeType())) {
        feeHeadRepository.save(feeHead);
      }
    }
  }

  private RuleDto.RuleParam buildRuleParam(
      FeeDto.FeeMasterRequest request, FeeMaster feeMaster, String orgSlug) {
    return RuleDto.RuleParam.builder()
        .feeMasterId(feeMaster.getId())
        .orgSlug(orgSlug)
        .paramType(getRuleParamType(request.scopeType()))
        .paramValues(getRuleParamValues(request))
        .build();
  }

  private RuleParamType getRuleParamType(ScopeType scopeType) {
    return switch (scopeType) {
      case SECTION -> RuleParamType.SECTION;
      case GRADE -> RuleParamType.GRADE;
      case SCHOOL -> RuleParamType.SCHOOL;
      case CUSTOM -> RuleParamType.STUDENT_ID;
    };
  }

  private List<String> getRuleParamValues(FeeDto.FeeMasterRequest request) {
    return switch (request.scopeType()) {
      case SECTION -> request.sectionUuid();
      case GRADE -> request.gradeSlug();
      case SCHOOL -> Collections.singletonList("school");
      case CUSTOM -> request.studentId().stream().map(String::valueOf).toList();
    };
  }

  private boolean checkIfAlreadyExists(Student student, FeeMaster feeMaster, FeeType feeType) {
    return feeHeadRepository.existsByFeeMasterAndStudentAndFeeType(feeMaster, student, feeType);
  }

  private void saveFeeMasterStudents(FeeMaster feeMaster, List<Student> students) {
    students.forEach(
        student ->
            feeMasterStudentRepository.save(
                FeeMasterStudent.builder().feeMaster(feeMaster).student(student).build()));
  }

  private FeeHead buildFeeHead(
      FeeMaster feeMaster, Student student, FeeGroupFeeType feeGroupFeeType, String orgSlug) {
    return FeeHead.builder()
        .feeMaster(feeMaster)
        .orgSlug(orgSlug)
        .student(student)
        .amount(feeGroupFeeType.getAmount())
        .balanceAmount(feeGroupFeeType.getAmount())
        .discountAmount(0.0)
        .fineAmount(feeGroupFeeType.getFineAmount())
        .feeType(feeGroupFeeType.getFeeType())
        .dueDate(feeGroupFeeType.getDueDate())
        .status(FeeStatus.UNPAID)
        .build();
  }

  public List<FeeDto.StudentsFeeHeadResponse> getFeeHeadsByStudent(
      String orgSlug, String authUserId) {
    var user = validationUtils.isValidUser(authUserId);
    var feeHeads =
        feeHeadRepository.findAllByStudentIdAndOrgSlug(user.getStudentInfo().getId(), orgSlug);
    if (feeHeads.isEmpty()) {
      return Collections.emptyList();
    }

    return feeHeads.stream()
        .map(
            feeHead -> {
              var student = feeHead.getStudent();
              var section = student.getSection();

              return FeeDto.StudentsFeeHeadResponse.builder()
                  .feeHeadId(feeHead.getId().toString())
                  .studentId(student.getId())
                  .studentName(user.getFirstName() + " " + user.getLastName())
                  .gradeSlug(section.getGradeSlug())
                  .gradeName(section.getGradeName())
                  .sectionUuid(section.getUuid().toString())
                  .sectionName(section.getName())
                  .amount(feeHead.getAmount())
                  .fineAmount(feeHead.getFineAmount())
                  .discountAmount(feeHead.getDiscountAmount())
                  .paidAmount(feeHead.getPaidAmount())
                  .balanceAmount(feeHead.getBalanceAmount())
                  .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
                  .feeTypeId(feeHead.getFeeType().getId())
                  .feeTypeCode(feeHead.getFeeType().getCode())
                  .feeTypeName(feeHead.getFeeType().getName())
                  .feeTypeDescription(feeHead.getFeeType().getDescription())
                  .feeMasterId(feeHead.getFeeMaster().getId())
                  .createdOn(convertIso8601ToEpoch(feeHead.getCreatedAt().toLocalDateTime()))
                  .build();
            })
        .toList();
  }
}
