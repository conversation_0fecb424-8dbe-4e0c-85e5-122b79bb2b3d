package com.wexl.erp.medical.controller;

import com.wexl.erp.medical.dto.MedicalProfileDto;
import com.wexl.erp.medical.service.MedicalService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/students")
public class MedicalController {

  private final MedicalService medicalService;

  @PostMapping("/{studentAuthId}/medical-history")
  public void createMedicalHistory(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestBody MedicalProfileDto.Request request) {
    medicalService.createMedicalHistory(orgSlug, studentAuthId, request);
  }

  @GetMapping("/{studentAuthId}/medical-history")
  public MedicalProfileDto.Response getMedicalHistory(@PathVariable String studentAuthId) {
    return medicalService.getMedicalHistory(studentAuthId);
  }

  @GetMapping("/medical-history")
  public List<MedicalProfileDto.Response> getMedicalHistoryOfOrg(@PathVariable String orgSlug) {
    return medicalService.getMedicalHistoryOfOrg(orgSlug);
  }
}
