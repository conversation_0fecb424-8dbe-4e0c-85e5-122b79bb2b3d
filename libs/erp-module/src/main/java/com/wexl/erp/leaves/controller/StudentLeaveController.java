package com.wexl.erp.leaves.controller;

import com.wexl.erp.leaves.dto.StudentLeaveRequestDto;
import com.wexl.erp.leaves.service.LeaveService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/students/{studentAuthId}/leaves")
public class StudentLeaveController {

  private final LeaveService leaveService;

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void applyForLeave(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestBody StudentLeaveRequestDto.Request request) {

    leaveService.applyForLeave(orgSlug, studentAuthId, request);
  }

  @GetMapping
  public List<StudentLeaveRequestDto.Response> getStudentLeaves(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {
    try {
      return leaveService.getStudentLeaveRequests(orgSlug, studentAuthId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/{leaveId}")
  public StudentLeaveRequestDto.Response updateLeaveRequest(
      @PathVariable String studentAuthId,
      @PathVariable Long leaveId,
      @RequestBody StudentLeaveRequestDto.Request request) {
    try {
      return leaveService.updateLeaveRequest(studentAuthId, leaveId, request);
    } catch (Exception e) {
      log.error("Error while updating leave request: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @DeleteMapping("/{leaveId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteLeaveRequest(@PathVariable Long leaveId) {
    try {
      leaveService.deleteLeaveRequest(leaveId);
    } catch (Exception e) {
      log.error("Error while deleting leave request: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/attachments")
  public List<S3FileUploadResult> uploadAttachments(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestPart(value = "files") List<MultipartFile> multipartFiles) {
    return leaveService.uploadLeaveAttachments(orgSlug, studentAuthId, multipartFiles);
  }
}
