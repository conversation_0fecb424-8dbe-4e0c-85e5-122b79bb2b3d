package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeGroup;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeGroupRepository extends JpaRepository<FeeGroup, Long> {
  List<FeeGroup> findAllByOrgSlug(String orgSlug);

  Optional<FeeGroup> findByIdAndOrgSlug(UUID feeTypeId, String orgSlug);
}
