package com.wexl.erp.fees.service.rules;

import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SectionRule implements Rule {

  private final StudentRepository studentRepository;

  @Override
  public boolean isApplicable(RuleDto.StudentFeeDto student, RuleDto.RuleParam ruleParam) {
    // Check if student.sectionSlug() is in ruleParam.paramValues(), check ignore case
    return ruleParam.paramValues().stream()
        .anyMatch(value -> value.equalsIgnoreCase(student.sectionUuid()));
  }

  @Override
  public boolean supports(RuleDto.RuleParam ruleParam) {
    return ruleParam.paramType().equals(RuleParamType.SECTION);
  }

  @Override
  public List<Student> getStudents(RuleDto.RuleParam ruleParam) {
    List<UUID> sectionUuids = ruleParam.paramValues().stream().map(UUID::fromString).toList();
    return studentRepository.getStudentsBySectionUuidsAndOrgSlug(sectionUuids, ruleParam.orgSlug());
  }
}
