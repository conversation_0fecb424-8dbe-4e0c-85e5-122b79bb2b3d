package com.wexl.retail.test.assignment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wexl.retail.test.school.domain.TestData;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssignmentRequest extends TestData {

  private long teacherId;

  private String artifactReference;

  private Boolean questionUploaded = false;

  private Boolean solutionUploaded = false;

  private List<AssignmentQuestionRequest> questions;
}
