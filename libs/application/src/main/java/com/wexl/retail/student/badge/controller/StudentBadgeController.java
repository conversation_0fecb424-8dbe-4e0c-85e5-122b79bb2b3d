package com.wexl.retail.student.badge.controller;

import com.wexl.retail.student.badge.dto.StudentBadgeDto;
import com.wexl.retail.student.badge.service.StudentBadgeService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{orgSlug}/")
@RequiredArgsConstructor
public class StudentBadgeController {

  private final StudentBadgeService studentBadgeService;

  @GetMapping("students/{authUserId}/badges")
  List<StudentBadgeDto> getStudentBadges() {
    return studentBadgeService.getAllStudentBadges();
  }
}
