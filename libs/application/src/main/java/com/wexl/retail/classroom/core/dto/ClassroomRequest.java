package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ClassroomRequest {

  @NotNull private String name;

  @JsonProperty("teacher_ids")
  private List<Long> teacherIds;

  @JsonProperty("student_ids")
  private List<Long> studentIds;

  private ClassRoomDto.Extensions extensions;
}
