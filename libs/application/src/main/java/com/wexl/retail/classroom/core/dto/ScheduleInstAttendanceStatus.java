package com.wexl.retail.classroom.core.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ScheduleInstAttendanceStatus {
  PRESENT("PRESENT"),
  ABSENT("ABSENT"),
  CLOSED("CLOSED"),
  HOLIDAY("HOLIDAY"),
  NOT_MARKED("NOT_MARKED"),
  HALF_DAY("HALF_DAY"),
  LATE_COMER("LATE_COMER"),
  LEAVE("LEAVE"),
  PTM("PTM"),
  NID("NID");

  private final String value;

  public static ScheduleInstAttendanceStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ScheduleInstAttendanceStatus enumEntry : ScheduleInstAttendanceStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
