package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubjectSummaryBuilder {

  @JsonProperty("subject_name")
  private String name;

  private String slug;

  @JsonProperty("chapters")
  private List<GenericDto> chapterData;
}
