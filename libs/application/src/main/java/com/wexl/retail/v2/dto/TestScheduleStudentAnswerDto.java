package com.wexl.retail.v2.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.school.dto.PbqDto;
import java.util.List;
import lombok.Builder;

public record TestScheduleStudentAnswerDto() {
  public record TestScheduleStudentAnswerRequest(
      @JsonProperty("mcq_selected_answer") Integer mcqSelectedAnswer,
      @JsonProperty("subjective_written_answer") String subjectiveWrittenAnswer,
      @JsonProperty("msq_selected_answer") List<Long> msqSelectedAnswer,
      @JsonProperty("yes_no_selected_answer") Boolean yesNoSelectedAnswer,
      @JsonProperty("nat_selected_answer") Float natSelectedAnswer,
      @JsonProperty("fbq_selected_answer") String fbqSelectedAnswer,
      @JsonProperty("amcq_selected_answer") Integer amcqSelectedAnswer,
      @JsonProperty("spch_selected_answer") String spchSelectedAnswer,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("attempted_status") StudentTestAttemptStatus attemptStatus,
      @JsonProperty("pbq_selected_answer") PbqDto.Data pbqSelectedAnswer,
      @JsonProperty("ddfbq_attempted_answer") String ddFbqAttemptedAnswer) {}

  @Builder
  public record TestDetails(List<BetTestDetails> betTestDetails) {}

  @Builder
  public record BetTestDetails(
      @JsonProperty("test_definition_name") String testName,
      @JsonProperty("test_schedule_id") Long testScheduleId) {}
}
