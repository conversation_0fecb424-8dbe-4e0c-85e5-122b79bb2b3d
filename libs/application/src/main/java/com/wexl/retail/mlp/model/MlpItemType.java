package com.wexl.retail.mlp.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MlpItemType {
  SYNOPSIS("SYNOPSIS"),
  VIDEO("VIDEO");

  private final String value;

  public static MlpItemType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (MlpItemType enumEntry : MlpItemType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
