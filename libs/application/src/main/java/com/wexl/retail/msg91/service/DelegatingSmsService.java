package com.wexl.retail.msg91.service;

import static com.wexl.retail.util.Constants.ACCEPTED_ORGS_FOR_TXT_LOCAL;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DelegatingSmsService {

  private final Msg91SmsService msg91SmsService;
  private final TextLocalSmsService textLocalSmsService;

  public SmsService get(String orgSlug) {
    if (ACCEPTED_ORGS_FOR_TXT_LOCAL.contains(orgSlug)) {
      return textLocalSmsService;
    }
    return msg91SmsService;
  }
}
