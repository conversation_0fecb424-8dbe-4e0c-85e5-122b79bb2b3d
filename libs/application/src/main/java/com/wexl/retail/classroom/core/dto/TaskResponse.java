package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskResponse {

  @JsonProperty("task_count")
  private Long taskCount;

  @JsonProperty("attempted_count")
  private Long attemptedCount;

  @JsonProperty("not_attempted_count")
  private Long notAttemptedCount;

  @JsonProperty("practice_activity_count")
  private Long practiceActivityCount;

  @JsonProperty("task_inst_responses")
  private List<TaskInstResponse> taskInstResponses;
}
