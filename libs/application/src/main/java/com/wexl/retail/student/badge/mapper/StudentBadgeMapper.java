package com.wexl.retail.student.badge.mapper;

import com.wexl.retail.model.Badge;
import com.wexl.retail.model.UserBadge;
import com.wexl.retail.student.badge.dto.StudentBadgeDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StudentBadgeMapper {
  StudentBadgeMapper mapper = Mappers.getMapper(StudentBadgeMapper.class);

  @Mapping(target = "badgeId", source = "badge.id")
  @Mapping(target = "badgeExpiryInDays", source = "badge.expireInDays")
  @Mapping(target = "badgeImageUrl", source = "badge.badgeImageUrl")
  @Mapping(target = "description", source = "badge.description")
  @Mapping(target = "seq", source = "badge.seq")
  @Mapping(target = "slug", source = "badge.slug")
  @Mapping(target = "issuedDate", source = "userBadge.issuedDate")
  @Mapping(target = "expiryDate", source = "userBadge.expiryDate")
  @Mapping(target = "badgeContext", source = "userBadge.context")
  @Mapping(target = "name", source = "badge.name")
  StudentBadgeDto studentBadgeToStudentBadgeDto(UserBadge userBadge, Badge badge);
}
