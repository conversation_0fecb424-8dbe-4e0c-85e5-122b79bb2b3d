package com.wexl.retail.student.publisher;

import com.wexl.retail.model.Student;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StudentPromotionPublisher {
  private final ApplicationEventPublisher applicationEventPublisher;

  public void publishEvent(final Student student) {
    applicationEventPublisher.publishEvent(new StudentPromotionEvent(student));
  }
}
