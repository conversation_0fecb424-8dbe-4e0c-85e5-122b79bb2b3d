package com.wexl.retail.student.exam;

import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
public class ExamResponse {
  private long id;
  private long examType;
  private long examDifficultyLevelId;
  private long goalItemId;
  private int allowedDuration;
  private int noOfQuestions;
  private Timestamp startTime;
  private String questionPreviewUrl;
}
