package com.wexl.retail.student.mlp.controller;

import com.wexl.retail.student.mlp.dto.MlpSummary;
import com.wexl.retail.student.mlp.dto.StudentsMlpOverview;
import com.wexl.retail.student.mlp.service.MlpStudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("orgs/{orgSlug}/")
public class MlpStudentController {

  private final MlpStudentService studentMlpService;

  @GetMapping("mlps/sections/{sectionUuid}")
  public MlpSummary getSectionMlpSummary(
      @PathVariable String orgSlug,
      @PathVariable String sectionUuid,
      @RequestParam String subject,
      @RequestParam String month) {
    return studentMlpService.getSectionMlpSummary(orgSlug, sectionUuid, subject, month);
  }

  @GetMapping("mlps/sections/{sectionUuid}/student-details")
  public StudentsMlpOverview getStudentsMlpOverview(
      @PathVariable String sectionUuid, @RequestParam String subject, @RequestParam String month) {
    return studentMlpService.getStudentsMlpOverview(sectionUuid, subject, month);
  }
}
