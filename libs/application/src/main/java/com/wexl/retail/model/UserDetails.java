package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class UserDetails {

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("full_name")
  private String fullName;

  private String section;
  private String email;

  @JsonProperty("mobile_number")
  private String mobileNumber;
}
