package com.wexl.retail.student.answer;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum McqOption {
  OPTION_ONE(1),
  OPTION_TWO(2),
  OPTION_THREE(3),
  OPTION_FOUR(4);

  private final int value;

  public static McqOption fromValue(int value) {
    if (value == 0) {
      throw new IllegalArgumentException("error.ValueCannotBeZero");
    }

    for (McqOption mcqOption : McqOption.values()) {
      if (mcqOption.value == value) {
        return mcqOption;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return String.valueOf(this.value);
  }
}
