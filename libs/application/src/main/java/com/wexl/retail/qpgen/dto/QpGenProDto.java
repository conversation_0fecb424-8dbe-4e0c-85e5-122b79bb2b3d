package com.wexl.retail.qpgen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.test.school.dto.TestDefinitionsDto;
import com.wexl.retail.test.school.dto.TestQuestionRequest;
import java.util.List;
import lombok.Builder;

public record QpGenProDto() {

  @Builder
  public record Request(
      String title,
      Long marks,
      Long duration,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_slug") List<String> chapterSlug,
      @JsonProperty("chapter_name") List<String> chapterName,
      @JsonProperty("blue_printId") Long bluePrintId) {}

  @Builder
  public record ContentRequest(
      @JsonProperty("sections_details") List<BluePrintDto.Section> sections,
      @JsonProperty("chapter_slug") List<String> chapterSlug) {}

  @Builder
  public record Section(
      Long id,
      @JsonProperty("question_count") Long questionCount,
      String complexity,
      String category,
      @JsonProperty("question_type") QuestionType questionType,
      Double marks,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record Response(
      Long id,
      String title,
      Long marks,
      Long duration,
      QpGenStatus status,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("created_by_authId") String createdBy,
      @JsonProperty("reviewer_name") String reviewerName,
      @JsonProperty("reviewer_authId") String reviewerAuthId,
      @JsonProperty("created_by_name") String createdByName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("blue_print_id") Long bluePrintId,
      @JsonProperty("no_of_questions") Long questionsCount,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("chapter_slug") List<String> chapterSlug,
      @JsonProperty("dummy_questions_exist") Boolean isDummyQuestionsExist,
      @JsonProperty("list_of_subjects") String listOfSubjects,
      @JsonProperty("blueprint_section_details")
          List<BluePrintSectionDetails> bluePrintSectionDetails,
      @JsonProperty("test_definition")
          TestDefinitionsDto.TestDefinitionResponse testDefinitionResponse) {}

  @Builder
  public record BluePrintSectionDetails(
      @JsonProperty("blueprint_section_name") String bpSectionName,
      @JsonProperty("bluePrint_section_marks") Long bpSectionMarks) {}

  @Builder
  public record Return(Long id) {}

  @Builder
  public record ChangeMarks(
      Long marks,
      @JsonProperty("question_uuid") String QuestionUuid,
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("test_definition_id") Long testDefinitionId) {}

  @Builder
  public record ReplaceQuestion(
      @JsonProperty("new_question") QpGenDto.QuestionsResponse question,
      Long marks,
      @JsonProperty("question_uuid") String QuestionUuid,
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("test_definition_id") Long testDefinitionId) {}

  @Builder
  public record InternalChoice(
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("question_id") Long questionId,
      @JsonProperty("internal_choice") TestQuestionRequest testQuestionRequest) {}

  @Builder
  public record InternalChoiceChangeMarks(
      @JsonProperty("internal_question_uuid") String questionUuid,
      @JsonProperty("test_question_uuid") String testQuestionUuid,
      @JsonProperty("section_id") Long sectionId,
      Integer marks) {}

  @Builder
  public record UpdateSectionQuestions(
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("question_type") String questionType,
      @JsonProperty("question_category") String questionCategory,
      @JsonProperty("question_category_name") String questionCategoryName,
      @JsonProperty("question_complexity") String questionComplexity,
      @JsonProperty("question_complexity_name") String questionComplexityName,
      @JsonProperty("total_available_questions") Long totalAvailableQuestions,
      @JsonProperty("selected_chapter_questions") Long selectedChapterQuestions,
      @JsonProperty("question_tags") String questionTags,
      @JsonProperty("questions_info") List<QuestionsInfo> questions) {}

  @Builder
  public record QuestionsInfo(
      @JsonProperty("marks") String marks,
      @JsonProperty("total_questions") Long totalQuestions,
      @JsonProperty("selected_questions") Long selectedQuestions) {}
}
