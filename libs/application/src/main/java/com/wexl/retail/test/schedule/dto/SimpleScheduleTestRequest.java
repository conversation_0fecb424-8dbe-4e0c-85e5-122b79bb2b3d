package com.wexl.retail.test.schedule.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleScheduleTestRequest {
  @NotNull private int duration;
  @NotNull private long testDefinitionId;

  @JsonProperty("studentId")
  @NotNull
  private Set<Long> studentIds;

  @NotNull private long startDate;
  @NotNull private long endDate;
  @NotNull private boolean allStudents;
  @NotNull private String message;
  private ScheduleTestMetadata metadata;
  private String published;
  private String type;
  private String orgSlug;
}
