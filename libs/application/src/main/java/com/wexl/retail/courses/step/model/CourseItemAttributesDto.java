package com.wexl.retail.courses.step.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record CourseItemAttributesDto() {
  @Builder
  public record Attributes(@JsonProperty("meta_data") MetaData metaData) {}

  @Builder
  public record MetaData(
      @JsonProperty("test_metadata") CourseItemAttributesTestMetadata testMetadata,
      @JsonProperty("video_metadata") CourseItemAttributesVideoMetadata videoMetadata) {}
}
