package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ClassroomTeacherAttendanceReport extends AbstractMetricHandler {
  @Override
  public String name() {
    return "classroom-teacher-attendance-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Long fromDate = (Long) genericMetricRequest.getInput().get("fromDate");
    Long toDate = (Long) genericMetricRequest.getInput().get("toDate");
    return classroomAttendanceService.getHostJoinedReport(org, fromDate, toDate);
  }
}
