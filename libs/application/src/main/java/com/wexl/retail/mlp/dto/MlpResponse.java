package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class MlpResponse {

  private Long id;

  private String title;

  private String uuid;

  private Integer questionCount;

  private String synopsisSlug;

  private String videoSlug;

  private String altVideoSlug;

  private String videoSha;

  private String videoSource;

  private String teacherName;

  private String comment;

  private String subTopicName;

  private String synopsisName;

  private String gradeName;

  private String chapterName;

  @JsonProperty("subtopic_slug")
  private String subTopic;

  @JsonProperty("chapter_slug")
  private String chapter;

  @JsonProperty("subject")
  private String subject;

  @JsonProperty("subject_name")
  private String subjectName;

  @CreatedDate private Long createdAt;

  @JsonProperty("exam_ref")
  private String examRef;

  private String sectionName;

  private String practiceStatus;

  private String synopsisStatus;

  private String videoStatus;

  @JsonProperty("questions_assignee_mode")
  private QuestionsAssigneeMode questionsAssigneeMode;

  @JsonProperty("exam_id")
  private long examId;

  @JsonProperty("knowledge_percentage")
  private Double knowledgePercentage;

  @JsonProperty("attendance_percentage")
  private Double attendancePercentage;

  @JsonProperty("source")
  private String source;
}
