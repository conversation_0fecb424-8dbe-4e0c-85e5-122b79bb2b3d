package com.wexl.retail.subjects.controller;

import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsDto;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.service.SubjectsMetaDataService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}")
@RequiredArgsConstructor
public class SubjectsMetaDataController {

  private final SubjectsMetaDataService subjectsMetaDataService;

  @PostMapping("/subjects-metadata")
  public void saveSubjectsMetaData(
      @RequestBody SubjectsDto.Request request, @PathVariable String orgSlug) {
    subjectsMetaDataService.saveSubjectsMetaData(request, orgSlug);
  }

  @GetMapping("/subjects-metadata")
  public List<SubjectsDto.Response> getSubjectsMetaData(
      @RequestParam(value = "board", required = false) String boardSlug,
      @RequestParam(value = "grade", required = false) String gradeSlug,
      @RequestParam(value = "category", required = false) SubjectsCategoryEnum subjectsCategory,
      @RequestParam(value = "type", required = false) SubjectsTypeEnum subjectType,
      @PathVariable String orgSlug,
      @RequestParam(value = "childOrg", required = false) String childOrg) {
    return subjectsMetaDataService.getSubjectsMetaData(
        boardSlug,
        gradeSlug,
        subjectsCategory,
        subjectType,
        Objects.isNull(childOrg) ? orgSlug : childOrg);
  }

  @DeleteMapping("/subjects-metadata/{subjectId}")
  public void deleteSubjectMetaData(@PathVariable Long subjectId) {
    subjectsMetaDataService.deleteSubjectMetaData(subjectId);
  }

  @PutMapping("/subjects-metadata/{subjectId}")
  public void editSubjectsMetaData(
      @PathVariable Long subjectId, @RequestBody SubjectsDto.Request request) {
    subjectsMetaDataService.editSubjectsMetaData(request, subjectId);
  }

  @PostMapping("/subjects-metadata/{subjectId}")
  public void assignToStudents(
      @RequestBody SubjectsDto.StudentsRequest request, @PathVariable Long subjectId) {
    subjectsMetaDataService.assignToStudents(request, subjectId);
  }

  @GetMapping("/subjects-metadata/{subjectId}")
  public SubjectsDto.StudentsResponse getStudentsAssigned(
      @PathVariable Long subjectId,
      @RequestParam(value = "section_uuid", required = false) String sectionUuid) {
    return subjectsMetaDataService.getStudentsAssigned(subjectId, sectionUuid);
  }

  @DeleteMapping("/subjects-metadata/{subjectId}/students/{studentAuthId}")
  public void deleteStudentSubject(
      @PathVariable Long subjectId, @PathVariable String studentAuthId) {
    subjectsMetaDataService.deleteStudentSubject(subjectId, studentAuthId);
  }

  @GetMapping("/subject-metadata:download")
  public List<SubjectsDto.StudentSubjectMetadataResponse> getStudentSubjectMetadata(
      @PathVariable String orgSlug, @RequestParam String board, @RequestParam String grade) {
    return subjectsMetaDataService.getStudentSubjectMetadata(orgSlug, board, grade);
  }
}
