package com.wexl.retail.v2.service;

import static com.wexl.retail.notifications.dto.NotificationType.INDIVIDUAL;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.core.TokenGenerator;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.latex.LatexToMathMLTransformer;
import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.mlp.service.VideoUrlEncryptor;
import com.wexl.retail.model.Student;
import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.service.Msg91SmsService;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.student.exam.migration.ExamMigrationService;
import com.wexl.retail.student.exam.publisher.AdmissionTestCompletionEventPublisher;
import com.wexl.retail.student.exam.publisher.MockExamCompletionEventPublisher;
import com.wexl.retail.student.exam.revision.repository.ExamRevisionRepository;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.ExamResultDto;
import com.wexl.retail.test.schedule.dto.ScheduleTestResponse;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentResponseImpl;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.TestDefinitionsDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestDefinitionSectionRepository;
import com.wexl.retail.test.school.repository.TestEnrichmentRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.UploadService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.dto.AnswerSheetTemplatesDto;
import com.wexl.retail.v2.dto.BetDtoBody;
import com.wexl.retail.v2.dto.StudentResultDto;
import com.wexl.retail.v2.dto.TestScheduleStudentAnswerDto;
import com.wexl.retail.v2.repository.ScAnswerSheetTemplatesRepository;
import jakarta.transaction.Transactional;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleTestStudentService {

  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final UserRepository userRepository;
  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;
  private final ExamService examService;
  private final ScAnswerSheetTemplatesRepository scAnswerSheetTemplatesRepository;

  private final StorageService storageService;
  private final DateTimeUtil dateTimeUtil;

  private final TestDefinitionRepository testDefinitionRepository;
  private final AuthService authService;
  private final UserService userService;
  private final StudentService studentService;

  private final TestDefinitionService testDefinitionService;
  private final TestQuestionRepository testQuestionRepository;
  private final TestDefinitionSectionRepository testDefinitionSectionRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final StudentRepository studentRepository;
  private final ExamRepository examRepository;
  private final VideoUrlEncryptor videoUrlEncryptor;
  private final Msg91SmsService msg91SmsService;
  private final EventNotificationService eventNotificationService;
  private final NotificationsService notificationService;
  private final OrganizationRepository organizationRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final MockExamCompletionEventPublisher mockExamCompletionEventPublisher;
  private final ValidationUtils validationUtils;
  private final ExamMigrationService examMigrationService;
  private final ReportCardTemplateService reportCardTemplateService;
  private final SpringTemplateEngine templateEngine;
  private final RestTemplate restTemplate;
  private final ExamRevisionRepository examRevisionRepository;
  private final UploadService uploadService;
  private final TestEnrichmentRepository testEnrichmentRepository;
  private final OrgSettingsService orgSettingsService;
  private final String WEXL_STRAPI_IMAGES = "wexl-strapi-images";
  private final String NOT_ANSWERED = "Not Answered";
  private final TokenGenerator tokenGenerator;
  private final UserRoleHelper userRoleHelper;
  private final AdmissionTestCompletionEventPublisher admissionTestCompletionEventPublisher;
  private final LatexToMathMLTransformer latexToMathMLTransformer;

  public static final Set<ProctoringStatus> PROCTORING_COMPLETED_STATUSES =
      EnumSet.of(ProctoringStatus.SUSPICIOUS, ProctoringStatus.HONEST);

  @Value("${app.publicDomainUrl}")
  private String appDomainUrl;

  @Value("${app.omrPath}")
  String omrPath;

  @Value("${urls.correction}")
  private String correctionServiceBaseUrl;

  @Value("${app.storageBucket}")
  private String s3BucketName;

  public QuestionDto.QuestionsResponse startMockExam(String studentId, String uuid) {

    ScheduleTestStudent scheduleTestStudent = getScheduleTestStudentByUuid(studentId, uuid);

    var scheduleTest = scheduleTestStudent.getScheduleTest();

    if (List.of(TestStudentStatus.SUBMITTED.name(), TestStudentStatus.COMPLETED.name())
            .contains(scheduleTestStudent.getStatus())
        && Objects.nonNull(scheduleTestStudent.getEndTime())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.scheduleTime");
    } else if (Objects.isNull(scheduleTestStudent.getAllowedEndTime())
        && TestStudentStatus.PENDING.name().equals(scheduleTestStudent.getStatus())) {
      return startExamFirstTime(scheduleTest, scheduleTestStudent, uuid, studentId);
    }
    return restartExam(scheduleTest, scheduleTestStudent, uuid, studentId);
  }

  public List<QuestionDto.StudentQuestionStatusResponse> buildQuestionResponse(
      List<TestScheduleStudentAnswer> testScheduleStudentAnswers) {
    return testScheduleStudentAnswers.stream()
        .map(
            tssa -> {
              var testQuestion =
                  testQuestionRepository.findByIdAndQuestionUuid(
                      tssa.getTestQuestionId(), tssa.getQuestionUuid());
              return QuestionDto.StudentQuestionStatusResponse.builder()
                  .questionUuid(tssa.getQuestionUuid())
                  .testQuestionMarks(testQuestion.getMarks())
                  .questionType(tssa.getQuestionType())
                  .tssaUuid(tssa.getUuid().toString())
                  .status(tssa.getAttemptStatus())
                  .mcqSelectedAnswer(tssa.getMcqSelectedAnswer())
                  .subjectiveWrittenAnswer(tssa.getSubjectiveWrittenAnswer())
                  .msqSelectedAnswer(tssa.getMsqSelectedAnswer())
                  .natSelectedAnswer(tssa.getNatSelectedAnswer())
                  .yesNoSelectedAnswer(tssa.getYesNoSelectedAnswer())
                  .fbqSelectedAnswer(tssa.getFbqSelectedAnswer())
                  .pbqSelectedAnswer(tssa.getPbqAnswers())
                  .amcqSelectedAnswer(tssa.getAmcqSelectedAnswer())
                  .spchSelectedAnswer(tssa.getSpchSelectedAnswer())
                  .ddFbqAttemptedAnswer(tssa.getDdfbqAttemptedAnswer())
                  .build();
            })
        .toList();
  }

  private QuestionDto.QuestionsResponse restartExam(
      ScheduleTest scheduleTest,
      ScheduleTestStudent scheduleTestStudent,
      String uuid,
      String studentId) {
    if (scheduleTestStudent.getAllowedEndTime().isBefore(scheduleTestStudent.getAllowedEndTime())
        || scheduleTestStudent.getAllowedEndTime().isBefore(LocalDateTime.now())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Completed");
    }
    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuidAndUserName(uuid, studentId);

    return QuestionDto.QuestionsResponse.builder()
        .Instructions(scheduleTest.getTestDefinition().getInstructions())
        .duration(
            dateTimeUtil.getAvailableExamDuration(
                LocalDateTime.now(), scheduleTestStudent.getAllowedEndTime()))
        .StudentQuestionStatusResponse(buildQuestionResponse(testScheduleStudentAnswers))
        .build();
  }

  private QuestionDto.QuestionsResponse startExamFirstTime(
      ScheduleTest scheduleTest,
      ScheduleTestStudent scheduleTestStudent,
      String uuid,
      String studentId) {
    LocalDateTime startDate = scheduleTest.getStartDate();
    long epochStart = dateTimeUtil.convertIso8601ToEpoch(startDate);
    LocalDateTime startingTime = dateTimeUtil.convertEpochToIso8601(epochStart);
    DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("hh:mm a");
    String timeOnly = startingTime.format(timeFormatter);
    if (scheduleTest.getStartDate().isAfter(LocalDateTime.now())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Exam.NotStarted", new String[] {timeOnly});
    } else if (scheduleTest.getEndDate().isBefore(LocalDateTime.now())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Completed");
    }

    scheduleTestStudent.setStartTime(LocalDateTime.now());
    scheduleTestStudent.setStatus(TestStudentStatus.STARTED.name());
    scheduleTestStudent.setAllowedEndTime(
        LocalDateTime.now().plusMinutes(scheduleTest.getDuration()));
    scheduleTestStudentRepository.save(scheduleTestStudent);
    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuidAndUserName(uuid, studentId);

    return QuestionDto.QuestionsResponse.builder()
        .Instructions(scheduleTest.getTestDefinition().getInstructions())
        .duration((long) scheduleTest.getDuration() * 60)
        .StudentQuestionStatusResponse(buildQuestionResponse(testScheduleStudentAnswers))
        .build();
  }

  private ScheduleTestStudent getScheduleTestStudentByUuid(String studentId, String tssUuid) {

    var studentUser =
        userRepository
            .findByAuthUserId(studentId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUser"));
    return scheduleTestStudentRepository
        .findByStudentAndUuid(studentUser, tssUuid)
        .orElseThrow(
            () ->
                new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentTestNotFound"));
  }

  public void saveMockExamAnswer(
      String studentId,
      String answerUuid,
      TestScheduleStudentAnswerDto.TestScheduleStudentAnswerRequest
          testScheduleStudentAnswerRequest,
      String tssUuid) {

    var tssa =
        studentScheduleTestAnswerRepository
            .findByUuidAndUserNameAndTssUuid(
                UUID.fromString(answerUuid), studentId, String.valueOf(tssUuid))
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidAnswerUuid"));

    tssa.setMcqSelectedAnswer(testScheduleStudentAnswerRequest.mcqSelectedAnswer());
    tssa.setSubjectiveWrittenAnswer(testScheduleStudentAnswerRequest.subjectiveWrittenAnswer());
    tssa.setMsqSelectedAnswer(testScheduleStudentAnswerRequest.msqSelectedAnswer());
    tssa.setNatSelectedAnswer(testScheduleStudentAnswerRequest.natSelectedAnswer());
    tssa.setYesNoSelectedAnswer(testScheduleStudentAnswerRequest.yesNoSelectedAnswer());
    tssa.setAmcqSelectedAnswer(testScheduleStudentAnswerRequest.amcqSelectedAnswer());
    tssa.setSpchSelectedAnswer(testScheduleStudentAnswerRequest.spchSelectedAnswer());
    tssa.setFbqSelectedAnswer(testScheduleStudentAnswerRequest.fbqSelectedAnswer());
    tssa.setAttemptStatus(testScheduleStudentAnswerRequest.attemptStatus());
    tssa.setPbqAnswers(testScheduleStudentAnswerRequest.pbqSelectedAnswer());
    tssa.setDdfbqAttemptedAnswer(testScheduleStudentAnswerRequest.ddFbqAttemptedAnswer());
    tssa.setSubmittedTime(LocalDateTime.now());
    studentScheduleTestAnswerRepository.save(tssa);
  }

  public QuestionDto.SubmitMockExamResponse submitMockExam(String studentId, String tssUuid) {

    ScheduleTestStudent scheduleTestStudent = getScheduleTestStudentByUuid(studentId, tssUuid);
    var studentUser = userRepository.findByAuthUserId(studentId).orElseThrow();
    var org = orgSettingsService.validateOrganizaiton(studentUser.getOrganization());
    if (TestStudentStatus.COMPLETED.name().equals(scheduleTestStudent.getStatus())) {
      var exam =
          examRepository
              .findByScheduleTestAndStudentIn(
                  scheduleTestStudent.getScheduleTest(),
                  Collections.singletonList(studentUser.getStudentInfo()))
              .getFirst();
      return QuestionDto.SubmitMockExamResponse.builder().examId(exam.getId()).build();
    }
    var testDefinition = scheduleTestStudent.getScheduleTest().getTestDefinition();
    scheduleTestStudent.setEndTime(LocalDateTime.now());
    if (testDefinition.getCategory().equals(TestCategory.LWS)
        || testDefinition.getCategory().equals(TestCategory.BET_LWS)
        || testDefinition.getCategory().equals(TestCategory.BET)) {
      scheduleTestStudent.setStatus(TestStudentStatus.PROCESSING.name());
      scheduleTestStudentRepository.save(scheduleTestStudent);
      var questions =
          examMigrationService.getContentQuestionByDefinitionAndType(
              scheduleTestStudent.getScheduleTest().getTestDefinition(), QuestionType.SUBJECTIVE);
      var exam = examMigrationService.processMigration(scheduleTestStudent, questions);
      getBetReportCard(exam);
      var studentInfo =
          scheduleTestRepository.getAdmissionTestStudentInfo(
              scheduleTestStudent.getScheduleTest().getId());
      if (studentInfo.isPresent()) {
        admissionTestCompletionEventPublisher.publishAdmissionTestCompletion(exam);
        return QuestionDto.SubmitMockExamResponse.builder()
            .examId(exam.getId())
            .reportCardUrl(generateAndFetchBetReportCard(exam))
            .reportCardName(
                String.format(
                        "%s_%s_%s",
                        studentInfo.get().getName(), studentInfo.get().getGrade(), LocalDate.now())
                    .replace(" ", "_"))
            .build();
      }
      return QuestionDto.SubmitMockExamResponse.builder()
          .examId(exam.getId())
          .reportCardUrl(generateAndFetchBetReportCard(exam))
          .reportCardName(
              String.format(
                      "%s_%s_%s",
                      studentUser.getFirstName(),
                      studentUser.getStudentInfo().getSection().getGradeName(),
                      LocalDate.now())
                  .replace(" ", "_"))
          .build();
    } else {
      scheduleTestStudent.setStatus(TestStudentStatus.SUBMITTED.name());
      scheduleTestStudentRepository.save(scheduleTestStudent);
      return QuestionDto.SubmitMockExamResponse.builder().build();
    }
  }

  private String generateAndFetchBetReportCard(Exam exam) {
    if (TestCategory.BET.equals(exam.getTestDefinition().getCategory())) {
      var user = exam.getStudent().getUserInfo();
      return constructBetReportCardLink(user.getOrganization(), exam.getRef());
    }
    return null;
  }

  private String constructBetReportCardLink(String orgSlug, String ref) {
    return "https://images.wexledu.com/%s"
        .formatted(String.format("%s/bet-report-card/%s", orgSlug, ref));
  }

  public QuestionDto.QuestionResponse getExamQuestionResponse(String studentId, String tssUuid) {

    ScheduleTestStudent scheduleTestStudent = getScheduleTestStudentByUuid(studentId, tssUuid);
    if (TestStudentStatus.STARTED.name().equals(scheduleTestStudent.getStatus())
        && scheduleTestStudent.getAllowedEndTime().isAfter(LocalDateTime.now())) {
      return testDefinitionService.getQuestionResponsePreconfigured(
          scheduleTestStudent.getScheduleTest().getTestDefinition(),
          scheduleTestStudent.getQuestionResponseSet());
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "could not load questions");
  }

  public QuestionDto.StudentResultsResponse getExamResult(long examId) {

    Exam exam = examService.findById(examId);

    TestDefinition testDefinition = exam.getTestDefinition();
    final TestDefinitionMetadata.VideoExplanationMetadata videoMetadata =
        examService.getVideoMetadata(testDefinition);

    var scheduleTestStudent =
        exam.getScheduleTest().getScheduleTestStudent().stream()
            .filter(
                tss ->
                    tss.getStudent()
                        .getUserName()
                        .equals(exam.getStudent().getUserInfo().getUserName()))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "schedule test not found for this student"));
    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(
            testDefinition, getQuestionResponseSet(scheduleTestStudent));
    List<QuestionDto.TestDefinitionSection> testDefinitionSectionResults =
        buildTestDefinitionSectionResult(questionResponse, exam, testDefinition);
    boolean isBetMockTest = false;
    Float totalMarksSecured = 0.0f;

    if (testDefinition.getTestName().toLowerCase().contains("bet")) {
      isBetMockTest = true;
      totalMarksSecured =
          (float)
              testDefinitionSectionResults.stream()
                  .mapToDouble(QuestionDto.TestDefinitionSection::marks)
                  .sum();
    }
    var hasPdfAnswerSheet = examService.checkIfPdfAnswerSheetAvailable(exam);
    var filePath = uploadService.constructAnswerSheetFilePath(examId, "%s.pdf".formatted(examId));

    return QuestionDto.StudentResultsResponse.builder()
        .examId(examId)
        .testDefinitionId(testDefinition.getId())
        .testName(testDefinition.getTestName())
        .questionPaperRef(buildPath(testDefinition.getQuestionPath()))
        .answersPaperRef(buildPath(testDefinition.getSolutionPath()))
        .omrImagePath(buildOmrImagePath(scheduleTestStudent.getOmrImagePath()))
        .gradeName(testDefinition.getGradeSlug())
        .noOfQuestions(testDefinition.getNoOfQuestions().longValue())
        .hasPdfAnswerSheet(hasPdfAnswerSheet)
        .pdfAnswerSheetUrl(hasPdfAnswerSheet ? examService.getPdfAnswerSheetUrl(exam) : "")
        .uploadCorrectedAnswerSheetUrl(
            hasPdfAnswerSheet ? storageService.generatePreSignedUrlForUpload(filePath) : null)
        .testDefinitionSection(testDefinitionSectionResults)
        .totalMarksSecured(
            isBetMockTest
                ? (totalMarksSecured < 0 ? 0.0f : totalMarksSecured)
                : (exam.getMarksScored() < 0 ? 0.0f : exam.getMarksScored()))
        .totalMarks(exam.getTotalMarks())
        .explanationVideoSha(videoUrlEncryptor.convertEncrypted(videoMetadata.getAltVimeoLink()))
        .assetSlug(examService.getAssetSlug(testDefinition))
        .attemptedQuestions(
            Objects.nonNull(exam.getAttemptedQuestionsCount())
                ? exam.getAttemptedQuestionsCount()
                : getAttemptedQuestionCount(scheduleTestStudent).size())
        .percentageSecured(
            isBetMockTest
                ? ((totalMarksSecured / exam.getTotalMarks() * 100) < 0
                    ? 0.0f
                    : (float) Math.round(totalMarksSecured / exam.getTotalMarks() * 100))
                : ((exam.getMarksScored() / exam.getTotalMarks() * 100) < 0
                    ? 0.0f
                    : (float) Math.round(exam.getMarksScored() / exam.getTotalMarks() * 100)))
        .build();
  }

  private Integer getQuestionResponseSet(ScheduleTestStudent scheduleTestStudent) {
    return AuthUtil.isStudent(authService.getUserDetails())
        ? scheduleTestStudent.getQuestionResponseSet()
        : 0;
  }

  private String buildOmrImagePath(String omrImagePath) {
    if (omrImagePath == null) {
      return null;
    }
    return "%s/%s".formatted(omrPath, omrImagePath);
  }

  private String buildPath(String path) {
    return buildOmrImagePath(storageService.generatePreSignedUrlForFetch(path));
  }

  public List<QuestionDto.TestDefinitionSection> buildTestDefinitionSectionResult(
      QuestionDto.QuestionResponse questionResponse, Exam exam, TestDefinition testDefinition) {

    int questionCount = 1;
    List<QuestionDto.TestDefinitionSection> testDefinitionSections = new ArrayList<>();

    for (var tssResponse : questionResponse.testDefinitionSectionResponses()) {

      var questionResult =
          buildQuestionResult(
              tssResponse.questions(),
              exam,
              testDefinition.getTestDefinitionSections(),
              questionCount);

      double sectionMarks =
          questionResult.stream().mapToDouble(QuestionDto.QuestionResult::marksSecured).sum();

      testDefinitionSections.add(
          QuestionDto.TestDefinitionSection.builder()
              .id(tssResponse.id())
              .name(tssResponse.name())
              .seqNo(tssResponse.seqNo())
              .noOfQuestions(tssResponse.noOfQuestions())
              .questionResults(questionResult)
              .marks(Double.valueOf(String.format("%.1f", sectionMarks)))
              .build());
      questionCount += tssResponse.noOfQuestions();
    }
    return testDefinitionSections;
  }

  public List<TestScheduleStudentAnswer> getAttemptedQuestionCount(
      ScheduleTestStudent scheduleTestStudent) {

    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuid(scheduleTestStudent.getUuid());
    return testScheduleStudentAnswers.stream()
        .filter(
            tssa -> tssa.getAttemptStatus().name().equals(StudentTestAttemptStatus.ANSWERED.name()))
        .toList();
  }

  public List<QuestionDto.QuestionResult> buildQuestionResult(
      List<QuestionDto.Question> questions,
      Exam exam,
      List<TestDefinitionSection> testDefinitionSection,
      int initialQuestionCount) {
    AtomicInteger questionCount = new AtomicInteger(initialQuestionCount);

    HashMap<String, ExamAnswer> resultMap = new HashMap<>();
    exam.getExamAnswers()
        .forEach(examAnswer -> resultMap.put(examAnswer.getQuestionUuid(), examAnswer));

    HashMap<String, TestQuestion> testQuestionMap = new HashMap<>();

    var testQuestions =
        testDefinitionSection.stream()
            .map(TestDefinitionSection::getTestQuestions)
            .flatMap(List::stream)
            .toList();

    testQuestions.forEach(tds -> testQuestionMap.put(tds.getQuestionUuid(), tds));

    return questions.stream()
        .map(
            question ->
                buildQuestionResult(
                    question, resultMap, testQuestionMap, questionCount.getAndIncrement()))
        .filter(Objects::nonNull)
        .toList();
  }

  private QuestionDto.QuestionResult buildQuestionResult(
      QuestionDto.Question question,
      HashMap<String, ExamAnswer> resultMap,
      HashMap<String, TestQuestion> testQuestionMap,
      int questionCount) {
    if (Objects.isNull(resultMap.get(question.uuid()))) {
      return null;
    }
    return QuestionDto.QuestionResult.builder()
        .id(question.id())
        .questionSeqNo(questionCount)
        .question(question.question())
        .audioPath(question.audioPath())
        .videoPath(question.videoPath())
        .chapterSlug(question.chapterSlug())
        .subtopicSlug(question.subtopicSlug())
        .explanation(question.explanation())
        .uuid(question.uuid())
        .category(question.category())
        .questionTags(String.valueOf(question.questionTags()))
        .complexity(question.complexity())
        .type(question.type())
        .marks(question.marks())
        .isCorrected(resultMap.get(question.uuid()).isCorrect())
        .isAttempted(resultMap.get(question.uuid()).isAttempted())
        .negativeMarks(question.negativeMarks())
        .mcqSelectedAnswer(
            Objects.nonNull(resultMap.get(question.uuid()).getSelectedOption())
                ? resultMap.get(question.uuid()).getSelectedOption().longValue()
                : null)
        .yesNoSelectedAnswer(resultMap.get(question.uuid()).getYesNoSelectedAnswer())
        .natSelectedAnswer(resultMap.get(question.uuid()).getNatSelectedAnswer())
        .subjectiveSelectedAnswer(resultMap.get(question.uuid()).getSubjectiveWrittenAnswer())
        .aiMarks(resultMap.get(question.uuid()).getAiMarks())
        .aiAnalysis(resultMap.get(question.uuid()).getAiAnalysis())
        .msqSelectedAnswer(resultMap.get(question.uuid()).getMsqSelectedAnswer())
        .fbqSelectedAnswer(resultMap.get(question.uuid()).getFbqSelectedAnswer())
        .amcqSelectedAnswer(resultMap.get(question.uuid()).getAmcqSelectedAnswer())
        .spchSelectedAnswer(resultMap.get(question.uuid()).getSpchSelectedAnswer())
        .ddFbqSelectedAnswer(resultMap.get(question.uuid()).getDdfbqAttemptedAnswer())
        .marksSecured(getMarksSecured(resultMap.get(question.uuid()), question.type()))
        .spch(
            testDefinitionService.buildSpchQuestion(
                question.spch(), testQuestionMap.get(question.uuid()).getSpchAnswer()))
        .amcq(
            testDefinitionService.buildAmcqQuestion(
                question.amcq(), testQuestionMap.get(question.uuid()).getAmcqAnswer()))
        .subjective(testDefinitionService.buildSubjectiveQuestion(question.subjective()))
        .mcq(
            testDefinitionService.buildMcqQuestion(
                question.mcq(), testQuestionMap.get(question.uuid()).getMcqAnswer()))
        .nat(
            testDefinitionService.buildNatQuestion(
                question.nat(), testQuestionMap.get(question.uuid()).getNatAnswer()))
        .fbq(
            testDefinitionService.buildFbqQuestion(
                question.fbq(), testQuestionMap.get(question.uuid()).getFbqAnswer()))
        .pbq(
            testDefinitionService.buildPbqQuestion(
                question.pbq(),
                resultMap.get(question.uuid()).getPbqAnswers(),
                testQuestionMap.get(question.uuid()).getPbqAnswers()))
        .msq(
            testDefinitionService.buildMsqQuestion(
                question.msq(), testQuestionMap.get(question.uuid()).getMsqAnswer()))
        .yesNo(
            testDefinitionService.buildYesNoQuestion(
                question.yesNo(), testQuestionMap.get(question.uuid()).getYesNo()))
        .ddFbq(
            testDefinitionService.buildDdFbqQuestion(
                question.ddFbq(), testQuestionMap.get(question.uuid()).getDdFbqAnswer()))
        .build();
  }

  private Float getMarksSecured(ExamAnswer examAnswer, QuestionType questionType) {
    if (Objects.isNull(examAnswer.getMarksScoredPerQuestion())
        && QuestionType.SUBJECTIVE.equals(questionType)
        && Objects.nonNull(examAnswer.getAiMarks())) {
      return examAnswer.getAiMarks();
    }
    return Float.valueOf(
        String.format(
            "%.1f",
            examAnswer.getMarksScoredPerQuestion() == null
                ? 0.0F
                : examAnswer.getMarksScoredPerQuestion()));
  }

  public QuestionDto.MockExamAnalyticResponse getMockExamAnalytics(long scheduleTestId) {

    var scheduleTest =
        scheduleTestRepository
            .findById(scheduleTestId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid schedule"));

    List<ScheduleTestStudent> totalScheduleTestStudents = new ArrayList<>();
    List<ScheduleTestStudent> scheduleTestStudents = scheduleTest.getScheduleTestStudent();

    if (scheduleTestStudents.isEmpty()) {
      List<ScheduleTest> childScheduleTests = scheduleTestRepository.findAllByParent(scheduleTest);

      childScheduleTests.forEach(
          childTestSchedule -> {
            List<ScheduleTestStudent> testScheduleStudents =
                childTestSchedule.getScheduleTestStudent();
            totalScheduleTestStudents.addAll(testScheduleStudents);
          });
    }
    totalScheduleTestStudents.addAll(scheduleTestStudents);

    var testCompletedStudents =
        totalScheduleTestStudents.stream()
            .filter(
                tss ->
                    List.of(TestStudentStatus.SUBMITTED.name(), TestStudentStatus.COMPLETED.name())
                        .contains(tss.getStatus()))
            .toList();

    TestDefinition testDefinition = scheduleTest.getTestDefinition();

    return QuestionDto.MockExamAnalyticResponse.builder()
        .questionResponse(getQuestionResponse(testDefinition))
        .teacherName(userService.getNameByUserInfo(authService.getTeacherDetails()))
        .title(testDefinition.getTestName())
        .createdDate(
            DateTimeUtil.convertIso8601ToEpoch(
                scheduleTest.getTestDefinition().getCreatedAt().toLocalDateTime()))
        .questionDetails(getQuestionDetails(scheduleTest))
        .studentCount(totalScheduleTestStudents.size())
        .attemptedCount(testCompletedStudents.size())
        .build();
  }

  private QuestionDto.QuestionResponse getQuestionResponse(TestDefinition testDefinition) {
    var testDefinitionSections = testDefinition.getTestDefinitionSections();

    var questionResponseFromS3 =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);

    HashMap<String, TestQuestion> testQuestionMap = new HashMap<>();

    var testQuestions =
        testDefinitionSections.stream()
            .map(TestDefinitionSection::getTestQuestions)
            .flatMap(List::stream)
            .toList();

    testQuestions.forEach(tds -> testQuestionMap.put(tds.getQuestionUuid(), tds));

    return QuestionDto.QuestionResponse.builder()
        .testDefinitionId(testDefinition.getId())
        .gradeName(testDefinition.getGradeSlug())
        .noOfQuestions((long) testQuestions.size())
        .testName(testDefinition.getTestName())
        .testDefinitionSectionResponses(
            buildTestDefinitionSectionResponse(questionResponseFromS3, testQuestionMap))
        .build();
  }

  private List<QuestionDto.TestDefinitionSectionResponse> buildTestDefinitionSectionResponse(
      QuestionDto.QuestionResponse questionResponse,
      HashMap<String, TestQuestion> testQuestionMap) {

    return questionResponse.testDefinitionSectionResponses().stream()
        .map(
            tssResponse ->
                QuestionDto.TestDefinitionSectionResponse.builder()
                    .id(tssResponse.id())
                    .name(tssResponse.name())
                    .seqNo(tssResponse.seqNo())
                    .noOfQuestions(tssResponse.noOfQuestions())
                    .seqNo(tssResponse.seqNo())
                    .questions(buildQuestions(tssResponse.questions(), testQuestionMap))
                    .build())
        .toList();
  }

  private List<QuestionDto.Question> buildQuestions(
      List<QuestionDto.Question> questions, HashMap<String, TestQuestion> testQuestionMap) {
    return questions.stream()
        .map(
            question ->
                QuestionDto.Question.builder()
                    .id(question.id())
                    .uuid(question.uuid())
                    .question(question.question())
                    .marks(question.marks())
                    .chapterSlug(question.chapterSlug())
                    .active(question.active())
                    .bloomsTaxonomyId(question.bloomsTaxonomyId())
                    .category(question.category())
                    .questionTags(question.questionTags())
                    .complexity(question.complexity())
                    .explanation(question.explanation())
                    .msq(
                        testDefinitionService.buildMsqQuestion(
                            question.msq(), testQuestionMap.get(question.uuid()).getMsqAnswer()))
                    .mcq(
                        testDefinitionService.buildMcqQuestion(
                            question.mcq(), testQuestionMap.get(question.uuid()).getMcqAnswer()))
                    .nat(
                        testDefinitionService.buildNatQuestion(
                            question.nat(), testQuestionMap.get(question.uuid()).getNatAnswer()))
                    .fbq(
                        testDefinitionService.buildFbqQuestion(
                            question.fbq(), testQuestionMap.get(question.uuid()).getFbqAnswer()))
                    .pbq(
                        testDefinitionService.buildPbqQuestion(
                            question.pbq(),
                            null,
                            testQuestionMap.get(question.uuid()).getPbqAnswers()))
                    .yesNo(
                        testDefinitionService.buildYesNoQuestion(
                            question.yesNo(), testQuestionMap.get(question.uuid()).getYesNo()))
                    .spch(
                        testDefinitionService.buildSpchQuestion(
                            question.spch(), testQuestionMap.get(question.uuid()).getSpchAnswer()))
                    .amcq(
                        testDefinitionService.buildAmcqQuestion(
                            question.amcq(), testQuestionMap.get(question.uuid()).getAmcqAnswer()))
                    .negativeMarks(question.negativeMarks())
                    .organization(question.organization())
                    .organization(question.organizationSlug())
                    .type(question.type())
                    .subtopicSlug(question.subtopicSlug())
                    .chapterSlug(question.chapterSlug())
                    .published(question.published())
                    .build())
        .toList();
  }

  private List<QuestionDetails> getQuestionDetails(ScheduleTest scheduleTest) {
    return scheduleTestRepository.getQuestionDetailsByScheduleTestId(
        testDefinitionService.getChildTestSchedules(scheduleTest));
  }

  public QuestionDto.QuestionsResponse getInstructions(String studentId, String uuid) {
    ScheduleTestStudent scheduleTestStudent = getScheduleTestStudentByUuid(studentId, uuid);
    var scheduleTest = scheduleTestStudent.getScheduleTest();
    return QuestionDto.QuestionsResponse.builder()
        .Instructions(scheduleTest.getTestDefinition().getInstructions())
        .build();
  }

  public Map<Long, QuestionDto.TestSectionDetails> getTestScheduleSummary(long scheduleTestId) {
    var scheduleTest = scheduleTestRepository.findById(scheduleTestId).orElseThrow();
    var studentUsers =
        scheduleTest.getScheduleTestStudent().stream()
            .map(ScheduleTestStudent::getStudent)
            .toList();
    List<Student> students = studentRepository.findByUserInfoInAndDeletedAtIsNull(studentUsers);
    return getTestScheduleSummaryByStudents(scheduleTest, students);
  }

  private Map<Long, QuestionDto.TestSectionDetails> getTestScheduleSummaryByStudents(
      ScheduleTest scheduleTest, List<Student> students) {
    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(scheduleTest.getTestDefinition(), 1);
    List<Exam> exams = examRepository.findByScheduleTestAndStudentIn(scheduleTest, students);

    Map<Long, QuestionDto.TestSectionDetails> studentsMarks = new HashMap<>();

    exams.forEach(
        exam -> getStudentMarksSummary(exam, questionResponse, scheduleTest, studentsMarks));
    return studentsMarks;
  }

  private void getStudentMarksSummary(
      Exam exam,
      QuestionDto.QuestionResponse questionResponse,
      ScheduleTest scheduleTest,
      Map<Long, QuestionDto.TestSectionDetails> studentsMarks) {
    var testDefinitionSections =
        buildTestDefinitionSectionResult(questionResponse, exam, scheduleTest.getTestDefinition());

    var sectionWiseMarks =
        testDefinitionSections.stream()
            .map(
                tds ->
                    QuestionDto.SectionWiseMarks.builder()
                        .sectionName(tds.name())
                        .totalMarks(getTotalMarks(tds))
                        .securedMarks(getSecuredMarks(tds))
                        .attempted(getAttempted(tds, true))
                        .notAttempted(getAttempted(tds, false))
                        .build())
            .toList();

    var sectionMarks =
        QuestionDto.TestSectionDetails.builder()
            .studentName(exam.getStudentName())
            .testName(scheduleTest.getTestDefinition().getTestName())
            .scheduleDate(scheduleTest.getStartDate().toString())
            .sectionWiseMarks(sectionWiseMarks)
            .build();
    studentsMarks.put(exam.getStudent().getId(), sectionMarks);
  }

  private Long getAttempted(
      QuestionDto.TestDefinitionSection testDefinitionSection, Boolean isAttempted) {
    return testDefinitionSection.questionResults().stream()
        .filter(tds -> tds.isAttempted().equals(isAttempted))
        .count();
  }

  private Double getSecuredMarks(QuestionDto.TestDefinitionSection testDefinitionSection) {
    return testDefinitionSection.questionResults().stream()
        .mapToDouble(QuestionDto.QuestionResult::marksSecured)
        .sum();
  }

  private Double getTotalMarks(QuestionDto.TestDefinitionSection testDefinitionSection) {
    return testDefinitionSection.questionResults().stream()
        .mapToDouble(QuestionDto.QuestionResult::marks)
        .sum();
  }

  public QuestionDto.TestSectionDetails getStudentResult(String resultKey) {
    var keys = Arrays.asList(resultKey.split("-"));
    if (keys.size() != 2 || !StringUtils.isNumeric(keys.getFirst())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid result Key");
    }
    var optionalTss =
        scheduleTestStudentRepository.fetchByScheduleTestAndPrefixUuid(
            Long.parseLong(keys.getFirst()), keys.get(1));

    if (optionalTss.isEmpty()) {
      log.error("Unable to find student results [ %s ]".formatted(resultKey));
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "could not find student result");
    }
    return getStudentResultByTss(optionalTss.get());
  }

  private QuestionDto.TestSectionDetails getStudentResultByTss(ScheduleTestStudent tss) {
    var student =
        studentRepository
            .findByUserInfo(tss.getStudent())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound"));

    Map<Long, QuestionDto.TestSectionDetails> results =
        getTestScheduleSummaryByStudents(tss.getScheduleTest(), List.of(student));
    return results.get(student.getId());
  }

  public void sendStudentResultsNotification(long scheduleId, String teacherId) {
    ScheduleTest scheduledTest =
        scheduleTestRepository
            .findById(scheduleId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidTestSchedule"));

    List<ScheduleTestStudent> scheduleTestStudents =
        getTestCompletedStudentsByScheduleTest(scheduledTest);
    if (scheduleTestStudents.isEmpty()) {
      return;
    }
    validateBeforeSendResults(scheduledTest);
    var completedTss =
        scheduledTest.getScheduleTestStudent().stream()
            .filter(tss -> TestStudentStatus.COMPLETED.name().equals(tss.getStatus()))
            .toList();
    var studentUsers = completedTss.stream().map(ScheduleTestStudent::getStudent).toList();
    var students = studentRepository.findByUserInfoInAndDeletedAtIsNull(studentUsers);
    Map<Long, String> resultKey = new HashMap<>();
    completedTss.forEach(
        tss -> resultKey.put(tss.getStudent().getStudentInfo().getId(), buildResultKey(tss)));
    var notificationRequest =
        buildNotificationRequest(students, scheduledTest.getTestDefinition().getTestName());
    notificationService.createNotificationByTeacher(
        scheduledTest.getOrgSlug(), notificationRequest, teacherId, false);
    var org = organizationRepository.findBySlug(scheduledTest.getOrgSlug());
    if (Boolean.TRUE.equals(org.getSendSms())) {
      msg91SmsService.sendBulkMessage(
          "652cc33cd6fc0516b616b663", buildStudentRecipients(students, resultKey));
      scheduledTest.setNotificationStatus(true);
    }
    scheduleTestRepository.save(scheduledTest);
  }

  private void validateBeforeSendResults(ScheduleTest scheduledTest) {

    if (Objects.nonNull(scheduledTest.getNotificationStatus())
        && scheduledTest.getNotificationStatus()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Notification sent to registered mobile number");
    }
  }

  private List<ScheduleTestStudent> getTestCompletedStudentsByScheduleTest(
      ScheduleTest scheduledTest) {
    return scheduledTest.getScheduleTestStudent().stream()
        .filter(tss -> TestStudentStatus.COMPLETED.name().equals(tss.getStatus()))
        .toList();
  }

  private NotificationDto.NotificationRequest buildNotificationRequest(
      List<Student> students, String testName) {
    return NotificationDto.NotificationRequest.builder()
        .notificationType(INDIVIDUAL)
        .message(
            "Your " + testName + " Exam has been Completed ,You can check results in My Activity")
        .title("Exam Results")
        .studentIds(students.stream().map(Student::getId).toList())
        .build();
  }

  private String buildResultKey(ScheduleTestStudent tss) {

    var scheduleTestId = tss.getScheduleTest().getId();
    List<String> list = Arrays.asList(tss.getUuid().split("-"));
    String prefixUuid = "";
    if (!list.isEmpty() && list.size() > 1) {
      prefixUuid = list.getFirst();
    }
    return String.format(
        "%s/%s-%s", "%s/%s".formatted(appDomainUrl, "s"), scheduleTestId, prefixUuid);
  }

  public List<Msg91Dto.Recipient> buildStudentRecipients(
      List<Student> students, Map<Long, String> resultKey) {
    var validStudents = students.stream().filter(x -> !x.getGuardians().isEmpty()).toList();
    var recipientList =
        validStudents.stream()
            .filter(student -> Objects.nonNull(student.getUserInfo()))
            .map(student -> buildRecipient(student, resultKey.get(student.getId())))
            .toList();
    return recipientList.stream().filter(recipient -> recipient.mobiles() != null).toList();
  }

  private Msg91Dto.Recipient buildRecipient(Student student, String resultLink) {

    var primaryGuardian = eventNotificationService.getPrimaryGuardian(student);
    var organization = organizationRepository.findBySlug(student.getUserInfo().getOrganization());
    String orgName = null;
    if (organization.getName().length() <= 7) {
      orgName = organization.getName();
    }
    return Msg91Dto.Recipient.builder()
        .mobiles(primaryGuardian.getMobileNumber())
        .name(handleNameChars(userService.getNameByUserInfo(student.getUserInfo())))
        .orgname(Objects.nonNull(orgName) ? orgName : "WeXL")
        .link(resultLink)
        .build();
  }

  private String handleNameChars(String name) {
    if (name.length() > 10) {
      return "%s%s".formatted(name.substring(0, 8), "..");
    }
    return name;
  }

  public byte[] getMockTestReportCard(String studentAuthId, Long testScheduleId) {
    Student studentData = studentService.getStudentByAuthId(studentAuthId);
    var exam =
        examRepository
            .getExamDetails(studentData.getId(), testScheduleId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam not found"));
    return getBetReportCard(exam);
  }

  public byte[] getBetReportCard(Exam exam) {
    var student = exam.getStudent();
    var user = student.getUserInfo();
    var betReportCardPath = constructBetReportCardPath(user.getOrganization(), exam.getRef());
    if (storageService.isFileAvailableInBucket(betReportCardPath, WEXL_STRAPI_IMAGES)) {
      return fetchStoredReportCard(betReportCardPath, WEXL_STRAPI_IMAGES);
    } else if (storageService.isFileAvailable(betReportCardPath)) {
      return fetchStoredReportCard(betReportCardPath, s3BucketName);
    }
    var reportCardTemplate = getBetReportCardTemplateByGrade(student.getSection().getGradeSlug());
    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(exam.getScheduleTest().getId())
            .studentAuthId(user.getAuthUserId())
            .isAdmissionTest(
                !Objects.isNull(exam.getScheduleTest())
                    && isAdmissionTest(exam.getScheduleTest().getId()))
            .build();
    var reportCard =
        offlineTestReportService.getStudentReportByOfflineTestDefinition(
            user.getOrganization(), reportCardTemplate.getId(), request);
    storeBetReportCard(exam, reportCard);
    return reportCard;
  }

  private Boolean isAdmissionTest(Long scheduleTestId) {
    var admissionTest = scheduleTestRepository.getAdmissionTestStudentInfo(scheduleTestId);
    return admissionTest.isPresent();
  }

  private ReportCardTemplate getBetReportCardTemplateByGrade(String gradeSlug) {
    String templateConfig =
        Map.of("stdg", "inter-bet-report-card.xml").getOrDefault(gradeSlug, "bet-report-card.xml");

    return reportCardTemplateRepository
        .findByReportCardTemplateTypeAndConfig(ReportCardTemplateType.CUSTOM, templateConfig)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Report card template not found"));
  }

  public byte[] fetchStoredReportCard(String reportCardPath, String bucketName) {
    try {
      var file = storageService.downloadFile(reportCardPath, bucketName);
      return FileUtils.readFileToByteArray(file);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Failed to read stored report card", e);
    }
  }

  private void storeBetReportCard(Exam exam, byte[] reportCard) {
    try {
      var student = exam.getStudent();
      var betReportCardPath =
          constructBetReportCardPath(student.getUserInfo().getOrganization(), exam.getRef());
      storageService.uploadFile(
          WEXL_STRAPI_IMAGES, MediaType.APPLICATION_PDF_VALUE, betReportCardPath, reportCard, true);
    } catch (Exception e) {
      log.error("Failed to save report card:{}", e.getMessage(), e);
    }
  }

  private String constructBetReportCardPath(String orgSlug, String ref) {
    return String.format("%s/bet-report-card/%s", orgSlug, ref);
  }

  public TestScheduleStudentAnswerDto.TestDetails getAllBetTestSchedules(
      String orgSlug, String studentAuthId) {
    Student studentData = studentService.getStudentByAuthId(studentAuthId);
    var betTests =
        scheduleTestRepository.getStudentBetTests(
            orgSlug,
            studentData.getUserInfo().getId(),
            TestCategory.BET,
            String.valueOf(TestStudentStatus.COMPLETED));
    List<TestScheduleStudentAnswerDto.BetTestDetails> testDetailsList = new ArrayList<>();
    for (var test : betTests) {
      testDetailsList.add(
          TestScheduleStudentAnswerDto.BetTestDetails.builder()
              .testName(test.getTestDefinition().getTestName())
              .testScheduleId(test.getId())
              .build());
    }
    return TestScheduleStudentAnswerDto.TestDetails.builder()
        .betTestDetails(testDetailsList)
        .build();
  }

  public byte[] getParticipationCertificate(
      String orgSlug, String studentAuthId, Long testScheduleId) {

    var reportCardTemplate =
        reportCardTemplateRepository.findAllReportCardTemplateTypeName(
            "CUSTOM", "ParticipationCertificate", orgSlug);

    if (reportCardTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report Card template not found");
    }
    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(testScheduleId)
            .studentAuthId(studentAuthId)
            .build();
    return offlineTestReportService.getStudentReportByOfflineTestDefinition(
        orgSlug, reportCardTemplate.get(0).getId(), request);
  }

  public byte[] getStudentParticipationCertificate(
      String orgSlug, String studentAuthId, Long examId) {
    var exam = validationUtils.findByExamId(examId);
    return getParticipationCertificate(orgSlug, studentAuthId, exam.getScheduleTest().getId());
  }

  public byte[] getOverallParticipationCertificate(String orgSlug, Long testScheduleId) {
    var scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ""));
    var scheduleTestStudent = scheduleTestStudentRepository.findByScheduleTest(scheduleTest);
    List<ScheduleTestStudent> studentList =
        scheduleTestStudent.stream()
            .filter(student -> TaskStatus.COMPLETED.name().equals(student.getStatus()))
            .collect(Collectors.toList());
    List<String> studentAuthIds =
        studentList.stream()
            .map(student -> student.getStudent().getAuthUserId())
            .collect(Collectors.toList());
    List<byte[]> response = new ArrayList<>();
    for (String authId : studentAuthIds) {
      byte[] report = getParticipationCertificate(orgSlug, authId, testScheduleId);
      response.add(report);
    }
    byte[] data = reportCardTemplateService.mergeReportCards(response);
    return data;
  }

  public List<AnswerSheetTemplatesDto.Response> getAllAnswerSheetTemplates() {

    List<ReportCardTemplate> templates =
        reportCardTemplateRepository.findByReportCardTemplateTypeAndOrgSlug(
            ReportCardTemplateType.ANSWER_SHEET_TEMPLATE, "wexl-internal");
    return templates.stream()
        .map(
            data ->
                AnswerSheetTemplatesDto.Response.builder()
                    .templateId(data.getId())
                    .templateName(data.getName())
                    .build())
        .collect(Collectors.toList());
  }

  public byte[] getAnswerSheetTemplates(String orgSlug, String studentAuthId, Long testScheduleId) {
    Optional<ScheduleTest> testSchedule = scheduleTestRepository.findById(testScheduleId);
    if (testSchedule.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Test schedule not found for the given ID: " + testScheduleId);
    }
    TestDefinition testDefinition = testSchedule.get().getTestDefinition();
    Optional<TestDefinition> testDefinitionOptional =
        testDefinitionRepository.findById(testDefinition.getId());
    TestDefinition testDefinitionData = testDefinitionOptional.get();
    if (testDefinitionData.getReportCardTemplateId() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Please Set TemplateId");
    }
    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(testScheduleId)
            .studentAuthId(studentAuthId)
            .withMarks(false)
            .build();
    return offlineTestReportService.getStudentReportByOfflineTestDefinition(
        orgSlug, testDefinitionData.getReportCardTemplateId(), request);
  }

  public byte[] getOverallAnswerSheetTemplates(String orgSlug, Long testScheduleId) {
    var scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ""));
    var scheduleTestStudent = scheduleTestStudentRepository.findByScheduleTest(scheduleTest);
    List<String> studentAuthIds =
        scheduleTestStudent.stream()
            .map(student -> student.getStudent().getAuthUserId())
            .collect(Collectors.toList());
    List<byte[]> response = new ArrayList<>();
    for (String studentAuthId : studentAuthIds) {
      byte[] report = getAnswerSheetTemplates(orgSlug, studentAuthId, testScheduleId);
      response.add(report);
    }
    byte[] data = reportCardTemplateService.mergeReportCards(response);
    return data;
  }

  public ExamResultDto.Response generateStudentsScheduleTestResult(
      long scheduleId, boolean regenerate) {
    var scheduleTest = validationUtils.isTestScheduleValid(scheduleId);
    if (!regenerate && !StringUtils.isEmpty(scheduleTest.getStudentsResultPath())) {
      return ExamResultDto.Response.builder()
          .url(storageService.generatePreSignedUrlForFetch(scheduleTest.getStudentsResultPath()))
          .build();
    }
    var studentExams = examRepository.findAllByScheduleTestIn(List.of(scheduleTest));
    List<StudentResultDto.StudentReportResponse> studentReportResponses =
        studentExams.stream()
            .map(
                exam -> {
                  var tss = validateTssByExam(exam);
                  return constructStudentExamResult(exam, tss);
                })
            .sorted(
                Comparator.comparing(
                    s -> Double.parseDouble(s.totalAiAnalysisMarks()), Comparator.reverseOrder()))
            .toList();
    final String reportHtml = generateStudentReport(studentReportResponses);
    final var studentResultPath = constructPath(scheduleTest.getOrgSlug(), scheduleTest.getId());
    storageService.uploadFile(
        reportHtml.getBytes(StandardCharsets.UTF_8), studentResultPath, MediaType.TEXT_HTML_VALUE);
    scheduleTest.setStudentsResultPath(studentResultPath);
    scheduleTestRepository.save(scheduleTest);
    return ExamResultDto.Response.builder()
        .url(storageService.generatePreSignedUrlForFetch(studentResultPath))
        .build();
  }

  public String generateStudentReport(
      List<StudentResultDto.StudentReportResponse> reportResponses) {
    if (reportResponses.isEmpty()) {
      return "";
    }
    var isMathOrScienceSubject =
        Stream.of("mathematics", "science")
            .anyMatch(
                subject ->
                    StringUtils.startsWithIgnoreCase(
                        subject, reportResponses.getFirst().subject()));
    Context context = new Context();
    context.setVariable("includeMathJax", isMathOrScienceSubject);
    context.setVariable("students", reportResponses);
    return templateEngine.process("students-exam-report", context);
  }

  private StudentResultDto.StudentReportResponse constructStudentExamResult(
      Exam exam, ScheduleTestStudent tss) {
    var testDefinitionResponse =
        testDefinitionService.getQuestionsPreconfigured(
            exam.getTestDefinition(), getQuestionResponseSet(tss));
    var questions = testDefinitionResponse.getQuestionsAndOptions();
    final List<ExamAnswer> examAnswers = exam.getExamAnswers();
    var examAnwerMap =
        examAnswers.stream().collect(Collectors.groupingBy(ExamAnswer::getQuestionUuid));
    var studentAnalysisReport = getStudentAnalysisReports(exam);
    var reportResponseMap =
        studentAnalysisReport.data().stream()
            .collect(
                Collectors.toMap(
                    StudentResultDto.StudentAnalysisReportResponse::questionUuid,
                    Function.identity(),
                    (existing, replacement) -> existing));
    List<StudentResultDto.StudentAnswerResult> studentAnswerResults =
        IntStream.range(0, questions.size())
            .mapToObj(
                i -> {
                  var question = questions.get(i);
                  var examAnswersFromMap = examAnwerMap.get(question.getUuid());
                  if (examAnswersFromMap == null || examAnswersFromMap.isEmpty()) {
                    log.info("Working on exam: {}", exam.getId());
                    log.info(
                        "Cannot find question with uuid in examAnswers: {}", question.getUuid());
                    log.info("Exam AnswersMap is empty for this question {}", question.getUuid());
                    // hacky solution to handle the case where the question is not found in
                    // examAnswers
                    var resultBuilder =
                        StudentResultDto.StudentAnswerResult.builder()
                            .questionNumber(String.valueOf(i + 1))
                            .aiAnalysisPerformed("")
                            .marksAttained("0");
                    return resultBuilder.build();
                  }
                  var examAnswer = examAnswersFromMap.getFirst();
                  var resultBuilder =
                      StudentResultDto.StudentAnswerResult.builder()
                          .questionNumber(String.valueOf(i + 1))
                          .aiAnalysisPerformed(getStudentAnswerByType(examAnswer))
                          .totalMarksEachQuestion(String.valueOf(examAnswer.getMarksPerQuestion()))
                          .marksAttained(String.valueOf(examAnswer.getMarksScoredPerQuestion()));
                  if (QuestionType.SUBJECTIVE.name().equalsIgnoreCase(examAnswer.getType())) {
                    if (!exam.isCorrected()) {
                      var analysisReportResponse =
                          reportResponseMap.get(examAnswer.getQuestionUuid());
                      if (Objects.nonNull(analysisReportResponse)) {
                        resultBuilder
                            .totalMarksEachQuestion(
                                String.valueOf(examAnswer.getMarksPerQuestion()))
                            .marksAttained(String.valueOf(analysisReportResponse.marks()))
                            .aiAnalysisPerformed(analysisReportResponse.answer());
                      }
                    } else {
                      resultBuilder
                          .totalMarksEachQuestion(String.valueOf(examAnswer.getMarksPerQuestion()))
                          .marksAttained(String.valueOf(examAnswer.getAiMarks()))
                          .aiAnalysisPerformed(
                              Objects.isNull(examAnswer.getAiAnalysis())
                                  ? NOT_ANSWERED
                                  : examAnswer.getAiAnalysis());
                    }
                  }
                  return resultBuilder.build();
                })
            .toList();

    var student = exam.getStudent();
    return StudentResultDto.StudentReportResponse.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .grade(student.getSection().getGradeName())
        .testName(exam.getTestDefinition().getTestName())
        .admissionNumber(student.getRollNumber())
        .subject(exam.getSubjectName())
        .totalNumberOfQuestions(String.valueOf(exam.getNoOfQuestions()))
        .totalMarks(
            String.valueOf(
                studentAnswerResults.stream()
                    .mapToDouble(
                        result -> {
                          String totalMarks = result.totalMarksEachQuestion();
                          return totalMarks != null ? Double.parseDouble(totalMarks) : 0.0;
                        })
                    .sum()))
        .totalAiAnalysisMarks(
            String.valueOf(
                studentAnswerResults.stream()
                    .mapToDouble(
                        result -> {
                          String marks = result.marksAttained();
                          return marks != null ? Double.parseDouble(marks) : 0.0;
                        })
                    .sum()))
        .answers(studentAnswerResults)
        .build();
  }

  private String getStudentAnswerByType(ExamAnswer examAnswer) {
    if (QuestionType.MCQ.name().equalsIgnoreCase(examAnswer.getType())) {
      return Objects.isNull(examAnswer.getSelectedOption()) || examAnswer.getSelectedOption() == 0
          ? "NOT ATTEMPTED"
          : String.format(
              "Selected option: %s",
              testDefinitionService.convertMcqAnswer(
                  String.valueOf(examAnswer.getSelectedOption())));
    }
    return null;
  }

  public ExamResultDto.Response generateStudentTestResult(Exam exam) {
    try {
      var tss = validateTssByExam(exam);
      if (Objects.nonNull(tss.getResultPath())) {
        return ExamResultDto.Response.builder()
            .url(storageService.generatePreSignedUrlForFetch(tss.getResultPath()))
            .build();
      }
      var studentResponse = constructStudentExamResult(exam, tss);
      var report = generateStudentReport(List.of(studentResponse));
      final var studentResultPath =
          constructPath(exam.getScheduleTest().getOrgSlug(), exam.getId());
      storageService.uploadFile(
          report.getBytes(StandardCharsets.UTF_8), studentResultPath, MediaType.TEXT_HTML_VALUE);

      if (exam.isCorrected()) {
        tss.setResultPath(studentResultPath);
        tss.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
        scheduleTestStudentRepository.save(tss);
      }
      return ExamResultDto.Response.builder()
          .url(storageService.generatePreSignedUrlForFetch(studentResultPath))
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private ScheduleTestStudent validateTssByExam(Exam exam) {
    return scheduleTestStudentRepository
        .findByScheduleTestAndStudent(exam.getScheduleTest(), exam.getStudent().getUserInfo())
        .orElseThrow(
            () ->
                new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestStudentNotFound"));
  }

  private String constructPath(String orgSlug, Long id) {
    return String.format("%s/student-result/%s.html", orgSlug, id);
  }

  public StudentResultDto.StudentAnalysisReportResponseList getStudentAnalysisReports(Exam exam) {
    StudentResultDto.StudentAnalysisReportResponseList reportResponseList =
        StudentResultDto.StudentAnalysisReportResponseList.builder()
            .data(new ArrayList<>())
            .build();
    try {
      if (exam.isCorrected()) {
        return reportResponseList;
      }
      String analysisUrl =
          UriComponentsBuilder.fromUriString(correctionServiceBaseUrl)
              .pathSegment(
                  "orgs",
                  exam.getScheduleTest().getOrgSlug(),
                  "test-schedules",
                  String.valueOf(exam.getScheduleTest().getId()),
                  "students",
                  String.valueOf(exam.getStudent().getId()))
              .build()
              .toUriString();
      HttpHeaders headers = new HttpHeaders();
      headers.setBearerAuth(tokenGenerator.generateAdminToken());
      var entity = new HttpEntity<>(headers);
      var response =
          restTemplate.exchange(
              analysisUrl,
              HttpMethod.GET,
              entity,
              StudentResultDto.StudentAnalysisReportResponseList.class);
      if (response.getStatusCode().is2xxSuccessful() && Objects.nonNull(response.getBody())) {
        return response.getBody();
      }
      return reportResponseList;
    } catch (Exception e) {
      log.error(
          "Failed to get student Analysis Reports from correction service :{}", e.getMessage(), e);
      return reportResponseList;
    }
  }

  @Transactional
  public void deleteExam(long scheduleId, String studentAuthUserId) {
    ScheduleTest scheduledTest =
        scheduleTestRepository
            .findById(scheduleId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.TestSchedule",
                        new String[] {studentAuthUserId}));
    Student student = studentService.getStudentByAuthId(studentAuthUserId);
    var exams =
        examRepository.findByScheduleTestAndStudentIn(
            scheduledTest, Collections.singletonList(student));
    if (exams.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ExamNotFound");
    }
    final Exam exam = exams.getFirst();
    examRevisionRepository.deleteByExam(exam);
    examRepository.delete(exam);
    // find the ScheduleTestStudent and set its status back to "PENDING"
    var scheduleTestStudent =
        scheduleTestStudentRepository
            .findByScheduleTestAndStudent(scheduledTest, student.getUserInfo())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.TestStudentNotFound"));
    scheduleTestStudent.setStatus(TestStudentStatus.PENDING.name());
    scheduleTestStudentRepository.save(scheduleTestStudent);
  }

  public QuestionDto.StudentPersonalizedWorkSheetResponse getStudentEnrichments(long examId) {
    var exam = validationUtils.findByExamId(examId);
    var requiredExamAnswers = evaluateExamPerformance(exam.getExamAnswers());
    var questionUuids = requiredExamAnswers.stream().map(ExamAnswer::getQuestionUuid).toList();
    if (questionUuids.isEmpty()) {
      return QuestionDto.StudentPersonalizedWorkSheetResponse.builder()
          .testEnrichmentResponses(Collections.emptyList())
          .build();
    }
    var testEnrichments = testEnrichmentRepository.findByQuestionUuidIn(questionUuids);
    if (Objects.isNull(testEnrichments) || testEnrichments.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Could not get Personalized worksheet due to incomplete test enrichment");
    }
    var responses =
        testEnrichments.stream().map(this::buildStudentPersonalizedWorkSheetResponse).toList();
    return QuestionDto.StudentPersonalizedWorkSheetResponse.builder()
        .testEnrichmentResponses(responses)
        .build();
  }

  private QuestionDto.TestEnrichmentResponse buildStudentPersonalizedWorkSheetResponse(
      TestEnrichment testEnrichment) {
    return QuestionDto.TestEnrichmentResponse.builder()
        .concept(testEnrichment.getConcept())
        .summary(testEnrichment.getSummary())
        .questionUuid(testEnrichment.getQuestionUuid())
        .testEnrichData(testEnrichment.getContent())
        .references(testEnrichment.getReference())
        .build();
  }

  private List<ExamAnswer> evaluateExamPerformance(List<ExamAnswer> examAnswers) {
    return examAnswers.stream()
        .filter(
            ea ->
                !ea.isAttempted()
                    || !Objects.equals(
                        ea.getMarksScoredPerQuestion(), Float.valueOf(ea.getMarksPerQuestion())))
        .toList();
  }

  public ScheduleTestResponse patchBetScore(ScheduleTestResponse scheduledTest) {
    scheduledTest
        .getAssignedStudents()
        .forEach(
            scheduleTestStudentResponse -> {
              if (scheduleTestStudentResponse
                  .getStudentTestStatus()
                  .equalsIgnoreCase("COMPLETED")) {
                Map<String, Object> stringObjectMap = buildSections(scheduleTestStudentResponse);
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                BetDtoBody body =
                    objectMapper.convertValue(stringObjectMap.get("body"), BetDtoBody.class);
                List<TestDefinitionsDto.SectionsResponse> testDefinitionSections =
                    new ArrayList<>();
                scheduleTestStudentResponse
                    .getTestDefinitionSections()
                    .forEach(
                        sectionsResponse -> {
                          TestDefinitionsDto.SectionsResponse response =
                              switch (sectionsResponse.name().toLowerCase()) {
                                case "listening" ->
                                    new TestDefinitionsDto.SectionsResponse(
                                        sectionsResponse.id(),
                                        sectionsResponse.name(),
                                        sectionsResponse.sequenceNumber(),
                                        sectionsResponse.noOfQuestions(),
                                        sectionsResponse.totalMarks(),
                                        sectionsResponse.marksScored(),
                                        sectionsResponse.ieltsScored(),
                                        body.section1Grade(),
                                        body.section1Value());
                                case "speaking" ->
                                    new TestDefinitionsDto.SectionsResponse(
                                        sectionsResponse.id(),
                                        sectionsResponse.name(),
                                        sectionsResponse.sequenceNumber(),
                                        sectionsResponse.noOfQuestions(),
                                        sectionsResponse.totalMarks(),
                                        sectionsResponse.marksScored(),
                                        sectionsResponse.ieltsScored(),
                                        body.section2Grade(),
                                        body.section2Value());
                                case "reading" ->
                                    new TestDefinitionsDto.SectionsResponse(
                                        sectionsResponse.id(),
                                        sectionsResponse.name(),
                                        sectionsResponse.sequenceNumber(),
                                        sectionsResponse.noOfQuestions(),
                                        sectionsResponse.totalMarks(),
                                        sectionsResponse.marksScored(),
                                        sectionsResponse.ieltsScored(),
                                        body.section3Grade(),
                                        body.section3Value());
                                case "writing" ->
                                    new TestDefinitionsDto.SectionsResponse(
                                        sectionsResponse.id(),
                                        sectionsResponse.name(),
                                        sectionsResponse.sequenceNumber(),
                                        sectionsResponse.noOfQuestions(),
                                        sectionsResponse.totalMarks(),
                                        sectionsResponse.marksScored(),
                                        sectionsResponse.ieltsScored(),
                                        body.section4Grade(),
                                        body.section4Value());
                                default ->
                                    throw new IllegalStateException(
                                        "Unexpected value: "
                                            + sectionsResponse.name().toLowerCase());
                              };
                          testDefinitionSections.add(response);
                        });
                scheduleTestStudentResponse.setTestDefinitionSections(testDefinitionSections);
              }
            });
    return scheduledTest;
  }

  private Map<String, Object> buildSections(
      ScheduleTestStudentResponseImpl scheduledTestStudentResponse) {
    Long scheduleTestId = scheduledTestStudentResponse.getScheduleTestId();
    Long studentId = scheduledTestStudentResponse.getStudentId();
    Student student = studentRepository.findById(studentId).orElseThrow();
    var scheduledTest =
        scheduleTestRepository
            .findById(scheduleTestId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ExamNotFound"));

    var testDefinition = scheduledTest.getTestDefinition();

    ReportCardTemplate reportCardTemplate =
        getBetReportCardTemplateByGrade(student.getSection().getGradeSlug());

    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(scheduleTestId)
            .studentAuthId(scheduledTestStudentResponse.getUserName())
            .templateId(reportCardTemplate.getId())
            .build();

    return offlineTestReportService.constructReportCardModel(
        reportCardTemplate, testDefinition.getOrganization(), request);
  }
}
