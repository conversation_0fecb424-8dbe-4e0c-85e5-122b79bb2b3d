package com.wexl.retail.schedules.service;

import com.wexl.retail.schedules.dto.AggregatedScheduleResponse;
import com.wexl.retail.schedules.dto.ScheduleResponse;
import java.sql.Timestamp;
import java.util.*;
import org.springframework.beans.BeanUtils;

public class ScheduleResponseAggregator {

  private final Map<String, AggregatedScheduleResponse> indexMap;
  private final List<ScheduleResponse> scheduleResponses;

  public ScheduleResponseAggregator(List<ScheduleResponse> scheduleResponses) {
    indexMap = new HashMap<>(scheduleResponses.size());
    this.scheduleResponses = scheduleResponses;
  }

  public List<AggregatedScheduleResponse> aggregate() {
    scheduleResponses.forEach(
        scheduleResponse -> {
          String key = constructKey(scheduleResponse);
          if (indexMap.containsKey(key)) {
            AggregatedScheduleResponse aggregatedScheduleResponse = indexMap.get(key);
            aggregatedScheduleResponse.getSections().add(scheduleResponse.getSection());
          } else {
            AggregatedScheduleResponse aggregatedScheduleResponse =
                new AggregatedScheduleResponse();
            BeanUtils.copyProperties(scheduleResponse, aggregatedScheduleResponse);
            aggregatedScheduleResponse.setFromTime(new Timestamp(scheduleResponse.getFromTime()));
            aggregatedScheduleResponse.setToTime(new Timestamp(scheduleResponse.getToTime()));
            aggregatedScheduleResponse.getSections().add(scheduleResponse.getSection());
            indexMap.put(key, aggregatedScheduleResponse);
          }
        });

    List<AggregatedScheduleResponse> aggregatedScheduleResponses =
        new ArrayList<>(indexMap.values());
    return aggregatedScheduleResponses.stream()
        .sorted(Comparator.comparing(AggregatedScheduleResponse::getCreatedAt).reversed())
        .toList();
  }

  private String constructKey(ScheduleResponse scheduleResponse) {
    return scheduleResponse.getUrl()
        + scheduleResponse.getFromTime()
        + scheduleResponse.getToTime();
  }
}
