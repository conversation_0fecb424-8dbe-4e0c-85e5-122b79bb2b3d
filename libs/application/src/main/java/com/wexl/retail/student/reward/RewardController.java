package com.wexl.retail.student.reward;

import com.wexl.retail.commons.exceptions.ApiException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/student/reward")
@RequiredArgsConstructor
public class RewardController {

  private final RewardService rewardService;

  @GetMapping
  public List<RewardTransactionResponse> findAll(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "id") String sort,
      @RequestParam(defaultValue = "ASC") Sort.Direction direction) {

    return rewardService.findAll(page, size, Sort.by(direction, sort));
  }

  @PostMapping
  public RewardTransactionResponse redeemReward(
      @RequestBody RewardTransactionRequest rewardTransactionRequest) throws ApiException {

    return rewardService.redeemReward(rewardTransactionRequest);
  }

  @GetMapping("/{reward_id}")
  public RewardTransaction findById(@PathVariable("reward_id") long rewardId) {

    return rewardService.findById(rewardId);
  }
}
