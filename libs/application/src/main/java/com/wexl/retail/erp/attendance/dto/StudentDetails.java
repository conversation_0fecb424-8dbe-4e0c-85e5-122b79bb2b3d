package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentDetails {
  @JsonProperty("attendance_status")
  private String attendanceStatus;

  @JsonProperty("section_attendance_details_id")
  private Long sectionAttendanceDetailsId;

  @JsonProperty("student_id")
  private Long student;

  @JsonProperty("entry_time")
  private Long entryTime;

  @JsonProperty("exit_time")
  private Long exitTime;

  private String note;
}
