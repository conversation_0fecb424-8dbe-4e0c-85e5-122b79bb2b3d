package com.wexl.retail.section.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TeacherSubjectsResponse {

  private String teacherName;
  private String sectionName;
  private UUID sectionUuid;
  private String subjectSlug;
  private String subjectName;
  private String gradeName;
  private String boardName;
  private String boardSlug;
  private Long teacherSubjectId;

  @JsonProperty("is_classteacher")
  private Boolean isClassTeacher;
}
