package com.wexl.retail.classroom.group.controller;

import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleRequest;
import com.wexl.retail.classroom.group.dto.GroupClassroomDto;
import com.wexl.retail.classroom.group.service.GroupClassroomService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/wexl-internal")
@RequiredArgsConstructor()
public class GroupClassroomController {

  private final GroupClassroomService groupClassroomService;

  @PostMapping("/group-classrooms")
  public void createClassroomByOrganization(
      @RequestBody GroupClassroomDto.GroupClassroomRequest classroomRequest) {
    groupClassroomService.createGroupClassroom(classroomRequest);
  }

  @PostMapping("/group-classrooms/{classroomId}/schedules")
  public void scheduleGroupClassroom(
      @RequestParam(name = "allowConflict", required = false, defaultValue = "false")
          boolean isAllowConflict,
      @PathVariable long classroomId,
      @RequestBody List<ClassroomScheduleRequest> classroomScheduleRequests) {
    groupClassroomService.scheduleGroupClassroom(classroomScheduleRequests, classroomId);
  }

  @GetMapping("/group-classrooms")
  public List<GroupClassroomDto.GroupClassroomsResponse> getGroupClassrooms() {
    return groupClassroomService.getGroupClassrooms();
  }

  @GetMapping("/group-classrooms/{classroomId}")
  public GroupClassroomDto.GroupClassroomDetails getGroupClassroomDetails(
      @PathVariable Long classroomId) {
    return groupClassroomService.getGroupClassroomDetails(classroomId);
  }

  @GetMapping("/group-classrooms/schedule-insts")
  public List<ClassroomScheduleInstResponse> getGroupClassroomSchedules(
      @RequestParam(value = "from_date") Long fromDate,
      @RequestParam(value = "to_date") Long toDate) {
    return groupClassroomService.getGroupClassroomSchedules(fromDate, toDate);
  }

  @DeleteMapping("/group-classrooms/{classroomId}")
  public void deleteGroupClassroomDetails(@PathVariable Long classroomId) {
    groupClassroomService.deleteGroupClassroomDetails(classroomId);
  }

  @PostMapping("/group-classrooms/{classroomId}")
  public void editGroupClassroomDetails(
      @PathVariable Long classroomId,
      @RequestBody GroupClassroomDto.GroupClassroomRequest classroomRequest) {
    groupClassroomService.editGroupClassroom(classroomId, classroomRequest);
  }
}
