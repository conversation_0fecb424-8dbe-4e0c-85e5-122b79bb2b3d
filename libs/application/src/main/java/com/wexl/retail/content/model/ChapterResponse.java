package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChapterResponse {
  private Long id;
  private Boolean premium;

  @JsonProperty("name")
  private String name;

  @JsonProperty("grade")
  private String gradeSlug;

  @JsonProperty("gradeId")
  private Integer gradeId;

  @JsonProperty("subject")
  private String subjectSlug;

  @JsonProperty("subjectId")
  private Integer subjectId;

  @JsonProperty("subjectName")
  private String subjectName;

  @JsonProperty("board")
  private String eduboardSlug;

  @JsonProperty("boardId")
  private Integer eduboardId;

  @JsonProperty("slug")
  private String chapterSlug;

  @JsonProperty("status")
  private ChapterStatus status;

  @JsonProperty("subtopics")
  private List<SubtopicDetails> subtopics;

  @JsonProperty("sub_topic_count")
  private Integer subTopicCount;

  @JsonProperty("asset_count")
  private Integer assetCount;

  @JsonProperty("concept_video_count")
  private Integer conceptVideoCount;

  @JsonProperty("question_count")
  private Integer questionCount;
}
