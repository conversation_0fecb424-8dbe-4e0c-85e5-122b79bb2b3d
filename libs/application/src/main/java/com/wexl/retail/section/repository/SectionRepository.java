package com.wexl.retail.section.repository;

import com.wexl.retail.metrics.dto.StudentsStatusResponse;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.dto.response.SectionResponseQueryResult;
import com.wexl.retail.section.dto.response.SectionStudentResponse;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SectionRepository extends JpaRepository<Section, Long> {

  Set<Section> findAllByOrganizationAndDeletedAtIsNullOrderByName(String organization);

  Set<Section> findAllByOrganizationAndDeletedAtIsNullAndStatusOrderByName(
      String organization, SectionStatus status);

  @Query(
      value =
          "select * from sections sec where sec.organization = :organization and sec.deleted_at is null and status='ACTIVE' order by name",
      nativeQuery = true)
  List<Section> getSectionsByOrganizationAndDeletedAtIsNullOrderByName(String organization);

  List<Section> findAllByOrganizationAndGradeSlugAndStatusAndDeletedAtIsNull(
      String organization, String gradeSlug, SectionStatus status);

  @Query(
      value =
          """
                  select count(s.*) from sections sec join students s on sec.id = s.section_id
                  join users u on s.user_id = u.id
                  where sec.id = ?1 and u.deleted_at is null""",
      nativeQuery = true)
  Long getStudentCountOfSection(Long sectionId);

  @Query(
      value =
          """
                       select sec.id as sectionId , count(s.*) as studentCount from sections sec
                       join students s on sec.id = s.section_id where sec.id in :sectionIds and s.deleted_at is null group by sec.id""",
      nativeQuery = true)
  List<SectionStudentResponse> getStudentCountsForSections(List<Long> sectionIds);

  Set<Section> getSectionsByOrganizationAndGradeId(String organization, Integer gradeId);

  List<Section> findTop1ByOrganizationAndName(String organization, String name);

  Optional<Section> findByUuid(UUID uuid);

  @Query(
      value =
          """
          select s from Section s
          where s.status = 'ACTIVE' and s.organization=:orgSlug and s.gradeId=:gradeId
          order by s.name asc
          """)
  List<Section> getSectionsOfGrade(String orgSlug, Integer gradeId);

  @Query(
      value =
          """
                          SELECT
                              sec.grade_slug as gradeSlug,
                              sec.grade_name as gradeName,
                              COUNT(DISTINCT s.user_id) AS totalUsersCount,
                              COUNT(DISTINCT CASE WHEN u.deleted_at IS NULL THEN s.user_id END) AS activeUsersCount,
                              COUNT(DISTINCT CASE WHEN u.deleted_at IS NOT NULL THEN s.user_id END) AS inactiveUsersCount
                          FROM students s
                          JOIN users u ON u.id = s.user_id
                          JOIN sections sec ON s.section_id = sec.id
                          WHERE u.organization = :orgSlug
                          GROUP BY sec.grade_slug, sec.grade_name
                          """,
      nativeQuery = true)
  List<StudentsStatusResponse> getAllGradeStudentsOfOrganization(String orgSlug);

  @Query(value = "select s from Section s where s.uuid in :uuidList")
  List<Section> findStudentsInSectionList(List<UUID> uuidList);

  @Query(value = "select s.id from sections s where s.uuid in (:sectionUuids)", nativeQuery = true)
  List<Long> getSectionIdsByUuids(List<UUID> sectionUuids);

  @Query(
      value = "select * from sections where grade_slug in (:grades) and organization = :orgSlug",
      nativeQuery = true)
  List<Section> getSectionsUsingGradeSlugs(List<String> grades, String orgSlug);

  @Query(
      value =
          "select * from sections where organization = :orgSlug and teacher_id is null order by name",
      nativeQuery = true)
  List<Section> getSectionsUsingOrgSlugAndTeacherIsNull(String orgSlug);

  @Query(
      value =
          "select * from sections where grade_slug in (:grades) and organization = :orgSlug and board_slug = :boardSlug",
      nativeQuery = true)
  List<Section> getSectionsUsingGradeSlugsAndBoardSlugs(
      List<String> grades, String orgSlug, String boardSlug);

  List<Section> findAllByGradeSlugInAndOrganizationAndStatus(
      List<String> gradeSlug, String organization, SectionStatus status);

  @Query(
      value =
          """
          select s.organization as organizationSlug, o.name  as organizationName, s.name as sectionName,
                   s.id as sectionId ,cast(s.uuid as varchar) as sectionUuid from sections s
                   inner join orgs o on s.organization = o.slug
                   where s.grade_slug in (:gradeSlug) and s.organization in (:childOrgs) order by o.name""",
      nativeQuery = true)
  List<SectionResponseQueryResult> getSectionsOfChildOrgsByGrade(
      String gradeSlug, List<String> childOrgs);

  @Query(
      value =
          """
                  select s.organization as organizationSlug, o.name  as organizationName, s.name as sectionName,
                           s.id as sectionId ,cast(s.uuid as varchar) as sectionUuid from sections s
                           inner join orgs o on s.organization = o.slug
                           where  s.organization in (:childOrgs) and s.deleted_at is NULL order by o.name""",
      nativeQuery = true)
  List<SectionResponseQueryResult> getSectionsOfChildOrgs(List<String> childOrgs);

  List<Section> findAllByOrganizationAndIdIn(String organization, List<Long> sectionIds);

  @Query(
      value = "select * from sections where id = -1 and organization = 'wexl-internal'",
      nativeQuery = true)
  Section getDefaultSection();

  List<Section> findAllByIdIn(List<Long> sectionIds);

  List<Section> findAllByUuidIn(List<UUID> uuids);

  List<Section> findAllByBoardSlugIsNull();

  List<Section> findAllByOrganizationAndBoardSlugAndGradeSlugAndStatusOrderByName(
      String org, String board, String grade, SectionStatus status);

  List<Section> findAllByOrganizationAndBoardSlugInAndGradeSlugIn(
      String orgSlug, List<String> boardSlugs, List<String> gradeSlugs);

  Section findAllByUuid(UUID sectionUuid);

  Optional<Section> findByOrganizationAndUuid(String orgSlug, UUID sectionUuid);

  Optional<Section> findByClassTeacher(Teacher teacher);

  Optional<Section> findByNameAndOrganization(String sectionName, String orgSlug);
}
