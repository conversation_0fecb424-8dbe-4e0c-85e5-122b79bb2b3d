package com.wexl.retail.teacher.teacherpublisher;

import com.wexl.retail.model.Teacher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class TeacherCreationEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void teacherCreationEvent(final Teacher teacher) {
    TeacherCreationEvent teacherCreationEvent = new TeacherCreationEvent(teacher);
    applicationEventPublisher.publishEvent(teacherCreationEvent);
  }
}
