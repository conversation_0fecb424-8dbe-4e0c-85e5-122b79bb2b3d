package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AttendanceSearchUserResponse {

  @JsonProperty("section_id")
  private Long sectionId;

  @JsonProperty("attendance_id")
  private Long attendanceId;

  @JsonProperty("name_of_student")
  private String nameOfStudent;

  @JsonProperty("grade")
  private String grade;

  @JsonProperty("section_name")
  private String sectionName;

  @JsonProperty("auth_user_id")
  private String authUserId;

  @JsonProperty("user_id")
  private Long userId;

  @JsonProperty("status")
  private CompletionStatus status;

  @JsonProperty("date_id")
  private Long dateId;

  @JsonProperty("entry_time")
  private Long entryTime;

  @JsonProperty("exit_time")
  private Long exitTime;

  @JsonProperty("note")
  private String note;
}
