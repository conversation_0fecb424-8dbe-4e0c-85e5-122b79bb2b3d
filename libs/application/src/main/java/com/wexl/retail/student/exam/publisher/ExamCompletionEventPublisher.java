package com.wexl.retail.student.exam.publisher;

import com.wexl.retail.student.exam.Exam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class ExamCompletionEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishExamCompletion(final Exam exam) {
    ExamCompletionEvent examCompletionEvent = new ExamCompletionEvent(exam);
    applicationEventPublisher.publishEvent(examCompletionEvent);
  }
}
