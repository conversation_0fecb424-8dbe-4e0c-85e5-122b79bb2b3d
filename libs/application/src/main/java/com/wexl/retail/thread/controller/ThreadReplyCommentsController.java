package com.wexl.retail.thread.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.service.ThreadReplyCommentsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class ThreadReplyCommentsController {

  private final ThreadReplyCommentsService threadReplyCommentsService;

  @IsTeacher
  @PostMapping("/teachers/{teacherAuthId}/thread-replies/{threadReplyId}")
  public void createThreadReplyCommentsByTeacher(
      @PathVariable String orgSlug,
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable String teacherAuthId,
      @PathVariable Long threadReplyId) {
    threadReplyCommentsService.createThreadReplyCommentsByTeacher(
        request, threadReplyId, teacherAuthId, orgSlug);
  }

  @IsStudent
  @PostMapping("/students/{studentAuthId}/thread-replies/{threadReplyId}")
  public void createThreadReplyCommentsByStudent(
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable String studentAuthId,
      @PathVariable Long threadReplyId) {
    threadReplyCommentsService.createThreadReplyCommentsByStudent(
        request, threadReplyId, studentAuthId);
  }

  @IsTeacher
  @PutMapping("/teachers/{teacherAuthId}/thread-reply-comments/{threadReplyCommentId}")
  public void editThreadReplyCommentsByTeacher(
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable String teacherAuthId,
      @PathVariable Long threadReplyCommentId) {
    threadReplyCommentsService.editThreadReplyCommentsByTeacher(
        request, teacherAuthId, threadReplyCommentId);
  }

  @IsStudent
  @PutMapping("/students/{studentAuthId}/thread-reply-comments/{threadReplyCommentId}")
  public void editThreadReplyCommentsByStudent(
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable String studentAuthId,
      @PathVariable Long threadReplyCommentId) {
    threadReplyCommentsService.editThreadReplyCommentsByStudent(
        request, studentAuthId, threadReplyCommentId);
  }
}
