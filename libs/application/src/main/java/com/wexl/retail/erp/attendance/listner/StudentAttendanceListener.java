package com.wexl.retail.erp.attendance.listner;

import com.wexl.retail.attendance.service.AttendanceService;
import com.wexl.retail.erp.attendance.dto.AddAttendanceRequest;
import com.wexl.retail.erp.attendance.publisher.StudentAttendanceEvent;
import com.wexl.retail.repository.StudentAuditRepository;
import com.wexl.retail.repository.TeacherRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class StudentAttendanceListener implements ApplicationListener<StudentAttendanceEvent> {

  private final TeacherRepository teacherRepository;
  private final StudentAuditRepository studentAuditRepository;
  private final AttendanceService attendanceService;

  @Override
  public void onApplicationEvent(StudentAttendanceEvent studentAttendanceEvent) {

    var source = studentAttendanceEvent.getSource();

    if (source instanceof AddAttendanceRequest attendanceRequest) {

      attendanceService.saveAudit(attendanceRequest);
    }
  }
}
