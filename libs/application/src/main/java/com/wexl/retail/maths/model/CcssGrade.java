package com.wexl.retail.maths.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@Table(name = "ccss_grades")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class CcssGrade extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;
  private String gradeSlug;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "ccssGrade", cascade = CascadeType.ALL)
  private List<CcssDomain> ccssDomains;
}
