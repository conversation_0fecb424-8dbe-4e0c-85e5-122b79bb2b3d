package com.wexl.retail.teacher.auth;

import com.wexl.retail.auth.AuthResponse;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.OtpVerificationRequest;
import com.wexl.retail.model.User;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.util.MobileAppUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/auth/teacher")
public class TeacherAuthController {

  private final TeacherAuthService teacherService;
  private final AuthService authService;

  @PostMapping("/send_email_otp/{id}")
  public OtpResponse sendEmailOtp(@Valid @PathVariable("id") long userId) {

    return teacherService.sendEmailOtp(userId);
  }

  @PostMapping("/verify_email")
  public AuthResponse verifyEmailOtp(
      @Valid @RequestBody OtpVerificationRequest verificationRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {

    User teacher = teacherService.verifyEmailOtp(verificationRequest);
    if (teacher != null) {
      var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);

      String jwtToken =
          authService.generateTeacherAccessToken(
              requestComingFromMobileApp, teacher, LoginMethod.USERNAME_PASSWORD);
      return AuthResponse.builder().accessToken(jwtToken).build();
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Code.Expired");
    }
  }

  @IsOrgAdmin
  @DeleteMapping("/{teacherAuthId}:hardDelete")
  public String deleteTeacher(
      @PathVariable String teacherAuthId, @RequestParam("org_slug") String orgSlug) {

    return teacherService.deleteTeacher(teacherAuthId, orgSlug);
  }
}
