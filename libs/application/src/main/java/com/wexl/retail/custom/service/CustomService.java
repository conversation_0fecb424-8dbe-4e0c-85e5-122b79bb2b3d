package com.wexl.retail.custom.service;

import static com.wexl.retail.commons.util.ExtensionUtil.CONTENT_TYPE_EXTENSIONS;
import static java.time.ZoneOffset.UTC;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ExtensionUtil;
import com.wexl.retail.custom.dto.CustomDto;
import com.wexl.retail.storage.StorageService;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomService {

  private final StorageService storageService;
  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";

  public CustomDto.UploadFileResponse uploadSpeechRecording(
      MultipartFile multipartFile, String orgSlug) {
    if (multipartFile == null || multipartFile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Uploaded file is empty or null");
    }

    String contentType = multipartFile.getContentType();
    if (contentType == null || !CONTENT_TYPE_EXTENSIONS.containsKey(contentType)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unsupported file type");
    }

    String reference = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));
    String fileExtension = CONTENT_TYPE_EXTENSIONS.get(contentType);
    String originalFileName = "test.%s".formatted(fileExtension);

    try {
      return generateUploadResponse(orgSlug, multipartFile, reference, originalFileName);
    } catch (IOException e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "File upload failed: " + e.getMessage(), e);
    }
  }

  private CustomDto.UploadFileResponse generateUploadResponse(
      String orgSlug, MultipartFile multipartFile, String reference, String originalFileName)
      throws IOException {

    byte[] fileBytes = multipartFile.getBytes();
    String url = uploadFileToStorage(fileBytes, reference, originalFileName, orgSlug);

    return CustomDto.UploadFileResponse.builder().path("").url(url).previewUrl(url).build();
  }

  private String uploadFileToStorage(
      byte[] fileBytes, String reference, String fileName, String orgSlug) {
    try {
      String contentType = determineContentType(fileName);
      String filePath = getFilePath(orgSlug, reference, contentType);

      storageService.uploadFile("wexl-strapi-images", contentType, filePath, fileBytes, true);

      return "https://images.wexledu.com/%s".formatted(filePath);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Error uploading file to S3: " + e.getMessage(), e);
    }
  }

  private String determineContentType(String fileName) {
    try {
      String contentType = Files.probeContentType(Path.of(fileName));
      return contentType != null ? contentType : MediaType.IMAGE_PNG_VALUE;
    } catch (IOException e) {
      log.debug("Failed to determine content type, defaulting to PNG", e);
      return MediaType.IMAGE_PNG_VALUE;
    }
  }

  public String getFilePath(String orgSlug, String reference, String contentType) {
    return "bet-sections/%s/%s%s"
        .formatted(orgSlug, reference, ExtensionUtil.getExtension(contentType));
  }
}
