package com.wexl.retail.classroom.core.handler;

import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.classroom.core.service.ClassroomScheduleInstService;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.model.User;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Order(100)
public class DefaultClassroomScheduleInstHandler implements ClassroomScheduleInstHandler {

  private final ClassroomScheduleInstService classroomScheduleInstService;
  private final ClassroomService classroomService;

  @Override
  public List<ClassroomScheduleInstResponse> getClassroomSchedules(
      User user, Long fromDate, Long toDate, String orgSlug) {
    return classroomScheduleInstService.getClassroomSchedules(user, fromDate, toDate, orgSlug);
  }

  @Override
  public List<StudentScheduleResponse> getStudentSchedules(
      String orgSlug, Long studentId, Integer limit, Long date) {
    return classroomScheduleInstService.getStudentSchedules(orgSlug, studentId, limit, date);
  }

  @Override
  public List<Long> getClassroomDates(String studentId, Integer limit) {
    return classroomService.getClassroomDates(studentId, limit);
  }

  @Override
  public List<StudentScheduleResponse> getStudentClassroomsByDate(
      String studentId, String orgSlug, Long date, Integer limit) {
    return classroomService.getStudentClassroomsByDate(studentId, orgSlug, date, limit);
  }
}
