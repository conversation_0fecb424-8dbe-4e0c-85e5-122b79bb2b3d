package com.wexl.retail.schedules.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.schedules.domain.MeetingType;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseScheduleResponse {
  private String url;

  @JsonProperty("teacher_url")
  private String teacherUrl;

  private String name;
  private String zoomMeetingNumber;

  @JsonProperty("schedule_id")
  private long id;

  @JsonProperty("to_time")
  private long toTime;

  @JsonProperty("from_time")
  private long fromTime;

  @JsonProperty("day_of_week")
  private DayOfWeek dayOfWeek;

  @JsonProperty("meeting_type")
  private MeetingType meetingType;

  public void setFromTime(Timestamp fromTime) {
    this.fromTime = fromTime.getTime();
  }

  public void setToTime(Timestamp toTime) {
    this.toTime = toTime.getTime();
  }

  @JsonProperty("created_at")
  private long createdAt;
}
