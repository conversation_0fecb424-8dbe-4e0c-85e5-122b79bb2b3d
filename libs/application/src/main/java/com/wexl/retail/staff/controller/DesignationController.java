package com.wexl.retail.staff.controller;

import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.service.DesignationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/designations")
@RequiredArgsConstructor
@Slf4j
public class DesignationController {
  private final DesignationService designationService;

  @PostMapping()
  public ResponseEntity<StaffDto.DesignationResponse> createDesignation(
      @RequestBody StaffDto.DesignationRequest request, @PathVariable String orgSlug) {
    log.info("Creating new designation: {}", request.name());
    return new ResponseEntity<>(
        designationService.createDesignation(request, orgSlug), HttpStatus.CREATED);
  }

  @GetMapping("/{id}")
  public ResponseEntity<StaffDto.DesignationResponse> getDesignationById(@PathVariable Long id) {
    log.info("Fetching designation with id: {}", id);
    return ResponseEntity.ok(designationService.getDesignationById(id));
  }

  @GetMapping()
  public ResponseEntity<List<StaffDto.DesignationResponse>> getAllDesignations() {
    log.info("Fetching all designations");
    return ResponseEntity.ok(designationService.getAllDesignations());
  }

  @GetMapping("/departments/{departmentId}")
  public ResponseEntity<List<StaffDto.DesignationResponse>> getDesignationsByDepartment(
      @PathVariable Long departmentId) {
    log.info("Fetching designations for department id: {}", departmentId);
    return ResponseEntity.ok(designationService.getDesignationsByDepartment(departmentId));
  }

  @PutMapping("/{id}")
  public ResponseEntity<StaffDto.DesignationResponse> updateDesignation(
      @PathVariable Long id, @RequestBody StaffDto.DesignationRequest request) {
    log.info("Updating designation with id: {}", id);
    return ResponseEntity.ok(designationService.updateDesignation(id, request));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteDesignation(@PathVariable Long id) {
    log.info("Deleting designation with id: {}", id);
    designationService.deleteDesignation(id);
    return ResponseEntity.noContent().build();
  }
}
