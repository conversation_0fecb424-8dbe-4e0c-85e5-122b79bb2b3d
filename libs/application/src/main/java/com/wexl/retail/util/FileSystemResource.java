package com.wexl.retail.util;

import lombok.EqualsAndHashCode;
import org.springframework.core.io.ByteArrayResource;

@EqualsAndHashCode(callSuper = true)
public class FileSystemResource extends ByteArrayResource {

  private String fileName;

  public FileSystemResource(byte[] byteArray, String filename) {
    super(byteArray);
    this.fileName = filename;
  }

  @Override
  public String getFilename() {
    return fileName;
  }

  public void setFilename(String fileName) {
    this.fileName = fileName;
  }
}
