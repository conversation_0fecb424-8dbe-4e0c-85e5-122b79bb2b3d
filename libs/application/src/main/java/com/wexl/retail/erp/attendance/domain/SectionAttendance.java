package com.wexl.retail.erp.attendance.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import jakarta.persistence.*;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "section_attendance")
public class SectionAttendance extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne
  @JoinColumn(name = "section_id")
  private Section section;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization org;

  @Column(name = "date_id")
  private Integer dateId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "calender_details_id")
  private CalenderDetails calenderDetails;

  @Column(name = "is_holiday")
  private CompletionStatus status = CompletionStatus.NOTCOMPLETED;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "sectionAttendance", cascade = CascadeType.ALL)
  private List<SectionAttendanceDetails> attendanceDetails;
}
