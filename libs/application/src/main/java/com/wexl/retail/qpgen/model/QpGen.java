package com.wexl.retail.qpgen.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import com.wexl.retail.qpgen.dto.QpGenDto;
import com.wexl.retail.qpgen.dto.QpGenStatus;
import com.wexl.retail.qpgen.dto.QpType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qp_gen")
public class QpGen extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;
  private Long marks;
  private Long duration;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("board_slug")
  private String boardSlug;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("subject_name")
  private String subjectName;

  @JsonProperty("qp_type")
  private QpType qpType;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "sections", columnDefinition = "jsonb")
  private List<QpGenDto.Sections> sections;

  private QpGenStatus status;

  @JsonProperty("test_definition_id")
  private List<Long> testDefinitionId;
}
