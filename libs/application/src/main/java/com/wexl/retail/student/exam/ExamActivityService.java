package com.wexl.retail.student.exam;

import static com.wexl.retail.util.Constants.COMMA_SEPERATOR;
import static com.wexl.retail.util.Constants.TEST_EVENT_SQL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.MathUtil;
import com.wexl.retail.generic.GenericRequest;
import com.wexl.retail.generic.GenericResponse;
import com.wexl.retail.generic.GenericService;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.NotificationService;
import com.wexl.retail.notification.dto.NotificationEvent;
import com.wexl.retail.notification.dto.TestRequest;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.LinkedCaseInsensitiveMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExamActivityService {

  private final StrapiService strapiService;
  private final GenericService genericService;
  private final NotificationService notificationService;

  @Value("${app.sns.studentEvents.wexlPracticeUrl}")
  private String wexlPracticeUrl;

  @Value("${app.sns.studentEvents.wexlTestUrl}")
  private String wexlTestUrl;

  @Value("${app.sns.studentEvents.resultUrl}")
  private String resultUrl;

  @Value("${app.sns.studentEvents.correctionUrl}")
  private String correctionUrl;

  @Value("${app.sns.studentEvents.wexlSchoolTestUrl}")
  private String wexlSchoolTestUrl;

  @Value("${app.publishTestNotification:false}")
  public boolean shouldPublishTestNotification;

  private LinkedCaseInsensitiveMap<String> fetchTestEventDetails(Long examId, String key) {
    ArrayList<String> paramList = new ArrayList<>(Collections.singleton(examId.toString()));
    var genericRequest = GenericRequest.builder().key(key).params(paramList).build();
    GenericResponse response = genericService.fetchData(genericRequest);
    final var responseData = (ArrayList) response.getData();
    if (CollectionUtils.isEmpty(responseData)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestEventDetails.Empty");
    }
    return ((LinkedCaseInsensitiveMap<String>) (responseData).getFirst());
  }

  public void notifyTestCompletion(Exam exam, TestDefinitionService testDefinitionService) {
    if (!shouldPublishTestNotification) {
      return;
    }

    try {
      TestRequest testRequest =
          exam.getExamType().equals(Constants.MOCK_TEST)
              ? generateMockTestRequest(exam)
              : generateTestRequest(exam, testDefinitionService);
      notificationService.notifyTestCompletion(testRequest);
    } catch (Exception ex) {
      log.error("Error during publishing test completion message", ex);
      // log and move on if there is any issue with publishing the test completion
    }
  }

  private TestRequest generateMockTestRequest(Exam exam) {
    User user = exam.getStudent().getUserInfo();
    TestDefinition testDefinition = exam.getTestDefinition();
    return TestRequest.builder()
        .userName(user.getUserName())
        .fullName(user.getFirstName() + user.getLastName())
        .chapter("")
        .subject("")
        .board(testDefinition.getBoardSlug())
        .subTopic("")
        .organization(testDefinition.getOrganization())
        .type(TestType.MOCK_TEST.toString())
        .grade(testDefinition.getGradeSlug())
        .totalQuestions(exam.getNoOfQuestions())
        .correctAnswers(exam.getCorrectAnswers())
        .startTime(exam.getStartTime())
        .endTime(exam.getEndTime())
        .resultUrl(resultUrl + exam.getId())
        .correctionUrl(String.format(correctionUrl, exam.getStudentId(), exam.getId()))
        .section(List.of(exam.getStudentSection().getUuid().toString()))
        .parent("")
        .examId(exam.getId())
        .corrected(exam.isCorrected())
        .studentId(exam.getStudentId())
        .testUrl("")
        .marksScored(exam.getMarksScored())
        .totalMarks(exam.getTotalMarks())
        .percentage(MathUtil.calculatePercentage(exam.getMarksScored(), exam.getTotalMarks()))
        .firebaseToken(exam.getFirebaseToken())
        .build();
  }

  public void notifyTestCorrection(Exam exam, TestDefinitionService testDefinitionService) {
    var testRequest = generateTestRequest(exam, testDefinitionService);
    testRequest.setType(NotificationEvent.CORRECTION_COMPLETED.getType());
    notificationService.notifyTestCompletion(testRequest);
  }

  private TestRequest generateTestRequest(Exam exam, TestDefinitionService testDefinitionService) {
    LinkedCaseInsensitiveMap<String> eventDetails =
        Objects.requireNonNull(fetchTestEventDetails(exam.getId(), TEST_EVENT_SQL));
    return TestRequest.builder()
        .userName(eventDetails.get("username"))
        .fullName(eventDetails.get("fullname"))
        .chapter(getChapterNamesForExam(exam, testDefinitionService))
        .subject(
            exam.getSubjectId() != 0
                ? strapiService.getSubjectById(exam.getSubjectId()).getName()
                : null)
        .board(eventDetails.get("board"))
        .subTopic(eventDetails.get("subtopic"))
        .organization(eventDetails.get("organization"))
        .type(eventDetails.get("type"))
        .grade(eventDetails.get("grade"))
        .totalQuestions(exam.getNoOfQuestions())
        .correctAnswers(exam.getCorrectAnswers())
        .startTime(exam.getStartTime())
        .endTime(exam.getEndTime())
        .resultUrl(resultUrl + exam.getId())
        .correctionUrl(String.format(correctionUrl, exam.getStudentId(), exam.getId()))
        .section(List.of(exam.getStudentSection().getUuid().toString()))
        .parent("")
        .examId(exam.getId())
        .corrected(exam.isCorrected())
        .studentId(exam.getStudentId())
        .testUrl(buildTestUrl(eventDetails))
        .marksScored(exam.getMarksScored())
        .totalMarks(exam.getTotalMarks())
        .percentage(MathUtil.calculatePercentage(exam.getMarksScored(), exam.getTotalMarks()))
        .firebaseToken(exam.getFirebaseToken())
        .build();
  }

  private String getChapterNamesForExam(Exam exam, TestDefinitionService testDefinitionService) {
    Assert.notNull(exam, "Fetch chapter names : Exam cannot be null!");
    if (Objects.nonNull(exam.getTestDefinition())) {
      return String.join(
          COMMA_SEPERATOR,
          testDefinitionService.getChapterNamesOfTestDefinition(exam.getTestDefinition()));
    }
    if (Objects.nonNull(exam.getScheduleTest())) {
      return String.join(
          COMMA_SEPERATOR,
          testDefinitionService.getChapterNamesOfTestDefinition(
              exam.getScheduleTest().getTestDefinition()));
    }
    return strapiService.getChapterById(exam.getChapterId()).getName();
  }

  private String buildTestUrl(LinkedCaseInsensitiveMap<String> eventDetails) {
    if ("PRACTICE".equals(eventDetails.get("type"))) {
      StringSubstitutor stringSubstitutor = new StringSubstitutor(eventDetails, "[", "]");
      return stringSubstitutor.replace(wexlPracticeUrl);
    }

    if ("TEST".equals(eventDetails.get("type"))) {
      StringSubstitutor stringSubstitutor = new StringSubstitutor(eventDetails, "[", "]");
      return stringSubstitutor.replace(wexlTestUrl);
    }

    if ("SCHOOL_TEST".equals(eventDetails.get("type"))) {
      StringSubstitutor stringSubstitutor = new StringSubstitutor(eventDetails, "[", "]");
      return stringSubstitutor.replace(wexlSchoolTestUrl);
    }
    return "";
  }
}
