package com.wexl.retail.test.school.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = "test_definitions")
public class TestDefinition extends TestData {

  @Column(columnDefinition = "VARCHAR(150)")
  private String questionPath;

  @Column(columnDefinition = "VARCHAR(150)")
  private String solutionPath;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private TestDefinitionMetadata metadata;

  @JsonProperty("total_marks")
  private Integer totalMarks;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "question_v1", columnDefinition = "jsonb")
  private List<String> questionV1;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "question_v2", columnDefinition = "jsonb")
  private List<String> questionV2;

  public TestDefinition() {
    super();
  }

  private String instructions;

  @Column(name = "published_at")
  private Timestamp publishedAt;

  @JsonIgnore
  @OneToMany(fetch = FetchType.LAZY, mappedBy = "testDefinition", cascade = CascadeType.ALL)
  private List<TestDefinitionSection> testDefinitionSections;

  private String theme;

  private TestCategory category = TestCategory.DEFAULT;

  private Long reportCardTemplateId;

  private String S3Path;
}
