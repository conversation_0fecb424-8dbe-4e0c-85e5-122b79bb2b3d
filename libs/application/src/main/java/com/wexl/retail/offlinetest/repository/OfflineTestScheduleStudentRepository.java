package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.OfflineTestSchedule;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface OfflineTestScheduleStudentRepository
    extends JpaRepository<com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent, Long> {

  @Query(
      value =
          """
                            select otd.title as examName,sm."name"  as subjectName,sm.wexl_subject_slug as subjectSlug,ots.marks as totalMarks,otss.marks as marksScored,
                            otd.exam_start_date as examStartDate
                            from offline_test_schedule_student otss  join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                            join offline_test_definition otd on otd.id  = ots.offline_test_definition_id
                            join subject_metadata sm on sm.id = ots.subject_metadata_id
                            where otss.student_id  =:studentId and ots.deleted_at is null
                            order by otd.id desc
                                                 """,
      nativeQuery = true)
  List<StudentReportCard> getStudentReportCard(Long studentId);

  @Query(
      value =
          """
                          select COALESCE(sum(ots.marks),0) as marksScored,sum(ots2.marks) as totalMarks
                          from subject_metadata_students sms join offline_test_schedule_student ots  on sms.student_id  = ots.student_id
                          join offline_test_schedule ots2 on ots2.id = ots.offline_test_schedule_id
                          join subject_metadata sm  on sms.subject_metadata_id  = sm.id and
                           ots2.subject_metadata_id = sm.id
                          where ots2.offline_test_definition_id  =:testDefinitionId and sms.student_id  =:studentId
                          and ots2.deleted_at is null
                          and sm."type" = 'MANDATORY' and sm.category = 'SCHOLASTIC'
                          """,
      nativeQuery = true)
  ReportCardDataTotals getReportCardTotals(Long studentId, Long testDefinitionId);

  @Query(
      value =
          """
                          SELECT DISTINCT sm."name" AS subjectName,COALESCE(otss.marks,0) AS marks,ots.marks as totalMarks,
                          otd.title,otss.is_attended as isAttended,sm.seq_no as seqNo,sm."type" as type,sm.category,term_slug as termSlug,
                          otd.assessment_slug as assessmentSlug
                          FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                          JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                          JOIN subject_metadata_students sms ON sms.student_id = otss.student_id
                          JOIN subject_metadata sm ON sm.id = sms.subject_metadata_id
                          WHERE term_slug IN ('t1', 't2') AND otd.org_slug =:orgSlug AND otss.student_id =:studentId
                          GROUP BY sm."name",otss.marks, otd.title, otss.is_attended, sm.seq_no, sm."type", sm.category, term_slug,totalMarks,assessmentSlug
                          ORDER BY sm.seq_no ASC;
                                  """,
      nativeQuery = true)
  List<LowerGradeReportCardData> getLowerGradeReportCard(Long studentId, String orgSlug);

  @Query(
      value =
          """
                          SELECT DISTINCT sm."name" AS subjectName,COALESCE(otss.marks,0) AS total,COALESCE(otss.marks_1,0) as marks1,
                          COALESCE(otss.marks_3,0) as marks2,COALESCE(otss.marks_2,0) as marks3,otd.assessment_slug as assessmentSlug,
                          otd.title,otss.is_attended as isAttended,sm.seq_no as seqNo,sm."type" as type,sm.category,term_slug as termSlug
                          FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                          JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                          JOIN subject_metadata_students sms ON sms.student_id = otss.student_id
                          JOIN subject_metadata sm ON sm.id = sms.subject_metadata_id AND ots.subject_name = sm."name"
                          WHERE term_slug IN ('t1', 't2') AND otd.org_slug =:orgSlug AND otss.student_id =:studentId
                          GROUP BY sm."name",otss.marks, otd.title, otss.is_attended, sm.seq_no, sm."type", sm.category, term_slug,assessmentSlug,marks1,marks2,marks3
                          ORDER BY sm.seq_no ASC;
                                  """,
      nativeQuery = true)
  List<UpperGradeReportCardData> getUpperGradeReportCard(Long studentId, String orgSlug);

  @Query(
      value =
          """
                          select otss.* from offline_test_definition otd
                          inner join offline_test_schedule ots on ots.offline_test_definition_id = otd.id
                          inner join subject_metadata sm on sm.id = ots.subject_metadata_id
                          inner join offline_test_schedule_student otss on otss.offline_test_schedule_id = ots.id
                          inner join students s on s.id = otss.student_id
                          where  otd.id = :otdId and sm.wexl_subject_slug = :subject and s.deleted_at is null
                                                  """,
      nativeQuery = true)
  List<OfflineTestScheduleStudent> getScheduleStudentsByDefinitionAndSubject(
      Long otdId, String subject);

  List<OfflineTestScheduleStudent> findByStudentId(long stuentId);

  @Query(
      value =
          """
                                  select otss.* from offline_test_definition otd
                                  inner join offline_test_schedule ots on ots.offline_test_definition_id = otd.id
                                  inner join offline_test_schedule_student otss on otss.offline_test_schedule_id = ots.id
                                  where  otd.id in (:offlineTestDefinitionIds)
                                                          """,
      nativeQuery = true)
  List<OfflineTestScheduleStudent> getAllByOfflineTestDefinition(
      List<Long> offlineTestDefinitionIds);

  List<OfflineTestScheduleStudent> findByStudentIdOrderByIdDesc(long studentId);

  @Query(
      value =
          """
                          select sm."name" as subjectName,ta.slug as assessmentSlug, ots.marks as totalMarks, otss.marks as marks, sm.seq_no as seqNo
                          from offline_test_schedule_student otss
                          join offline_test_schedule ots on ots.id = otss.offline_test_schedule_id
                          join offline_test_definition otd on otd.id  = ots.offline_test_definition_id
                          join subject_metadata sm on sm.id = ots.subject_metadata_id
                          join term_assessment_categories tac on tac.id = otd.assessment_category_id
                          join term_assessments ta on ta.id = otd.assessment_id
                          where otss.student_id = :studentId and otd.section_uuid = :sectionUuid and tac."name" in (:assessmentCategories) and sm.category = :category
                          """,
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentMarksByStudentAndSectionAndAssessmentCategories(
      Long studentId, String sectionUuid, List<String> assessmentCategories, String category);

  @Query(
      value =
          """
                  select distinct sm."name" as subjectName,ta.slug as assessmentSlug, ots.marks as totalMarks, otss.marks as marks,sm.wexl_subject_slug as subjectSlug,
                   sm.seq_no as seqNo,sm.category, tac."name" as assessmentCategory,otd.section_uuid,otss.is_attended as isAttended,otss.remarks
                   from offline_test_schedule_student otss
                   join offline_test_schedule ots on ots.id = otss.offline_test_schedule_id
                   join offline_test_definition otd on otd.id  = ots.offline_test_definition_id
                   join subject_metadata sm on sm.id = ots.subject_metadata_id
                   join subject_metadata_students sms on sms.student_id = otss.student_id and sm.id = sms.subject_metadata_id
                   join term_assessment_categories tac on tac.id = otd.assessment_category_id
                   join term_assessments ta on ta.id = otd.assessment_id
                   join terms t on t.id  = ta.term_id where otss.student_id = :studentId
                   and ots.deleted_at is null and (cast((:terms) as varChar) is null or t.slug in (:terms))
                                  """,
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentMarksByCategories(Long studentId, List<String> terms);

  List<OfflineTestScheduleStudent> findByOfflineTestScheduleDetailsIn(
      List<OfflineTestSchedule> testSchedules);
}
