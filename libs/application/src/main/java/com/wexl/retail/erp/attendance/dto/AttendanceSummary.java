package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface AttendanceSummary {

  @JsonProperty("attendance_id")
  Long getattendanceId();

  @JsonProperty("date_id")
  Long getDateId();

  @JsonProperty("section_name")
  String getsectionName();

  @JsonProperty("grade_name")
  String getgradeName();

  @JsonProperty("present_count")
  Long getpresentCount();

  @JsonProperty("leave_count")
  Long getleaveCount();

  @JsonProperty("late_comer_count")
  Long getLateComerCount();

  @JsonProperty("absent_count")
  Long getAbsentCount();

  @JsonProperty("half_day_count")
  Long getHalfDayCount();

  @JsonProperty("ptm_count")
  Long getPtmCount();

  @JsonProperty("nid_count")
  Long getNidCount();

  @JsonProperty("status")
  String getstatus();

  @JsonProperty("section_uuid")
  String getsectionUuid();

  @JsonProperty("section_id")
  Long getsectionId();

  @JsonProperty("isHoliday")
  Integer getisHoliday();

  @JsonProperty("grade_slug")
  String getGradeSlug();
}
