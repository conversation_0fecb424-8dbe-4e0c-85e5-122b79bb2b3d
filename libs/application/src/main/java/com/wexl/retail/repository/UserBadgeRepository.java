package com.wexl.retail.repository;

import com.wexl.retail.model.Badge;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserBadge;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserBadgeRepository extends JpaRepository<UserBadge, Long> {

  int countUserBadgeByUserAndBadge(User user, Badge badge);

  @Query(
      value =
          "select * from user_badge where user_id = :userId and badge_id = :badgeId order by issued_date desc limit 1",
      nativeQuery = true)
  Optional<UserBadge> getLatestIssuedBadge(long userId, long badgeId);
}
