package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentTestsCount extends AbstractMetricHandler implements MetricHandler {
  @Override
  public String name() {
    return "manager-overall-test-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Long testDefinition =
        Long.valueOf(genericMetricRequest.getInput().get(TEST_DEFINITION_ID).toString());
    return testDefinitionService.getMockTestReport(org, testDefinition);
  }
}
