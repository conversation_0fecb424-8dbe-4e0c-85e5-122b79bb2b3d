package com.wexl.retail.courses.categories.controller;

import com.wexl.retail.commons.security.annotation.LegacyApi;
import com.wexl.retail.courses.categories.dto.CourseCategoryDto;
import com.wexl.retail.courses.categories.service.CourseCategoryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class CourseCategoryController {

  private final CourseCategoryService courseCategoryService;

  @PostMapping("orgs/{orgSlug}/course-categories")
  @LegacyApi
  public void createCategory(
      @PathVariable("orgSlug") String org,
      @RequestBody CourseCategoryDto.CourseCategoryRequest categoryRequest) {

    courseCategoryService.createCategory(org, categoryRequest);
  }

  @GetMapping("orgs/{orgSlug}/course-categories")
  @LegacyApi
  public List<CourseCategoryDto.CourseCategories> getCategories(
      @PathVariable("orgSlug") String org) {
    return courseCategoryService.getAllCategories(org);
  }

  @PostMapping("orgs/{orgSlug}/course-categories/{id}")
  @LegacyApi
  public void editCategory(
      @PathVariable("orgSlug") String org,
      @PathVariable("id") Long categoryId,
      @RequestBody CourseCategoryDto.CourseCategoryRequest categoryRequest) {
    courseCategoryService.editCategory(org, categoryId, categoryRequest.name());
  }

  @DeleteMapping("orgs/{orgSlug}/course-categories/{id}")
  @LegacyApi
  public void deleteCategory(
      @PathVariable("orgSlug") String org, @PathVariable("id") Long categoryId) {

    courseCategoryService.deleteCategory(org, categoryId);
  }

  @GetMapping("orgs/{orgSlug}/course-categories/{id}")
  @LegacyApi
  public CourseCategoryDto.CourseCategories getCategoriesByID(
      @PathVariable String orgSlug, @PathVariable Long id) {
    return courseCategoryService.getCategoriesById(orgSlug, id);
  }
}
