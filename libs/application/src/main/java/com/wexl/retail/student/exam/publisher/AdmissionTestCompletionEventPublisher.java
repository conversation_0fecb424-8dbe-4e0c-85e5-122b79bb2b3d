package com.wexl.retail.student.exam.publisher;

import com.wexl.retail.student.exam.Exam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class AdmissionTestCompletionEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishAdmissionTestCompletion(final Exam exam) {
    var testCompletionEvent = new AdmissionTestCompletionEvent(exam);
    applicationEventPublisher.publishEvent(testCompletionEvent);
  }
}
