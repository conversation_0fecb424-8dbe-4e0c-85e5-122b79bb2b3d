package com.wexl.retail.mlp.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;
import static java.time.ZoneOffset.UTC;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.security.annotation.IsManagerOrOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.content.model.QuestionResponse;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.QuestionsAnalytics;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.dto.BulkMlpRequest.BulkMlpData;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.student.mlp.service.MlpAssetHandler;
import com.wexl.retail.util.MlpStudentOmrProcessor;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class MlpController {

  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";
  private final MlpService mlpService;
  private final AuthService authService;
  private final StorageService storageService;
  private final List<MlpAssetHandler> mlpAssetHandler;

  private final EventNotificationService eventNotificationService;

  private final MlpStudentOmrProcessor mlpStudentOmrProcessor;

  @PostMapping(path = "/orgs/{orgSlug}/mlps")
  @IsTeacher
  public void createMlp(
      @PathVariable String orgSlug,
      @RequestBody AdvancedMlpRequest advancedMlpRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    if (advancedMlpRequest.getQuestionsAssigneeMode().equals(QuestionsAssigneeMode.MANUAL)) {
      mlpService.createMlpAdvanced(advancedMlpRequest, orgSlug);
    } else {
      mlpService.createMlpLegacy(advancedMlpRequest, orgSlug, bearerToken);
    }

    eventNotificationService.triggerMlpNotification(advancedMlpRequest);
  }

  @IsStudent
  @PostMapping("/orgs/{orgId}/students/{studentId}/mlps:status")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateMlpStatus(@Valid @RequestBody MlpStudentStatus studentStatusUpdate) {

    mlpService.updateMlpInstStatus(studentStatusUpdate);
  }

  @GetMapping("orgs/{orgId}/mlps")
  public List<MlpResponse> getMlpAnnouncements(
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent,
      @PathVariable String orgId,
      @RequestParam(required = false, defaultValue = "false") boolean showAll,
      @RequestParam(required = false) Long fromDate,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    if (showAll && Objects.nonNull(fromDate)) {
      return mlpService.getAllMlps(orgId, fromDate, limit);
    }
    return mlpService.getMlpsByTeacherPreferences(orgId, limit);
  }

  @GetMapping("orgs/{orgId}/child-orgs/{childOrgSlug}/mlps")
  public List<MlpResponse> getChildOrgMlpAnnouncements(
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent,
      @PathVariable String childOrgSlug,
      @RequestParam Long fromDate,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    return mlpService.getAllMlps(childOrgSlug, fromDate, limit);
  }

  @IsTeacher
  @GetMapping("orgs/{orgId}/mlps/{mlpId}")
  public MlpSummary getStudentMlpResponse(
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent,
      @PathVariable String orgId,
      @PathVariable Long mlpId,
      @RequestParam(required = false) String childOrg) {

    return mlpService.getMlpStudentPracticeDetails(mlpId, childOrg);
  }

  @IsTeacher
  @PostMapping("orgs/{orgId}/mlps/{mlpId}/validate")
  public ResponseEntity<Void> validateMlp(@PathVariable String orgId, @PathVariable Long mlpId) {

    mlpService.validateMlp(mlpId);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/orgs/{orgId}/students/{studentId}/mlps")
  public List<MlpResponse> getSectionMlps(
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent,
      @PathVariable String orgId,
      @PathVariable String studentId,
      @RequestParam(required = false) Long date,
      @RequestParam(required = false) String mlpStatus,
      @RequestParam(required = false, defaultValue = "50") int limit) {

    return mlpService.getMlpsAssignedToStudent(orgId, studentId, date, limit, mlpStatus);
  }

  @IsTeacher
  @GetMapping("/orgs/{orgId}/mlps/teacher-metrics")
  public List<MlpTeacherMetricResponse> getTeacherMlpMetrics(
      @PathVariable String orgId,
      @RequestParam(required = false) String gradeSlug,
      @RequestParam Long fromDate) {

    return mlpService.getTeacherMetrics(orgId, fromDate, gradeSlug);
  }

  @IsTeacher
  @GetMapping("/orgs/{orgId}/child-orgs/{childOrgSlug}/mlps/teacher-metrics")
  public List<MlpTeacherMetricResponse> getChildOrgTeacherMlpMetrics(
      @PathVariable String childOrgSlug, @RequestParam Long fromDate) {

    return mlpService.getTeacherMetrics(childOrgSlug, fromDate, null);
  }

  @IsTeacher
  @GetMapping("/orgs/{orgId}/mlps/grade-metrics")
  public List<MlpGradeMetricResponse> getGradeMlpMetrics(
      @PathVariable String orgId, @RequestParam Long fromDate) {

    return mlpService.getGradeMetrics(orgId, fromDate);
  }

  @IsTeacher
  @GetMapping("/orgs/{orgId}/child-orgs/{childOrgSlug}/mlps/grade-metrics")
  public List<MlpGradeMetricResponse> getChildOrgGradeMlpMetrics(
      @PathVariable String childOrgSlug, @RequestParam Long fromDate) {

    return mlpService.getGradeMetrics(childOrgSlug, fromDate);
  }

  @GetMapping("/orgs/{orgId}/mlps/child-org-metrics")
  @IsTeacher
  public List<MlpMetricResponse> getChildInstitutionMetrics(
      @PathVariable String orgId, @RequestParam Long fromDate) {

    return mlpService.getChildInstitutionMetrics(fromDate);
  }

  @GetMapping("/orgs/{orgId}/mlps/child-org-grade-metrics")
  @IsTeacher
  public List<MlpMetricResponse> getChildInstitutionGradeMetrics(
      @PathVariable String orgId, @RequestParam Long fromDate) {

    return mlpService.getChildInstitutionGradeMetrics(fromDate);
  }

  @GetMapping("/orgs/{orgId}/mlps/child-org-subject-metrics")
  @IsTeacher
  public List<MlpMetricResponse> getChildInstitutionSubjectMetrics(
      @PathVariable String orgId, @RequestParam Long fromDate) {

    return mlpService.getChildInstitutionSubjectMetrics(fromDate);
  }

  @GetMapping("/orgs/{orgId}/students/{studentId}/mlps:activity")
  public List<Long> getSectionMlpsActivity(
      @PathVariable String orgId, @PathVariable String studentId) {

    return mlpService.getMlpActivityDates(orgId, studentId);
  }

  @GetMapping("/orgs/{orgId}/mlps/{examRef}/questions")
  public QuestionResponse getMlpQuestions(
      @PathVariable String examRef, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return mlpService.getMlpQuestions(examRef, bearerToken, false, false);
  }

  @IsStudent
  @PostMapping("/orgs/{orgId}/mlps/{examRef}/exam")
  public ExamResponse startMlpExam(
      @PathVariable("orgId") String orgSlug,
      @PathVariable String examRef,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    Student student = authService.getStudentDetails().getStudentInfo();
    return mlpService.startMlpExam(student, orgSlug, examRef, bearerToken);
  }

  @Deprecated
  @GetMapping("/orgs/{orgId}/mlp-weekly-stats")
  public List<GenericMetricResponse> getMlpMetrics(@PathVariable String orgId) {

    return mlpService.getMlpCountByWeek(List.of(orgId), 12);
  }

  @Deprecated
  @GetMapping("/orgs/{orgId}/child-orgs/{childOrgSlug}/mlp-weekly-stats")
  public List<GenericMetricResponse> getChildOrgMlpMetrics(@PathVariable String childOrgSlug) {

    return mlpService.getMlpCountByWeek(List.of(childOrgSlug), 12);
  }

  @IsManagerOrOrgAdmin
  @GetMapping("/orgs/{orgSlug}/section/{sectionUuid}/mlps-details-by-student")
  public List<MlpLearningAnalyticsByStudent> getDetailsByStudent(
      @PathVariable String sectionUuid,
      @RequestParam("subject") List<String> subjects,
      @RequestParam long fromDate,
      @PathVariable String orgSlug) {
    return mlpService.getMlpDetailsByStudent(orgSlug, sectionUuid, subjects, fromDate);
  }

  @PostMapping("/mlp/migrateMlp")
  public void migrateMlp() {
    mlpService.migrateMlp();
  }

  @GetMapping("/orgs/{orgSlug}/mlps/{mlpId}/questions-analysis")
  public QuestionsAnalytics getMlpDetailsById(
      @PathVariable long mlpId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @RequestParam(required = false) String childOrg) {
    return mlpService.getMlpDetailById(mlpId, bearerToken, childOrg);
  }

  @GetMapping("orgs/{orgSlug}/mlps/student-details")
  public List<MlpStudentDetail> fetchAllMlpsStudentResponse(
      @RequestParam Optional<String> childOrg,
      @RequestParam Optional<String> grade,
      @RequestParam Optional<String> subject,
      @RequestParam(required = false, defaultValue = "100") int limit,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate) {

    return mlpService.fetchStudentResponseOfMlpsByGradeAndSubject(
        childOrg.orElse(Strings.EMPTY),
        grade.orElse(Strings.EMPTY),
        subject.orElse(Strings.EMPTY),
        limit,
        fromDate,
        toDate);
  }

  @PostMapping("orgs/{orgId}/mlps/teacher-details")
  public List<MlpResponse> getAllMlpsByOrgAndTeacher(
      @PathVariable("orgId") String parentOrg,
      @RequestParam Optional<String> childOrg,
      @RequestParam Optional<String> teacherUuid,
      @RequestParam(required = false, defaultValue = "100") int limit,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate,
      @RequestBody KMBoardRequest request) {

    return mlpService.getAllMlpsByOrgAndTeacher(
        childOrg.orElse(Strings.EMPTY),
        teacherUuid.orElse(Strings.EMPTY),
        limit,
        parentOrg,
        fromDate,
        toDate,
        request.getSubjects());
  }

  @PostMapping("/orgs/{orgSlug}/mlps/{mlpId}/mlps-questions-analysis")
  public ResponseEntity<List<MlpsQuestionsOptionsData>> getMlpsQuestionsAnalytics(
      @PathVariable long mlpId) {
    return ResponseEntity.ok().body(mlpService.getMlpsQuestionsAnalytics(mlpId));
  }

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/get-mlp-details")
  public StudentsDetailsByMlpDto.StudentsDetailsByMlp getStudentsDetailsByMlp(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {
    return mlpService.getStudentsDetailsandMlpDetails(orgSlug, studentAuthId);
  }

  @PostMapping("/orgs/{orgSlug}/mlps/{examRef}/answers")
  public void submitMlpExamForAllStudent(
      @PathVariable String orgSlug,
      @PathVariable String examRef,
      @RequestBody BulkMlpRequest bulkMlpRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    mlpService.submitMlpExam(bulkMlpRequest, orgSlug, examRef, bearerToken);
  }

  @GetMapping("/orgs/{orgSlug}/mlps:omr-upload")
  @IsTeacher
  public Map<String, String> getMlpUploadPresignedUrl(
      @PathVariable String orgSlug, @RequestParam(defaultValue = "csv") String extn) {
    final String reference =
        DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));

    return Map.of(
        "reference",
        reference,
        "url",
        storageService.generatePreSignedUrlForUpload(
            mlpStudentOmrProcessor.getFilePath(orgSlug, reference, extn)),
        "preview-url",
        storageService.generatePreSignedUrlForFetch(
            mlpStudentOmrProcessor.getFilePath(orgSlug, reference, extn)));
  }

  @PostMapping("/orgs/{orgSlug}/mlps:omr-upload")
  public List<BulkMlpData> processCsvFile(
      @PathVariable String orgSlug, @RequestBody BulkMlpFileRequest bulkMlpFileRequest) {
    return mlpStudentOmrProcessor.processMlpFile(orgSlug, bulkMlpFileRequest);
  }

  @GetMapping("/orgs/{orgSlug}/teacher/{teacherId}/mlp-assets")
  public MlpDto.MlpAssetResponse mlpAssets(
      @PathVariable String orgSlug,
      @PathVariable String teacherId,
      @RequestParam String chapterSlug) {
    return mlpService.buildAssetsResponse(orgSlug, teacherId, chapterSlug);
  }
}
