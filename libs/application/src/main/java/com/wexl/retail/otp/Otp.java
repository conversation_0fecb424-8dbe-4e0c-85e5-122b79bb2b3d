package com.wexl.retail.otp;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;

@Entity
@Data
@Table(name = "otp")
public class Otp extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "otp-sequence-generator")
  @SequenceGenerator(name = "otp-sequence-generator", sequenceName = "otp_seq", allocationSize = 1)
  private long id;

  private String otp;

  @ManyToOne
  @JoinColumn(name = "user_id")
  private User user;

  private Date expireAt;
  private String reference;
  private String target;
}
