package com.wexl.retail.persons.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.*;
import com.wexl.retail.model.*;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.persons.dto.PersonsDto;
import com.wexl.retail.persons.dto.StudentTeamCountQueryResult;
import com.wexl.retail.repository.AddressRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.team.repository.TeamRepository;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class PersonService {

  private final StrapiService strapiService;
  private final StudentAuthService studentAuthService;
  private final OrganizationRepository organizationRepository;
  private final SectionRepository sectionRepository;

  private final StudentRepository studentRepository;

  private final AddressRepository addressRepository;
  private final UserRepository userRepository;
  private final TeamRepository teamRepository;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Transactional
  public void createPerson(PersonsDto.PersonsRequest request, String orgSlug) {
    Organization organization = organizationRepository.findBySlug(orgSlug);

    final var orgSettings = strapiService.getOrganizationBySlug(orgSlug).getSettings();
    if (Objects.isNull(orgSettings)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.FailedToCreatePerson.InvalidOrgConfig");
    }
    List<EduBoard> eduBoards = orgSettings.getBoards();
    EduBoard defaultEduBoard = eduBoards.getFirst();
    Grade defaultGrade = defaultEduBoard.getGrades().get(0);

    List<Section> sections =
        sectionRepository.getSectionsUsingGradeSlugs(List.of(defaultGrade.getSlug()), orgSlug);
    if (Objects.isNull(sections) || sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionFind.Grade");
    }

    StudentRequest studentRequest =
        StudentRequest.builder()
            .firstName(request.firstName())
            .lastName(request.lastName())
            .schoolName(organization.getName())
            .mobileNumber(request.mobileNumber())
            .boardSlug(defaultEduBoard.getSlug())
            .gradeSlug(defaultGrade.getSlug())
            .orgSlug(orgSlug)
            .password(request.password())
            .section(sections.getFirst().getName())
            .parentEmail("")
            .parentMobileNumber("")
            .parentFirstName("")
            .parentLastName("")
            .email(request.email())
            .userName(request.userName())
            .academicYearSlug(latestAcademicYear)
            .gender(Gender.valueOf(request.gender()))
            .crStudentUserName(request.crStudentUserName())
            .build();
    Student student = studentAuthService.createStudent(studentRequest, orgSlug);

    if (Objects.nonNull(request.crStudentUserName())) {
      Optional<Student> optionalClassRepStudent =
          Optional.ofNullable(
              studentRepository.getStudentByAuthUserIdAndOrgSlug(
                  request.crStudentUserName(), orgSlug));
      if (optionalClassRepStudent.isPresent()) {
        student.setCrStudentAuthUserId(student.getCrStudentAuthUserId());
        studentRepository.save(student);
      }
    }
    savePersonAddress(request, student);
  }

  private void savePersonAddress(PersonsDto.PersonsRequest request, Student student) {
    User user = userRepository.findByStudentId(student.getId());
    Addresses addresses =
        Addresses.builder()
            .line1(request.address())
            .country(request.country())
            .zipCode(request.zipCode())
            .state(request.state())
            .user(user)
            .build();
    addressRepository.save(addresses);
  }

  public List<PersonsDto.PersonsResponse.Persons> getAllPersonsOfOrg(String orgSlug) {

    List<Student> students = studentRepository.findByOrgSlug(orgSlug);
    List<Long> studentIds = students.stream().map(Student::getId).toList();

    List<StudentTeamCountQueryResult> studentTeamCount =
        teamRepository.getStudentNoOfTeamsCount(studentIds);
    return students.stream()
        .map(
            student -> {
              var count =
                  studentTeamCount.stream()
                      .filter(s -> s.getStudentId().equals(student.getId()))
                      .findFirst();

              User studentUser = student.getUserInfo();

              return PersonsDto.PersonsResponse.Persons.builder()
                  .firstName(studentUser.getFirstName())
                  .lastName(studentUser.getLastName())
                  .studentId(student.getId())
                  .noOfTeams(count.isPresent() ? count.get().getStudentTeamCount() : 0)
                  .reportingManager(getStudentName(student))
                  .studentAuthId(studentUser.getAuthUserId())
                  .build();
            })
        .toList();
  }

  private String getStudentName(Student student) {
    return student.getCrStudentAuthUserId() == null
        ? ""
        : student.getCrStudentAuthUserId().getUserInfo().getFirstName()
            + " "
            + student.getCrStudentAuthUserId().getUserInfo().getLastName();
  }

  public PersonsDto.PersonsResponse.PersonDetails getPersonDetails(String orgSlug, Long personId) {

    Optional<Student> optionalStudent =
        Optional.ofNullable(studentRepository.getStudentByIdAndOrgSlug(personId, orgSlug));
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonNotFound");
    }

    User user = userRepository.findByStudentId(optionalStudent.get().getId());
    if (Objects.equals(user.getIsDeleted(), Boolean.TRUE)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonIsDeleted");
    }
    Student classRep = optionalStudent.get().getCrStudentAuthUserId();
    User classRepUser = classRep == null ? null : classRep.getUserInfo();
    return PersonsDto.PersonsResponse.PersonDetails.builder()
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .userName(user.getUserName())
        .email(user.getEmail())
        .mobileNumber(user.getMobileNumber())
        .gender(user.getGender() == null ? Strings.EMPTY : user.getGender().toString())
        .country(user.getAddresses() == null ? Strings.EMPTY : user.getAddresses().getCountry())
        .address(user.getAddresses() == null ? Strings.EMPTY : user.getAddresses().getLine1())
        .zipCode(user.getAddresses() == null ? Strings.EMPTY : user.getAddresses().getZipCode())
        .reportingManager(getReportingManager(classRepUser))
        .reportingManagerAuthId(classRepUser == null ? Strings.EMPTY : classRepUser.getAuthUserId())
        .mobileNumber(user.getMobileNumber())
        .state(user.getAddresses() == null ? Strings.EMPTY : user.getAddresses().getState())
        .designation(Strings.EMPTY)
        .build();
  }

  private String getReportingManager(User user) {
    return (Objects.nonNull(user) && Objects.isNull(user.getDeletedAt()))
        ? (user.getFirstName() + user.getLastName())
        : Strings.EMPTY;
  }

  public void deletePerson(String orgSlug, Long personId) {
    Optional<Student> optionalStudent =
        Optional.ofNullable(studentRepository.getStudentByIdAndOrgSlug(personId, orgSlug));
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonNotFound");
    }
    Student student = optionalStudent.get();
    User user = userRepository.findByStudentId(student.getId());
    if (Objects.equals(user.getIsDeleted(), Boolean.TRUE)
        || !Objects.equals(user.getOrganization(), orgSlug)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonIsDeleted");
    }

    user.setIsDeleted(true);
    user.setDeletedAt(new Date());
    student.setDeletedAt(new Date());
    userRepository.save(user);
    studentRepository.save(student);

    var studentData =
        teamRepository.getStudentNoOfTeamsCount(Collections.singletonList(student.getId()));
    if (!studentData.isEmpty()) {
      teamRepository.deleteStudentFromTeam(student.getId());
    }
  }

  public void editPerson(String orgSlug, PersonsDto.EditPersonsRequest request, long personId) {

    var student = studentRepository.findById(personId);
    if (student.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonDoesntExist");
    }
    validateStudent(student, orgSlug);
    validateCR(request, orgSlug);
    User user = student.get().getUserInfo();

    StudentRequest studentRequest = new StudentRequest();
    studentRequest.setOrgSlug(orgSlug);
    studentRequest.setFirstName(request.firstName());
    studentRequest.setLastName(request.lastName());
    studentRequest.setGender(Gender.valueOf(request.gender()));
    studentRequest.setCrStudentUserName(request.crStudentUserName());
    studentRequest.setParentMobileNumber("");
    studentRequest.setParentEmail("");
    studentRequest.setParentFirstName("");
    studentRequest.setParentLastName("");
    studentRequest.setSchoolName(user.getStudentInfo().getSchoolName());
    studentRequest.setAcademicYearSlug(user.getStudentInfo().getAcademicYearSlug());
    if (request.mobileNumber() == null) {
      studentRequest.setMobileNumber(student.get().getUserInfo().getMobileNumber());
    } else {
      studentRequest.setMobileNumber(request.mobileNumber());
    }

    studentAuthService.editStudent(
        orgSlug, student.get().getUserInfo().getAuthUserId(), studentRequest);

    Addresses addresses = user.getAddresses();
    addresses.setLine1(request.address());
    addresses.setState(request.state());
    addresses.setZipCode(request.zipCode());
    addressRepository.save(addresses);
  }

  public void validateStudent(Optional<Student> student, String orgSlug) {
    if (student.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonDoesntExist");
    }
    if (!student.get().getUserInfo().getOrganization().equals(orgSlug)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPerson");
    }
  }

  public void validateCR(PersonsDto.EditPersonsRequest request, String orgSlug) {
    var user = userRepository.findByAuthUserIdAndOrganization(request.crStudentUserName(), orgSlug);
    if (user == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidCR");
    }
  }
}
