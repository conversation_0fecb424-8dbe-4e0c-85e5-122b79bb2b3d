package com.wexl.retail.metrics.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/metrics")
@RequiredArgsConstructor
public class GenericMetricController {

  private final List<MetricHandler> metricHandlers;

  @PostMapping()
  public List<GenericMetricResponse> getGenericMetricResponse(
      @PathVariable("orgSlug") String org, @RequestBody GenericMetricRequest genericMetricRequest) {
    Optional<MetricHandler> possibleMetricHandler =
        metricHandlers.stream()
            .filter(handler -> genericMetricRequest.getMetricName().equals(handler.name()))
            .findFirst();
    if (possibleMetricHandler.isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "error.MetricProcessing.Name",
          new String[] {genericMetricRequest.getMetricName()});
    }
    try {
      return possibleMetricHandler.get().execute(org, genericMetricRequest);
    } catch (Exception ex) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.MetricProcessing", ex);
    }
  }
}
