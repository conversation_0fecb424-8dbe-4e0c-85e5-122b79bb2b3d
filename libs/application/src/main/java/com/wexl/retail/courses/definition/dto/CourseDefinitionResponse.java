package com.wexl.retail.courses.definition.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.courses.categories.dto.CourseCategoryDto;
import com.wexl.retail.courses.definition.model.CourseDefinitionStatus;
import com.wexl.retail.courses.module.dto.CourseModuleResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class CourseDefinitionResponse {

  private String name;
  private long id;
  private String description;
  private CourseDefinitionStatus status;
  private int version;
  private Integer moduleCount;

  @JsonProperty("student_status")
  private String studentStatus;

  @JsonProperty("published_at")
  private Long publishedAt;

  @JsonProperty("is_private")
  private boolean isPrivate;

  @JsonProperty("org_slug")
  private String orgSlug;

  private List<CourseModuleResponse> modules;

  @JsonProperty("course_category")
  private CourseCategoryDto.CourseCategories courseCategory;

  @JsonProperty("course_km")
  private Double courseKm;

  @JsonProperty("thumbnail")
  String thumbNail;
}
