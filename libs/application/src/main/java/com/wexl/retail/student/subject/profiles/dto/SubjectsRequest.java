package com.wexl.retail.student.subject.profiles.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class SubjectsRequest {

  @JsonProperty("source_org_slug")
  private String orgSlug;

  @JsonProperty("board_slug")
  private String boardSlug;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("subject_name")
  private String subjectName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("display_name")
  private String displayName;
}
