package com.wexl.retail.whatsapp;

import com.wexl.retail.whatsapp.interakt.dto.Request;
import java.util.List;

public interface WhatsAppService {

  void sendWhatsAppMessage(String templateId, List<Request.Recipient> recipients);

  void sendWhatsAppBotMessage(String message, String recipientNumber);

  String sendBulkWhatsAppMessage(String templateId, List<Request.Recipient> recipientsOriginal);

  String sendBulkWhatsAppMessageWithStaticMessage(
      String templateId, List<Request.Recipient> recipientsOriginal);

  String sendFeeDueBulkWhatsAppMessage(String templateId, List<Request.Recipient> recipients);
}
