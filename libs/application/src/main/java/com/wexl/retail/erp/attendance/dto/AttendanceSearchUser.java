package com.wexl.retail.erp.attendance.dto;

import java.time.LocalDateTime;

public interface AttendanceSearchUser {

  Long getSectionId();

  Long getAttendanceId();

  String getFirstName();

  String getLastName();

  String getGrade();

  String getSectionName();

  String getAuthUserId();

  Long getUserId();

  String getStatus();

  Long getDateId();

  LocalDateTime getEntryTime();

  LocalDateTime getExitTime();

  String getNote();
}
