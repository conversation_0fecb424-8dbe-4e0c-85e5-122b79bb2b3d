package com.wexl.retail.pdf.viewer.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@Table(name = "pdf_digital_signatures")
public class DigitalSignature implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", updatable = false)
  private Long id;

  @Column(name = "username", nullable = false)
  private String username;

  @Column(name = "signature", nullable = false)
  private String signature;

  @Column(name = "width", nullable = false)
  private Long width;

  @Column(name = "height", nullable = false)
  private Long height;
}
