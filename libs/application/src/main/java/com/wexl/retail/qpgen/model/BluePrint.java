package com.wexl.retail.qpgen.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qp_gen_blueprints")
public class BluePrint extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("total_questions")
  private Long totalQuestions;

  @JsonProperty("total_marks")
  private Long totalMarks;

  @OneToMany(
      fetch = FetchType.LAZY,
      cascade = CascadeType.ALL,
      mappedBy = "bluePrint",
      orphanRemoval = true)
  private List<BluePrintSections> bluePrintSections;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "bluePrint", cascade = CascadeType.ALL)
  private List<QPGenPro> qpGenPros;
}
