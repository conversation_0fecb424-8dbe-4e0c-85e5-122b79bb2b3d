package com.wexl.retail.curriculum.service;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.Icon;
import com.wexl.retail.model.ContentProvider;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.OrgSettings;
import com.wexl.retail.model.Subject;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CurriculumService {

  @Autowired private StrapiService strapiService;
  @Autowired private ContentService contentService;

  @Value("${app.curriculum.eduBoards}")
  private final List<String> eduBoards;

  public List<EduBoard> getBoardsHierarchy(final String org) {
    final var orgSettings = contentService.getOrgSettings(org);
    if (Objects.isNull(orgSettings)) {
      return Collections.emptyList();
    }
    if (org.equals(WEXL_INTERNAL)) {
      var internalBoards =
          orgSettings.getBoards().stream()
              .filter(board -> eduBoards.contains(board.getSlug()))
              .toList();
      var requiredBoards = OrgSettings.builder().boards(internalBoards).build();
      return getBoardsHierarchy(requiredBoards);
    }
    return getBoardsHierarchy(orgSettings);
  }

  public EduBoard getBoardByGrade(String orgSlug, String gradeSlug) {
    try {
      List<EduBoard> boardsHierarchy = getBoardsHierarchy(orgSlug);
      Optional<EduBoard> optionalBoard =
          boardsHierarchy.stream()
              .filter(
                  b -> {
                    Optional<com.wexl.retail.model.Grade> first =
                        b.getGrades().stream()
                            .filter(g -> g.getSlug().equals(gradeSlug))
                            .findFirst();
                    return first.isPresent();
                  })
              .findFirst();

      if (optionalBoard.isPresent()) {
        return optionalBoard.get();
      }
    } catch (Exception e) {
      log.error(
          "Invalid organization configuration for orgSlug ["
              + orgSlug
              + "] and grade ["
              + gradeSlug
              + "]",
          e);
    }

    throw new ApiException(
        InternalErrorCodes.INVALID_REQUEST,
        "error.organization.grade",
        new String[] {orgSlug, gradeSlug});
  }

  private List<EduBoard> getBoardsHierarchy(OrgSettings orgSettings) {

    final var boardsHierarchy = orgSettings.getBoards();
    if (Objects.isNull(boardsHierarchy)) {
      return Collections.emptyList();
    }

    final var eduBoardsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllBoards());
    final var gradesMapBySlug =
        strapiService.transformGradeEntityToMapBySlug(strapiService.getAllGrades());
    final var subjectsMapBySlug =
        strapiService.transformStrapiEntityToMapBySlug(strapiService.getAllSubjects());

    boardsHierarchy.parallelStream()
        .forEach(
            board -> {
              mapEduBoard(board, eduBoardsMapBySlug);
              if (Objects.nonNull(board.getGrades())) {
                board.getGrades().parallelStream()
                    .forEach(
                        grade -> {
                          mapGrade(grade, gradesMapBySlug);
                          if (Objects.nonNull(grade.getSubjects())) {
                            mapSubjects(grade.getSubjects(), subjectsMapBySlug);
                          }
                        });
                board
                    .getGrades()
                    .sort(Comparator.comparing(com.wexl.retail.model.Grade::getOrderId));
              }
            });

    return boardsHierarchy;
  }

  private void mapEduBoard(final EduBoard eduBoard, final Map<String, Entity> entitiesMapBySlug) {
    try {
      eduBoard.setId(entitiesMapBySlug.get(eduBoard.getSlug()).getId());
      if (StringUtils.isBlank(eduBoard.getName())) {
        eduBoard.setName(entitiesMapBySlug.get(eduBoard.getSlug()).getAssetName());
      }
    } catch (Exception e) {
      log.error("Failed to fetch board:" + eduBoard.getSlug(), e);
    }
  }

  private void mapGrade(
      final com.wexl.retail.model.Grade grade, final Map<String, Grade> entitiesMapBySlug) {
    try {
      grade.setId(entitiesMapBySlug.get(grade.getSlug()).getId());
      grade.setOrderId(entitiesMapBySlug.get(grade.getSlug()).getOrder());
      if (StringUtils.isBlank(grade.getName())) {
        grade.setName(entitiesMapBySlug.get(grade.getSlug()).getName());
      }
    } catch (Exception e) {
      log.error("Failed to fetch grade:" + grade.getSlug(), e);
    }
  }

  private void mapSubjects(
      final List<Subject> subjects, final Map<String, Entity> subjectsMapBySlug) {
    subjects.parallelStream()
        .forEach(
            subject -> {
              try {
                subject.setId(subjectsMapBySlug.get(subject.getSlug()).getId());
                subject.setIcons(
                    subjectsMapBySlug.get(subject.getSlug()).getIcons().parallelStream()
                        .map(Icon::getUrl)
                        .toList());
                if (StringUtils.isBlank(subject.getName())) {
                  subject.setName(subjectsMapBySlug.get(subject.getSlug()).getName());
                }
              } catch (Exception e) {
                log.error("Failed to fetch subject:" + subject.getSlug(), e);
              }
            });
    subjects.sort(Comparator.comparing(Subject::getName));
  }

  public List<com.wexl.retail.model.Grade> getGradesByBoardId(final String org, final int boardId) {

    return Optional.ofNullable(getBoardsHierarchy(org))
        .orElse(Collections.emptyList())
        .parallelStream()
        .filter(board -> board.getId() == boardId)
        .findFirst()
        .flatMap(eduBoard -> Optional.ofNullable(eduBoard.getGrades()))
        .orElse(Collections.emptyList());
  }

  public List<Subject> getSubjectsByBoardIdAndGradeId(
      final String org, final int boardId, final int gradeId) {

    return Optional.ofNullable(getGradesByBoardId(org, boardId))
        .orElse(Collections.emptyList())
        .parallelStream()
        .filter(grade -> grade.getId() == gradeId)
        .findFirst()
        .flatMap(grade -> Optional.ofNullable(grade.getSubjects()))
        .orElse(Collections.emptyList());
  }

  public List<ContentProvider> getContentProviders(String org, String contentType) {

    return Optional.ofNullable(strapiService.getOrganizationBySlug(org).getSettings())
        .flatMap(orgSettings -> Optional.ofNullable(orgSettings.getContentProviders()))
        .orElse(Collections.emptyList())
        .stream()
        .filter(contentProvider -> contentProvider.getType().equals(contentType))
        .toList();
  }

  public List<EduBoard> getProvidersHierarchy(String org, String providerSlug, String contentType) {
    final var providerSettings = strapiService.getOrganizationBySlug(providerSlug).getSettings();
    if (Objects.isNull(providerSettings)) {
      return Collections.emptyList();
    }

    final var contentRecipients = providerSettings.getContentRecipients();
    if (Objects.isNull(contentRecipients)) {
      return Collections.emptyList();
    }

    Optional<ContentProvider> recipient =
        contentRecipients.stream()
            .filter(
                contentRecipient ->
                    contentRecipient.getType().equals(contentType)
                        && contentRecipient.getOrganization().equals(org))
            .findFirst();
    if (recipient.isEmpty()) {
      return Collections.emptyList();
    }

    return getBoardsHierarchy(providerSettings);
  }
}
