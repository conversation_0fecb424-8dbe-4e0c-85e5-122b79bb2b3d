package com.wexl.retail.feedback.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record TeacherFeedbackDto() {

  @Builder
  public record Counts(
      @JsonProperty("tutor_name") String name,
      @JsonProperty("tutor_id") Long id,
      @JsonProperty("assignment_feedback_count") Long assignmentFeedbackCount,
      @JsonProperty("assignment_feedback_pending_count") Long assignmentFeedbackPendingCount,
      @JsonProperty("test_feedback_count") Long testFeedbackCount,
      @JsonProperty("test_feedback_pending_count") Long testFeedbackPendingCount,
      @JsonProperty("subject_count") Long subjectCount,
      @JsonProperty("overall_count") Long overallCount) {}

  @Builder
  public record Data(
      @JsonProperty("feedback_message") String feedbackMessage,
      @JsonProperty("classroom_name") String classroomName,
      @JsonProperty("activity_name") String activityName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subtopic_name") String subtopicName,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_authId") String studentAuthId,
      @JsonProperty("taskInst_id") Long taskInstId,
      @JsonProperty("message_templates") List<MessageTemplateResponse> feedBackMessageTemplates,
      @JsonProperty("feedback_id") Long feedbackId) {}

  @Builder
  public record AssignmentCount(
      @JsonProperty("assignment_feedback_count") Long assignmentFeedbackCount,
      @JsonProperty("assignment_feedback_pending_count") Long assignmentFeedbackPendingCount) {}

  @Builder
  public record MessageTemplateResponse(Long id, String message) {}

  @Builder
  public record TestCount(
      @JsonProperty("test_feedback_count") Long testFeedbackCount,
      @JsonProperty("test_feedback_pending_count") Long testFeedbackPendingCount) {}

  @Builder
  public record OverAllCount(
      @JsonProperty("overall_feedback_count") Long overAllFeedbackCount,
      @JsonProperty("overall_feedback_pending_count") Long overAllFeedbackPendingCount) {}

  @Builder
  public record TeacherFeedbackSummary(
      @JsonProperty("summary") Summary summary, @JsonProperty("data") List<Data> data) {}

  @Builder
  public record Summary(
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      @JsonProperty("tutor_name") String name,
      @JsonProperty("tutor_id") Long id,
      @JsonProperty("total_feedback_given") Long totalFeedbackGiven,
      @JsonProperty("pending_feedback") Long pendingFeedback,
      @JsonProperty("feedback_type") String feedbackType) {}
}
