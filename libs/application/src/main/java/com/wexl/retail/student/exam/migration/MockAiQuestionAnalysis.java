package com.wexl.retail.student.exam.migration;

import com.wexl.retail.ai.AiQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponse;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptAnswerContent;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptQuestionContent;
import java.util.List;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(100)
public class MockAiQuestionAnalysis implements AiQuestionAnalysis {

  @Override
  public AiQuestionAnalysisResponseList analyzeQuestions(
      List<PromptQuestionContent> promptQuestionContents,
      List<PromptAnswerContent> promptAnswerContents) {
    AiQuestionAnalysisResponse response1 =
        new AiQuestionAnalysisResponse(9465576L, 1, "Did a good job");
    AiQuestionAnalysisResponse response2 =
        new AiQuestionAnalysisResponse(5678L, 1, "Did a good job");
    return new AiQuestionAnalysisResponseList(List.of(response1, response2));
  }
}
