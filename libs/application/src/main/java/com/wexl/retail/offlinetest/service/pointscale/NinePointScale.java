package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class NinePointScale implements PointScale {
  @Override
  public String evaluate(int marks) {
    if (marks >= 90) {
      return "A*";
    } else if (marks >= 80) {
      return "A";
    } else if (marks >= 70) {
      return "B";
    } else if (marks >= 60) {
      return "C";
    } else if (marks >= 50) {
      return "D";
    } else if (marks >= 40) {
      return "E";
    } else if (marks >= 30) {
      return "F";
    } else if (marks >= 20) {
      return "G";
    } else {
      return "U";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(90)) >= 0) {
      return "A*";
    } else if (marks.compareTo(new BigDecimal(80)) >= 0) {
      return "A";
    } else if (marks.compareTo(new BigDecimal(70)) >= 0) {
      return "B";
    } else if (marks.compareTo(new BigDecimal(60)) >= 0) {
      return "C";
    } else if (marks.compareTo(new BigDecimal(50)) >= 0) {
      return "D";
    } else if (marks.compareTo(new BigDecimal(40)) >= 0) {
      return "E";
    } else if (marks.compareTo(new BigDecimal(30)) >= 0) {
      return "F";
    } else if (marks.compareTo(new BigDecimal(20)) >= 0) {
      return "G";
    } else {
      return "U";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "A*")) {
      return BigDecimal.valueOf(90L);
    } else if (Objects.equals(grade, "A")) {
      return BigDecimal.valueOf(80L);
    } else if (Objects.equals(grade, "B")) {
      return BigDecimal.valueOf(70L);
    } else if (Objects.equals(grade, "C")) {
      return BigDecimal.valueOf(60L);
    } else if (Objects.equals(grade, "D")) {
      return BigDecimal.valueOf(50L);
    } else if (Objects.equals(grade, "E")) {
      return BigDecimal.valueOf(40L);
    } else if (Objects.equals(grade, "F")) {
      return BigDecimal.valueOf(30L);
    } else if (Objects.equals(grade, "G")) {
      return BigDecimal.valueOf(20L);
    } else {
      return BigDecimal.valueOf(10L);
    }
  }

  @Override
  public final List<String> getGradesByPointScale(String pointScale) {
    return List.of("A*", "A", "B", "C", "D", "E", "F", "G", "U");
  }
}
