package com.wexl.retail.test.school.repository;

import com.wexl.retail.metrics.handler.RangeByExam;
import com.wexl.retail.metrics.handler.TestDetailsInterface;
import com.wexl.retail.model.User;
import com.wexl.retail.test.assignment.dto.AssignmentsResponse;
import com.wexl.retail.test.schedule.dto.TestSchedulesReportInterface;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.TestDefinitionSummary;
import com.wexl.retail.test.school.dto.TestDetailsUser;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TestDefinitionRepository extends JpaRepository<TestDefinition, Long> {

  Optional<TestDefinition> findByIdAndOrganization(long id, String organization);

  @Query(
      value =
          """
              SELECT td.*
                           from test_definitions td
                           left outer join test_schedule ts on td.id = ts.test_definition_id
                           where td.deleted_at is null and td.active = true and ts.id is null
                           and td.organization = :orgSlug
                           and  (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                           and (cast((:subjectSlug) as varChar) is null or td.subject_slug in (:subjectSlug))
                           order by td.created_at desc limit :limit
                           """,
      nativeQuery = true)
  List<TestDefinition> getTeacherTestsThatAreNotScheduled(
      String orgSlug, int limit, List<String> gradeSlug, List<String> subjectSlug);

  @Query(
      value =
          """
                      SELECT td.*
                                   from test_definitions td
                                   inner join test_schedule ts on td.id = ts.test_definition_id
                                   where td.deleted_at is null and td.active = true
                                   and td.organization = :orgSlug
                                   and  (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                                   and (cast((:subjectSlug) as varChar) is null or td.subject_slug in (:subjectSlug))
                                   order by td.created_at desc limit :limit
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTeacherTestsThatAreScheduled(
      String orgSlug, int limit, List<String> gradeSlug, List<String> subjectSlug);

  @Query(
      value =
          """
                      SELECT td.*
                                   from test_definitions td
                                   left outer join test_schedule ts on td.id = ts.test_definition_id
                                   where td.deleted_at is null and td.active = true and ts.id is null
                                   and td.organization = :orgSlug and (td.test_name IS NULL OR LOWER(td.test_name) LIKE '%' || LOWER(:testName) || '%')
                                   and  (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                                   and (cast((:subjectSlug) as varChar) is null or td.subject_slug in (:subjectSlug))
                                   order by td.created_at desc limit :limit
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTeacherTestsThatAreNotScheduled(
      String orgSlug, int limit, List<String> gradeSlug, List<String> subjectSlug, String testName);

  List<TestDefinition> findAllByTeacherAndOrganizationAndDeletedAtIsNullOrderByCreatedAtDesc(
      User teacherId, String orgSlug);

  @Query(
      value =
          """
                          SELECT td.* FROM test_definitions td
                                   WHERE td.teacher_id = :teacherId
                                   AND td.organization = :orgSlug
                                   and (td.test_name IS NULL OR LOWER(td.test_name) LIKE '%' || LOWER(:testName) || '%')
                                   AND td.deleted_at IS NULL
                                  ORDER BY td.created_at DESC
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTeacherAndOrgAndTestNameAndDeletedAtIsNullOrderByCreatedAtDesc(
      Long teacherId, String orgSlug, String testName);

  @Query(
      value =
          """
              SELECT td.*
                           from test_definitions td
                           left outer join test_schedule ts on td.id = ts.test_definition_id
                           where td.deleted_at is null and td.active = true and ts.id is not null
                            and  (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                           and (cast((:subjectSlug) as varChar) is null or td.subject_slug in (:subjectSlug))
                           and  ts.org_slug = :orgSlug order by td.created_at desc limit :limit
                           """,
      nativeQuery = true)
  List<TestDefinition> getTeacherScheduledTests(
      String orgSlug, int limit, List<String> gradeSlug, List<String> subjectSlug);

  @Query(
      value =
          """
              SELECT td.*
                           from test_definitions td
                           left outer join test_schedule ts on td.id = ts.test_definition_id
                           where td.deleted_at is null and td.active = true and ts.id is null and td.teacher_id= :teacherUserId
                           and td.organization = :orgSlug order by td.created_at desc limit :limit
                           """,
      nativeQuery = true)
  List<TestDefinition> getTeacherTestsThatAreNotScheduled(
      String orgSlug, Long teacherUserId, int limit);

  @Query(
      value =
          """
              SELECT td.*
                           from test_definitions td
                           inner join test_schedule ts on td.id = ts.test_definition_id
                           where td.deleted_at is null and td.active = true and td.teacher_id= :teacherUserId
                           and  td.organization = :orgSlug order by td.created_at desc limit :limit
                           """,
      nativeQuery = true)
  List<TestDefinition> getTeacherScheduledTests(String orgSlug, Long teacherUserId, int limit);

  @Query(
      value =
          """
                      SELECT td.*
                                   from test_definitions td
                                   inner join test_schedule ts on td.id = ts.test_definition_id
                                   where td.deleted_at is null and td.active = true and td.teacher_id= :teacherUserId
                                   and (td.test_name IS NULL OR LOWER(td.test_name) LIKE '%' || LOWER(:testName) || '%')
                                   and  td.organization = :orgSlug order by td.created_at desc limit :limit
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTeacherScheduledTests(
      String orgSlug, Long teacherUserId, int limit, String testName);

  @Query(
      value =
          """
              select count(*) from test_definitions \
              where type = :testType and organization = :org\
              """,
      nativeQuery = true)
  Integer countByTestType(String org, String testType);

  @Query(
      value =
          """
              select count(*) from test_definitions
              where type in ('SCHOOL_TEST','MOCK_TEST') and organization = :org
              """,
      nativeQuery = true)
  Integer noOfTestsByOrg(String org);

  @Query(
      value =
          """
              with test_definition_subtopic as (
              select  jsonb_array_elements(metadata -> 'subtopics')->>'slug' as "subtopic",* from test_definitions
              where organization=:orgSlug and deleted_at is null
              and type=:testType)

              select * from test_definition_subtopic
              where subtopic=:subtopicSlug
              order by id desc
              """,
      nativeQuery = true)
  List<TestDefinition> getAllAssignments(String testType, String orgSlug, String subtopicSlug);

  @Query(
      value =
          """
              select  e.student_id as studentId,e.id as examId,td.id as testDefinitionId,u.first_name as name,e.subject_name as subjectName,e.subject_slug as subjectSlug,e.created_at as assignmentDate,
                 e.chapter_name as chapterName,e.chapter_slug as chapterSlug,e.subtopic_name as subtopicName,e.subtopic_slug as subtopicSlug,CASE WHEN  e.corrected is true THEN 'Corrected' ELSE 'NotCorrected' END AS status,
                 e.marks_scored AS marksScored,e.total_marks AS totalMarks,case when e.exam_type=1 then 'Classroom Practice Exam' when e.exam_type=2 then 'Classroom Test' when e.exam_type in (4,5) then 'School Test' when e.exam_type=6 then 'worksheet'  else 'Assignment' end as activityType,
                 e.updated_at as "lastUpdated"
                  from test_definitions td
                  join exams e on td.id=e.test_definition_id
                  join students s on s.id = e.student_id
                  join users u on u.id=s.user_id
                  where td.organization=:orgSlug and e.deleted_at is null and e.is_completed is true  and e.created_at >=:fromDate and
                                                           e.created_at <=:toDate and
                                                          ((e.exam_type in (1,2,4,5,6,7,9) and td.teacher_id=:teacherIntId) or
                                                          (e.exam_type= 7 and e.task_id in :taskIds))
                                                          order by e.created_at desc""",
      nativeQuery = true)
  List<AssignmentsResponse> getAllAssignmentStatus(
      String orgSlug, Long teacherIntId, LocalDate fromDate, LocalDate toDate, List<Long> taskIds);

  @Query(
      value =
          """
              select count(*) from
                (select CASE WHEN  e.corrected is true THEN 'Corrected' ELSE 'Pending' END AS status
                  from test_definitions t
                  inner join exams e on e.test_definition_id=t.id
                  inner join students s on s.id = e.student_id
                  inner join users u on u.id=s.user_id
                  inner join tasks tk on tk.id  = e.task_id
                where t.organization=:orgSlug and e.is_completed is true and e.deleted_at is null and
                             (e.exam_type in (7) and t.teacher_id=:teacherIntId) or
                              (e.exam_type= 7 and e.task_id in :taskIds)) t
                          where status=:status""",
      nativeQuery = true)
  Integer getAssignmentCountByStatus(
      String orgSlug, Long teacherIntId, String status, List<Long> taskIds);

  List<TestDefinition>
      findTop100ByGradeSlugAndOrganizationAndTypeAndDeletedAtIsNullOrderByCreatedAtDesc(
          String grade, String orgSlug, TestType type);

  Optional<TestDefinition> findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
      String elpSlug, String orgSlug);

  Optional<TestDefinition>
      findTop1ByTestNameAndOrganizationAndDeletedAtIsNullAndPublishedAtIsNotNull(
          String elpSlug, String orgSlug);

  Optional<TestDefinition> findTop1ByTestNameAndOrganization(String elpSlug, String orgSlug);

  List<TestDefinition> findAllByIdIn(List<Long> ids);

  List<TestDefinition>
      findTop500ByGradeSlugAndOrganizationAndTypeAndDeletedAtIsNullAndPublishedAtNotNullOrderByCreatedAtDesc(
          String grade, String orgSlug, TestType type);

  @Query(
      value =
          """

               select distinct td.test_name as testName,td.grade_slug as gradeSlug,td.organization as organization,
               td.type,jsonb_array_elements_text(ts.metadata -> 'sections') as sections,
               ts.id as testScheduleId,td.id as testDefinitionId,
               case when td.category = 0 then 'EAMCET'
                    when td.category = 1 then 'NEET'
                    when td.category = 2 then 'IIT24'
                    when td.category = 5 then 'STANDARD'
                    when td.category = 6 then 'CUSTOM'
                    when td.category = 9 then 'IIT' end as category
               ,ts.start_date as Date
               from test_definitions td  join test_schedule ts  on td.id = ts.test_definition_id
               where td.category is not null and td.category not in (3)
               and td.organization  =:orgSlug order by td.id desc
              """,
      nativeQuery = true)
  List<TestDefinitionExamInfo> getCompetitiveExams(String orgSlug);

  @Query(
      value =
          """
              select td.* from test_definitions td where (cast((:grade) as varChar) is null or td.grade_slug in (:grade))
              and organization in (:orgSlug)
              and "type" in (:type)
              and deleted_at is null
              and published_at notnull
              order by created_at desc
              limit :limit""",
      nativeQuery = true)
  List<TestDefinition> getAllByGradeAndOrgSlug(
      String grade, String orgSlug, String type, int limit);

  @Query(
      value =
          """
                          select * from test_definitions td where type = :type and organization  = 'wexl-internal'
                           and test_name like 'BET-%' and published_at is not null and td.deleted_at is null
                           and td.category = 7
                           order by id desc
                           """,
      nativeQuery = true)
  List<TestDefinition> getTestsByGradeAndOrgSlugAndType(String type);

  @Query(
      value =
          """
                          select td.test_name as testName,o."name" as orgName,o.slug as orgSlug,count(distinct(s.id)) as studentCount,
                          sum(CASE WHEN tss.status = 'COMPLETED' THEN 1 ELSE 0 END) as studentAttemptedCount
                          from test_definitions td
                          join test_schedule ts on ts.test_definition_id = td.id
                          join test_schedule_student tss on tss.schedule_test_id = ts.id
                          join users u on u.id = tss.student_id
                          join orgs o on o.slug = u.organization
                          left join students s on s.user_id = u.id
                          where td.id = (:testDefinitionId)
                          and (cast(:orgSlug as varchar) is null or o.slug = :orgSlug)
                          group by o."name",u.organization,td.id,td.test_name,o.slug
                          """,
      nativeQuery = true)
  List<TestDetailsInterface> getTestInsights(Long testDefinitionId, String orgSlug);

  @Query(
      value =
          """
                  SELECT ts.org_slug AS orgName,
                  sum(CASE WHEN e.total_marks > 0 AND (e.marks_scored / e.total_marks) * 100 > 80
                  THEN 1 END) AS greaterThan80,
                  sum(CASE WHEN e.total_marks > 0 AND (e.marks_scored / e.total_marks) * 100 BETWEEN 70 AND 80
                  THEN 1 END) AS range70to80,
                  sum(CASE WHEN e.total_marks > 0 AND (e.marks_scored / e.total_marks) * 100 BETWEEN 50 AND 70
                  THEN 1 END) AS range50to70,
                  sum(CASE WHEN e.total_marks > 0 AND (e.marks_scored / e.total_marks) * 100 BETWEEN 30 AND 50
                  THEN 1 END) AS range30to50,
                  sum(CASE WHEN e.total_marks > 0 AND (e.marks_scored / e.total_marks) * 100 < 30
                  THEN 1 END) AS lesserThan30
                  FROM  test_definitions td
                  join test_schedule ts on ts.test_definition_id = td.id
                  JOIN exams e ON e.schedule_test_id = ts.id
                  WHERE td.id = :testDefinitionId
                  group by ts.org_slug
                          """,
      nativeQuery = true)
  List<RangeByExam> getRangePercent(Long testDefinitionId);

  @Query(
      value =
          """
                  select
                  ts.id as scheduleId,td.test_name as testName,DATE(td.created_at) as date,u.first_name as teacherName,
                  o."name" as scheduledBy,s."name" as sectionName,td.id as testDefinitionId,s.grade_slug as gradeSlug,s.grade_name as gradeName
                  from test_definitions td
                  join test_schedule ts on ts.test_definition_id = td.id
                  join users u ON u.id = ts.teacher_id
                  join orgs o on o.slug = ts.org_slug
                  join sections s on s.organization = o.slug
                  where ts.org_slug in (:orgSlugs) and
                  td.grade_slug in (:gradeSlugs) and CAST(s."uuid" AS VARCHAR) in (:sectionUuid)
                  and td.board_slug in (:boardSlug) and to_char(td.created_at,'yyyy-MM-dd') between (:fDate) and (:tDate)
                  """,
      nativeQuery = true)
  List<TestSchedulesReportInterface> getTestDetails(
      List<String> orgSlugs,
      List<String> boardSlug,
      List<String> gradeSlugs,
      List<String> sectionUuid,
      String fDate,
      String tDate);

  @Query(
      value =
          """
                      select * from test_definitions td where type = 'MOCK_TEST'
                       and organization = :orgSlug and subject_slug  = ''
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTestDefinitionsDataForMigration(String orgSlug);

  @Query(
      value =
          """
                    select tq.subject_slug from test_definitions td join test_definition_sections tds on td.id = tds.test_definition_id
                       join  test_questions tq on tds.id = tq.test_definition_section_id
                       where td.id = :testDefId and td.subject_slug  = '' and td.type = 'MOCK_TEST' order by tq.id asc
                                   """,
      nativeQuery = true)
  List<String> getSubjectSlugForMigration(Long testDefId);

  List<TestDefinition> findAllByOrganizationAndDeletedAtIsNullOrderByCreatedAtDesc(
      String orgSlug, Pageable pageable);

  @Query(
      value =
          """
                      SELECT td.*
                                   from test_definitions td
                                   where td.deleted_at is null and td.active = true
                                   and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                                   and (cast((:subjectSlug) as varChar) is null or td.subject_slug in (:subjectSlug))
                                   and td.organization = :orgSlug order by td.created_at desc limit :limit
                                   """,
      nativeQuery = true)
  List<TestDefinition> getTestsByTeacherSubjects(
      String orgSlug, int limit, List<String> gradeSlug, List<String> subjectSlug);

  @Query(
      value =
          """
                          SELECT tds.name as sectionName,tds.id as sectionId,
                              chapter_name as chapterName,
                              chapter_slug as chapterSlug,
                              category as category,
                              complexity as complexity, type,
                              COUNT(CASE WHEN marks = 1 THEN 1 END) AS OneMarkCount,
                              COUNT(CASE WHEN marks = 2 THEN 1 END) AS TwoMarkCount,
                              COUNT(CASE WHEN marks = 3 THEN 1 END) AS ThreeMarkCount,
                              COUNT(CASE WHEN marks = 4 THEN 1 END) AS FourMarkCount,
                              COUNT(CASE WHEN marks = 5 THEN 1 END) AS FiveMarkCount
                          FROM
                             test_definition_sections tds join test_questions tq
                              on tq.test_definition_section_id  = tds.id
                          WHERE
                              tds.test_definition_id = :testDefId
                          GROUP BY
                              chapter_name, chapter_slug, category, complexity,tds.id,type

                                           """,
      nativeQuery = true)
  List<TestDefinitionSummary> getTestDefinitionSummary(Long testDefId);

  @Query(
      value =
          """
                  select td.* from test_definitions td
                  where td.type = :type and organization  = :orgSlug
                  and td.test_name like :testName and published_at is not null and td.deleted_at is null
                  order by td.id desc
                                   """,
      nativeQuery = true)
  List<TestDefinition> getBetTests(String testName, String type, String orgSlug);

  @Query(
      value =
          """
                          select distinct td.* from test_definitions td
                          left join test_schedule ts on ts.test_definition_id = td.id
                          left join test_schedule_student tss on tss.schedule_test_id = ts.id
                          where td.type = :type and  organization  = :orgSlug
                          and td.test_name like :testName and published_at is not null and td.deleted_at is null
                          and tss.status in ('COMPLETED','SUBMITTED') and (cast((:userId) as varChar) is null or tss.student_id in (:userId))
                          order by td.id desc
                          """,
      nativeQuery = true)
  List<TestDefinition> studentWrittenTest(
      String testName, String type, String orgSlug, Long userId);

  @Query(
      value =
          """
                  select * from test_definitions td
                  where test_name ilike :testName and organization = :orgSlug and board_slug = :boardSlug
                  and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug)) and published_at is not null
                  order by td.id desc
                          """,
      nativeQuery = true)
  List<TestDefinition> getTestDefinitionsByOrgAndBoard(
      String orgSlug, String boardSlug, String testName, String gradeSlug);

  @Query(
      value =
          """
                  select td.id as testDefinitionId,td.test_name as testName,td.organization as orgSlug ,td.total_marks as totalMarks,td.no_of_questions as totalQuestions,tss.status as status,
                  e.id as examId,e.end_time as completedDate,ts.id as scheduleId from test_definitions td
                          left join test_schedule ts on ts.test_definition_id = td.id
                          join test_schedule_student tss on tss.schedule_test_id = ts.id
                          join exams e on e.schedule_test_id = ts.id
                          where td.test_name ilike :testName and td.organization = :orgSlug and board_slug = :boardSlug
                          and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug)) and published_at is not null
                          and tss.student_id = :userId
                          union
                          select td.id as testDefinitionId,td.test_name as testName,td.organization as orgSlug ,total_marks as totalMarks,no_of_questions as totalQuestions,'PENDING',null,null,null from test_definitions td
                          where td.test_name ilike :testName and td.organization = :orgSlug and board_slug = :boardSlug
                          and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug)) and published_at is not null
                          """,
      nativeQuery = true)
  List<TestDetailsUser> getTestDefinitionsByOrgAndBoardAndUser(
      String orgSlug, String boardSlug, String testName, String gradeSlug, Long userId);

  @Query(
      value =
          """
                  select td.* from test_definitions td
                  join test_schedule ts on ts.test_definition_id = td.id
                  join test_schedule_student tss on tss.schedule_test_id  = ts.id
                  where td.test_name ilike :testName and td.organization = :orgSlug  and tss.student_id = :userId and td.published_at is not null
                  """,
      nativeQuery = true)
  List<TestDefinition> getUserTestDefForBet(String testName, String orgSlug, Long userId);

  @Query(
      value =
          """
                  select * from test_definitions td
                  where td.test_name ilike :testName and td.organization = :orgSlug and (cast((:testDefId) as varChar) is null or td.id not in (:testDefId))
                  """,
      nativeQuery = true)
  List<TestDefinition> getTestDefinitionByTestNameAndOrgSlug(
      String testName, String orgSlug, List<Long> testDefId);

  TestDefinition findByTestName(String testName);

  List<TestDefinition> findByOrganizationAndBoardSlug(String orgSlug, String boardSlug);

  TestDefinition findByTestNameAndBoardSlugAndOrganization(
      String testName, String boardSlug, String orgSlug);
}
