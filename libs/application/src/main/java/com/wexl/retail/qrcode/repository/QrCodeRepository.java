package com.wexl.retail.qrcode.repository;

import com.wexl.retail.qrcode.domain.QrCode;
import jakarta.transaction.Transactional;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface QrCodeRepository extends JpaRepository<QrCode, Long> {

  Optional<QrCode> findFirstByUuid(String uuid);

  @Modifying
  @Transactional
  @Query(value = "update qr_code set status = :status where uuid =:uuid ", nativeQuery = true)
  void updateQrCodeStatus(String uuid, String status);
}
