package com.wexl.retail.classroom.group.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.classroom.core.dto.ClassroomRequest;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleResponse;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.admin.teacher.TeacherResponse;
import java.util.List;
import lombok.Builder;

public class GroupClassroomDto {

  public record GroupClassroomRequest(
      List<OrgClassroomRequest> orgClassroomRequests, ClassroomRequest classroomRequest) {}

  public record OrgClassroomRequest(String orgSlug, String gradeSlug) {}

  @Builder
  public record GroupClassroomsResponse(
      @JsonProperty("classroom_id") Long id,
      @JsonProperty("classroom_name") String name,
      List<String> orgs,
      List<String> teachers,
      @JsonProperty("student_count") Long studentCount,
      @JsonProperty("schedule_count") Long scheduleCount) {}

  @Builder
  public record GroupClassroomDetails(
      @JsonProperty("classroom_name") String classroomName,
      List<StudentResponse> students,
      List<TeacherResponse> teachers,
      List<ClassroomScheduleResponse> schedules) {}
}
