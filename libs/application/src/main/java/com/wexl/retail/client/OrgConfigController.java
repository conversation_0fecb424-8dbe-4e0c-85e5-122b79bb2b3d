package com.wexl.retail.client;

import com.wexl.retail.client.service.LocaleSelectorService;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.model.UiConfig;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.util.StrapiService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
public class OrgConfigController {

  @Autowired private final StrapiService strapiService;
  @Autowired private final LocaleSelectorService localeSelectorService;
  private final OrganizationRepository organizationRepository;
  private final OrgSettingsService orgSettingsService;

  @GetMapping("/orgs/{org}/settings")
  public ResponseEntity<UiConfig> getUiConfig(final @PathVariable("org") String org) {
    var uiConfig = orgSettingsService.getUiConfigByOrgSlug(org);
    return ResponseEntity.ok().body(uiConfig);
  }

  @GetMapping(value = "/public/resources/i18n", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<String> getLanguageFile(@RequestParam(defaultValue = "en") String lang) {
    return ResponseEntity.ok().body(localeSelectorService.getSelectedLocaleFile(lang));
  }
}
