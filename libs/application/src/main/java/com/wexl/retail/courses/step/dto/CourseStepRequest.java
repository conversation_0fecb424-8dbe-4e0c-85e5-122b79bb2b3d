package com.wexl.retail.courses.step.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CourseStepRequest {

  private String title;

  @JsonProperty("item_type")
  private String itemType;

  @JsonProperty("item_pk")
  private String itemPK;

  @JsonProperty("attributes")
  private List<CourseStepRequestAttribute> attributes;

  @Data
  public static class CourseStepRequestAttribute {

    private String key;
    private String value;
  }
}
