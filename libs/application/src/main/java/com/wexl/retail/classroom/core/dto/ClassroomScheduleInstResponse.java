package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassroomScheduleInstResponse {

  @JsonProperty("classroom_name")
  private String classroomName;

  @JsonProperty("classroom_id")
  private Long classroomId;

  @JsonProperty("schedule_id")
  private Long scheduleId;

  @JsonProperty("schedule_inst_id")
  private Long scheduleInstId;

  private String title;

  @JsonProperty("start_time")
  private Long startTime;

  @JsonProperty("end_time")
  private Long endTime;

  @JsonProperty("students_count")
  private Integer studentsCount;

  @JsonProperty("status")
  private String status;

  @JsonProperty("meeting_link")
  private String link;

  @JsonProperty("meeting_room_id")
  private Long meetingRoomId;

  @JsonProperty("meeting_room_name")
  private String meetingRoomName;

  @JsonProperty("start_date")
  private Long startDate;

  @JsonProperty("expiry_date")
  private Long expiryDate;

  @JsonProperty("day_of_week")
  private String dayOfWeek;

  @JsonProperty("created_date")
  private Long createdDate;

  @JsonProperty("task_count")
  private Integer taskCount;

  @JsonProperty("teacher_names")
  private List<String> teacherNames;

  private String organization;

  @JsonProperty("is_attendance_marked")
  private boolean isAttendanceMarked;
}
