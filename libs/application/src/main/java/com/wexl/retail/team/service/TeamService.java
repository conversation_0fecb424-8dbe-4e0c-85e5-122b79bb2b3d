package com.wexl.retail.team.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.enrollment.model.CourseScheduleInst;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleInstRepository;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.team.domain.Team;
import com.wexl.retail.team.dto.TeamDetails;
import com.wexl.retail.team.dto.TeamQueryResult;
import com.wexl.retail.team.dto.TeamRequest;
import com.wexl.retail.team.dto.TeamResponse;
import com.wexl.retail.team.event.TeamCourseScheduleEventPublisher;
import com.wexl.retail.team.event.TeamCourseScheduleMetaData;
import com.wexl.retail.team.repository.TeamRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class TeamService {
  @Autowired private TeamRepository teamRepository;
  @Autowired private CourseScheduleRepository courseScheduleRepository;
  @Autowired private OrganizationRepository organizationRepository;
  @Autowired private StudentRepository studentRepository;
  @Autowired private UserRepository userRepository;
  @Autowired private AuthService authService;

  @Autowired private TeamCourseScheduleEventPublisher eventPublisher;
  @Autowired private CourseScheduleInstRepository courseScheduleInstRepository;

  public void createTeam(String orgSlug, TeamRequest teamRequest) {

    Organization org = organizationRepository.findBySlug(orgSlug);
    String name = teamRequest.getName();
    var teamName = teamRepository.findByNameAndOrgSlug(name, orgSlug);
    if (teamName != null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeamName.Exists");
    }

    Team teams = new Team();

    teams.setName(teamRequest.getName());
    teams.setStatus(Boolean.TRUE);
    teams.setOrg(org);
    teams.setOrgSlug(orgSlug);
    teams.setStudents(buildStudentId(teamRequest.getStudentId()));
    teamRepository.save(teams);
  }

  public List<Student> buildStudentId(List<Long> studentIds) {
    if (studentIds.isEmpty()) {
      return new ArrayList<>();
    }
    return studentRepository.findStudentListById(studentIds);
  }

  public List<TeamResponse> getAllTeams(String orgSlug) {
    List<TeamQueryResult> teamQueryResults = teamRepository.getTeamDetailsOrgWise(orgSlug);
    return buildTeamResponse(teamQueryResults);
  }

  public List<TeamResponse> buildTeamResponse(List<TeamQueryResult> teamQueryResult) {
    List<TeamResponse> teamResponses = new ArrayList<>();
    teamQueryResult.forEach(
        team ->
            teamResponses.add(
                TeamResponse.builder()
                    .teamId(team.getTeamId())
                    .noOfTeamMembers(team.getNoOfTeamMembers())
                    .teamName(team.getTeamName())
                    .build()));
    return teamResponses;
  }

  public void deleteTeam(String orgSlug, long teamId) {
    Team team = teamValidation(teamId, orgSlug);
    team.setDeletedAt(new Date());
    team.setStatus(Boolean.FALSE);
    teamRepository.save(team);
  }

  public Team teamValidation(long teamId, String orgSlug) {
    Team team = teamRepository.findByIdAndOrgSlug(teamId, orgSlug);
    if (team == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeamNotFound");
    }
    return team;
  }

  public TeamResponse getTeamDetailById(String orgSlug, long teamId) {
    Team team = teamValidation(teamId, orgSlug);
    List<TeamDetails> studentDetails =
        team.getStudents().stream()
            .map(
                student ->
                    TeamDetails.builder()
                        .studentId(student.getId())
                        .userId(student.getUserInfo().getId())
                        .studentName(
                            student.getUserInfo().getFirstName()
                                + " "
                                + student.getUserInfo().getLastName())
                        .build())
            .toList();

    return TeamResponse.builder()
        .teamId(team.getId())
        .teamName(team.getName())
        .noOfTeamMembers(studentDetails.size())
        .studentDetails(studentDetails)
        .build();
  }

  public void editTeam(String orgSlug, Long teamId, TeamRequest teamRequest) {
    if (teamRequest.getStudentId().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Persons.Team");
    }

    Team team = teamRepository.findByIdAndOrgSlug(teamId, orgSlug);
    if (team == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeamNotFound");
    }

    List<Long> currentStudentIds = teamRepository.getStudentIdsByTeamId(teamId);
    List<Long> newStudentIds =
        teamRequest.getStudentId().stream()
            .filter(element -> !currentStudentIds.contains(element))
            .toList();

    teamRepository.deleteStudentsFromTeam(teamId);

    team.setName(teamRequest.getName());
    team.setStudents(buildStudentId(teamRequest.getStudentId()));
    teamRepository.save(team);

    List<Long> courseIds = teamRepository.getCoursesForTeam(teamId);

    if (!courseIds.isEmpty() && !newStudentIds.isEmpty()) {
      try {
        eventPublisher.publishOnboardedCourseScheduleEvent(
            TeamCourseScheduleMetaData.builder()
                .courseIds(courseIds)
                .studentIds(newStudentIds)
                .build());
      } catch (Exception e) {
        throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.NewPerson.Schedule", e);
      }
    }
  }

  public List<GenericMetricResponse> getPersonsSummaryData(String orgSlug) {
    List<GenericMetricResponse> genericMetricResponse = new ArrayList<>();
    var teamsCount = teamRepository.countByOrgSlugAndStatus(orgSlug, true);
    var courseCount = courseScheduleRepository.countByOrgSlug(orgSlug);
    genericMetricResponse.add(buildPersonsSummaryData(teamsCount, courseCount));
    return genericMetricResponse;
  }

  public GenericMetricResponse buildPersonsSummaryData(long teamsCount, long courseCount) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("team_count", teamsCount);
    dataMap.put("course_count", courseCount);
    return GenericMetricResponse.builder().data(dataMap).build();
  }

  public List<GenericMetricResponse> getPersonDataCount(String orgSlug, String studentAuthUserId) {
    List<GenericMetricResponse> genericMetricResponse = new ArrayList<>();
    var user = userRepository.findByAuthUserId(studentAuthUserId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Student.Invalid");
    }
    List<CourseScheduleInst> courseScheduleInst =
        courseScheduleInstRepository.findByOrgSlugAndStudentId(orgSlug, user.get());
    long courseCountTotal = courseScheduleInst.size();
    long pendingCourse =
        courseScheduleInst.stream()
            .filter(course -> course.getStatus().name().equals("NOT_STARTED"))
            .count();
    long completedCourse =
        courseScheduleInst.stream()
            .filter(course -> course.getStatus().name().equals("COMPLETED"))
            .count();
    genericMetricResponse.add(
        buildPersonsCountData(courseCountTotal, pendingCourse, completedCourse));
    return genericMetricResponse;
  }

  private GenericMetricResponse buildPersonsCountData(
      long courseCountTotal, long pendingCourse, long completedCourse) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("total_course_count", courseCountTotal);
    dataMap.put("pending_course_count", pendingCourse);
    dataMap.put("completed_course_count", completedCourse);
    return GenericMetricResponse.builder().data(dataMap).build();
  }
}
