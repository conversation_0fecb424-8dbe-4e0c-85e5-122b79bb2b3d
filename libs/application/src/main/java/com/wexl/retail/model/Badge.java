package com.wexl.retail.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Badge extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "badge_id_seq")
  @SequenceGenerator(name = "badge_id_seq", allocationSize = 1)
  private long id;

  private String slug;
  private String name;
  private int seq;
  private String badgeImageUrl;
  private String description;
  private Integer expireInDays;
}
