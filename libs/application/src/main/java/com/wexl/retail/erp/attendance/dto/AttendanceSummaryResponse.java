package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AttendanceSummaryResponse {

  @JsonProperty("attendance_id")
  private Long attendanceID;

  @JsonProperty("date_id")
  private Long dateId;

  @JsonProperty("section_name")
  private String sectionName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("present_count")
  private Long presentCount;

  @JsonProperty("absent_count")
  private Long absentCount;

  @JsonProperty("leave_count")
  private Long leaveCount;

  @JsonProperty("late_comer_count")
  private Long lateComerCount;

  @JsonProperty("half_day_count")
  private Long halfDayCount;

  @JsonProperty("ptm_count")
  private Long ptmCount;

  @JsonProperty("nid_count")
  private Long nidCount;

  @JsonProperty("status")
  private String status;

  @JsonProperty("section_id")
  private Long sectionId;

  @JsonProperty("section_uuid")
  private String sectionUuid;

  @JsonProperty("isHoliday")
  private Integer isHoliday;
}
