package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.regex.Pattern;

public class Validation {

  private Validation() {
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Validation.Class");
  }

  public static boolean isValidEmail(String email) {
    if (email == null) {
      return false;
    }
    var regex = "^(.+)@(.+)$";
    var pattern = Pattern.compile(regex);
    return pattern.matcher(email).matches();
  }

  public static boolean isValidMobileNumber(String mobileNumber) {
    if (mobileNumber == null) {
      return false;
    }
    Pattern pattern = Pattern.compile("^\\d{10}$");
    return pattern.matcher(mobileNumber).matches();
  }
}
