package com.wexl.retail.documents.repository;

import com.wexl.retail.documents.model.DocumentTeacher;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface DocumentTeacherRepository extends JpaRepository<DocumentTeacher, Long> {

  List<DocumentTeacher> findAllByTeacherIdAndOrgSlug(Long teacherId, String orgSlug);

  @Query(
      value =
          """
                                  select distinct dt.* from document_teachers dt
                                   inner join documents d on dt.document_id = d.id
                                   inner join document_students ds on ds.document_id = d.id
                                   where dt.teacher_id = :teacherId and dt.org_slug = :orgSlug
                                   and (cast((:subjectSlugs) as varChar) is null or d.subject_slug in (:subjectSlugs))
                                   and (cast((:docTypeIds) as varChar) is null or d.document_type in (:docTypeIds))
                                   and (cast((:chapterSlugs) as varChar) is null or d.chapter_slug in (:chapterSlugs))
                                   and (cast((:classroomName) as varChar) is null or d.class_group_name in (:classroomName))
                                   and (cast((:studentIds) as varChar) is null or ds.student_id in (:studentIds))
                                   order by dt.created_at desc""",
      nativeQuery = true)
  List<DocumentTeacher> getDocumentsByTeacher(
      Long teacherId,
      String orgSlug,
      List<String> subjectSlugs,
      List<String> chapterSlugs,
      List<Integer> docTypeIds,
      List<String> classroomName,
      List<Long> studentIds);
}
