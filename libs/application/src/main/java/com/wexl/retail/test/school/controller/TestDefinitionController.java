package com.wexl.retail.test.school.controller;

import static com.wexl.retail.util.Constants.*;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.model.GenericResponse;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.*;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.zerodigital.service.ZeroDigitalService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

@IsTeacher
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}/teachers/{teacherId}/test-definitions")
public class TestDefinitionController {

  private final TestDefinitionService testDefinitionService;
  private final UserRepository userRepository;
  private final ZeroDigitalService zeroDigitalService;

  @PostMapping
  public TestDefinition createTestDefinition(
      @PathVariable("orgId") String orgSlug,
      @RequestBody TestDefinitionRequest testDefinitionRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return testDefinitionService.createTestDefinition(testDefinitionRequest, bearerToken, orgSlug);
  }

  @PostMapping("{id}/videos")
  @ResponseStatus(HttpStatus.OK)
  public void addVideoExplanation(
      @PathVariable("id") long testDefinitionId,
      @RequestBody TestDefinitionVideoRequest testDefinitionVideoRequest) {
    testDefinitionService.addVideoExplanation(testDefinitionId, testDefinitionVideoRequest);
  }

  @DeleteMapping("{id}/videos")
  @ResponseStatus(HttpStatus.OK)
  public void removeVideoExplanation(@PathVariable("id") long testDefinitionId) {
    testDefinitionService.removeVideoExplanation(testDefinitionId);
  }

  @GetMapping
  public List<TestDefinitionResponse> getAllTestDefinitions(
      @PathVariable String orgId,
      @PathVariable("teacherId") String teacherAuthUserId,
      @RequestParam(required = false) TestType testType,
      @RequestParam(required = false) String chapterSlug,
      @RequestParam(value = "test_name", required = false) String testName,
      @RequestParam(required = false) String gradeSlug,
      @RequestParam(value = "child_org", required = false) String childOrg,
      @RequestParam(required = false, defaultValue = "100") int limit) {
    if (Objects.nonNull(gradeSlug) && TestType.MOCK_TEST.equals(testType)) {
      return testDefinitionService.getMockTestByGrade(orgId, gradeSlug, limit);
    } else if (Objects.nonNull(gradeSlug)) {
      return testDefinitionService.getTestByGrade(orgId, gradeSlug);
    }
    if (Objects.nonNull(chapterSlug)) {
      return testDefinitionService.getSchoolTestByChapter(orgId, chapterSlug, limit);
    }

    final User teacherUser = userRepository.getUserByAuthUserId(teacherAuthUserId);
    if (childOrg != null) {
      final User admin = userRepository.getOrgAdminForOrganization(childOrg);
      return testDefinitionService.getAllTestDefinitions(admin, limit, testName);
    }
    if (AuthUtil.isTeacher(teacherUser)) {
      return testDefinitionService.getAllTestDefinitions(teacherUser, limit, testName);
    }

    return testDefinitionService.getTestDefinitionsCreatedByTeacher(teacherUser, limit);
  }

  @GetMapping("/{id}")
  public TestDefinitionResponse getTestDefinitionById(
      @PathVariable("id") long testDefinitionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return testDefinitionService.getTestDefinitionById(bearerToken, testDefinitionId);
  }

  @DeleteMapping("/{id}")
  public GenericResponse deleteTestDefinitionById(@PathVariable("id") long testDefinitionId) {

    return testDefinitionService.deleteTestDefinitionById(testDefinitionId);
  }

  @PutMapping("/{testDefinitionId}")
  @ResponseStatus(HttpStatus.OK)
  public void editTestDefinition(
      @PathVariable long testDefinitionId,
      @RequestBody TestDefinitionsDto.EditTestDefinitionRequest testDefinitionRequest) {
    testDefinitionService.editTestDefinition(testDefinitionId, testDefinitionRequest);
  }

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/{testDefinitionId}/test-definition-sections")
  public void createTestDefinitionSection(
      @PathVariable long testDefinitionId,
      @RequestBody TestDefinitionsDto.TestDefinitionSectionRequest testDefinitionRequest) {
    testDefinitionService.createTestDefinitionSection(testDefinitionId, testDefinitionRequest);
  }

  @GetMapping("/{testDefinitionId}/v1")
  public TestDefinitionsDto.TestDefinitionResponse getTestDefinitionByIdV1(
      @PathVariable long testDefinitionId) {
    return testDefinitionService.getTestDefinitionByIdV1(testDefinitionId);
  }

  @PutMapping("/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}")
  @ResponseStatus(HttpStatus.OK)
  public void editTestDefinitionSection(
      @PathVariable long testDefinitionSectionId,
      @RequestBody TestDefinitionsDto.TestDefinitionSectionRequest testDefinitionRequest) {
    testDefinitionService.editTestDefinitionSection(testDefinitionSectionId, testDefinitionRequest);
  }

  @ResponseStatus(HttpStatus.ACCEPTED)
  @DeleteMapping("/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}")
  public void deleteTestDefinitionSectionById(@PathVariable long testDefinitionSectionId) {

    testDefinitionService.deleteTestDefinitionSectionById(testDefinitionSectionId);
  }

  @PostMapping("/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}/questions")
  public void addQuestionsToSection(
      @RequestBody TestDefinitionRequest testDefinitionRequest,
      @PathVariable long testDefinitionId,
      @PathVariable long testDefinitionSectionId) {
    testDefinitionService.addQuestionsToSection(
        testDefinitionId, testDefinitionRequest, testDefinitionSectionId);
  }

  @GetMapping("/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}")
  public TestDefinitionResponse getQuestionsByTestDefinitionSectionId(
      @PathVariable long testDefinitionSectionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @PathVariable String orgId) {
    return testDefinitionService.getQuestionsByTestDefinitionSectionId(
        testDefinitionSectionId, bearerToken, orgId);
  }

  @ResponseStatus(HttpStatus.ACCEPTED)
  @DeleteMapping(
      "/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}/questions/{uuid}")
  public void deleteQuestionByTestDefinitionSectionById(
      @PathVariable long testDefinitionSectionId, @PathVariable("uuid") String questionUuid) {

    testDefinitionService.deleteQuestionByTestDefinitionSectionById(
        testDefinitionSectionId, questionUuid);
  }

  @PostMapping("/{testDefinitionId}:publish")
  public void publishCourseDefinitionById(
      @PathVariable long testDefinitionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @RequestParam(required = false, defaultValue = "false") boolean allowOverride) {
    testDefinitionService.publishTestDefinitionById(
        testDefinitionId, true, bearerToken, allowOverride);
  }

  @PostMapping("/{testDefinitionId}:unPublish")
  public void unPublishCourseDefinitionById(
      @PathVariable long testDefinitionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @RequestParam(required = false, defaultValue = "false") boolean allowOverride) {
    testDefinitionService.publishTestDefinitionById(
        testDefinitionId, false, bearerToken, allowOverride);
  }

  @GetMapping("/{testDefinitionId}/full")
  public QuestionDto.QuestionResponse getTestDefinitionQuestions(
      @PathVariable Long testDefinitionId,
      @RequestParam(name = "set", required = false, defaultValue = "1") Integer questionSetNo) {
    var questions =
        testDefinitionService.getTestDefinitionQuestions(testDefinitionId, questionSetNo);
    return testDefinitionService.buildQuestionResponse(questions);
  }

  @PostMapping(
      "/{testDefinitionId}/test-definition-sections/{testDefinitionSectionId}/zd-questions")
  public void addZeroDigitalQuestions(
      @PathVariable("orgId") String orgSlug,
      @RequestBody TestDefinitionsDto.ZeroDigitalQuestionRequest requests,
      @PathVariable("testDefinitionSectionId") long testDefinitionSectionId) {
    zeroDigitalService.addZeroDigitalQuestions(orgSlug, testDefinitionSectionId, requests);
  }

  @GetMapping("/{id}/questions")
  public QuestionDto.TestQuestionResponse getTestDefinitionSectionsDetails(
      @PathVariable("id") Long testDefinitionId) {
    return testDefinitionService.getTestDefinitionSections(testDefinitionId);
  }

  @PostMapping("/{testDefinitionId}:refresh")
  public void refreshTestDefinitionById(
      @PathVariable long testDefinitionId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    testDefinitionService.refreshTestDefinitionById(testDefinitionId, bearerToken);
  }

  @PutMapping("/{testDefinitionId}/answers-migration")
  public void migrateTestQuestionAnswers(@PathVariable("testDefinitionId") Long testDefinitionId) {
    testDefinitionService.migrateTestQuestionAnswers(testDefinitionId);
  }

  @PostMapping("/{testDefinitionId}/report-card-templates/{reportCardTemplateId}")
  public void addScAnswerSheetTemplate(
      @PathVariable("testDefinitionId") Long testDefinitionId,
      @PathVariable("reportCardTemplateId") Long reportCardTemplateId) {
    testDefinitionService.addScAnswerSheetTemplate(testDefinitionId, reportCardTemplateId);
  }

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/{testDefinitionId}/enrichments")
  public void testEnrichment(@PathVariable("testDefinitionId") long testDefinitionId) {
    testDefinitionService.testEnrichmentByTestDef(testDefinitionId);
  }

  @PostMapping("/questions:csv")
  public void uploadQuestions(
      @RequestParam("file") MultipartFile file,
      @PathVariable("orgId") String orgSlug,
      @RequestParam("board") String board,
      @RequestParam("grade") String grade,
      @RequestParam("subject") String subject) {

    if (file.isEmpty()) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "File is required");
    }
    testDefinitionService.uploadQuestions(file, orgSlug, board, grade, subject);
  }

  @PostMapping("/pdf")
  @ResponseStatus(HttpStatus.CREATED)
  public TestDefinitionsDto.MockTestResponse createMockTestDefinition(
      @PathVariable("orgId") String orgSlug,
      @PathVariable("teacherId") String teacherAuthUserId,
      @RequestBody TestDefinitionsDto.MockTestRequest mockTestRequest) {
    return testDefinitionService.createMockTestDefinition(
        orgSlug, teacherAuthUserId, mockTestRequest);
  }

  @PostMapping("/{id}/pdf:upload")
  public Map<String, String> getPdfFileUploadUrl(
      @PathVariable("orgId") String orgSlug, @PathVariable("id") Long testDefinitionId) {
    return testDefinitionService.getPdfFileUploadUrl(orgSlug, testDefinitionId);
  }

  @PostMapping("/{testDefinitionId}/pdf:questions")
  public void addQuestionsToTestDefinitionSection(
      @PathVariable("testDefinitionId") long testDefinitionId,
      @RequestBody TestDefinitionsDto.SectionRequest sectionRequest) {
    testDefinitionService.addQuestionsToTestDefinitionSection(testDefinitionId, sectionRequest);
  }
}
