package com.wexl.retail.student.answer;

import com.wexl.retail.model.Model;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.test.school.dto.PbqDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;
import org.hibernate.annotations.Type;

@Entity
@Data
@Table(name = "exam_answers")
public class ExamAnswer extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "exam_answer_id_seq")
  @SequenceGenerator(name = "exam_answer_id_seq", allocationSize = 1)
  private long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "exam_id")
  private Exam exam;

  @Column(name = "exam_reference")
  private Long examReference;

  @Column(name = "is_mobile")
  private Boolean isMobile;

  private long questionId;
  private String questionUuid;
  private Integer selectedOption;
  private boolean active;
  private boolean isCorrect;
  private String subtopicSlug;

  @Column(name = "is_attempted")
  private boolean isAttempted;

  @Column(name = "negative_marks")
  private Float negativeMarks = 0f;

  @Column(columnDefinition = "VARCHAR(100) default 'mcq'")
  private String type;

  @Column(name = "marks_scored", nullable = true)
  private Float marksScoredPerQuestion;

  private Integer marksPerQuestion;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "msq_selected_answer", columnDefinition = "jsonb")
  private List<Long> msqSelectedAnswer;

  private Boolean yesNoSelectedAnswer;
  private Integer amcqSelectedAnswer;
  private String spchSelectedAnswer;

  @Column(columnDefinition = "TEXT")
  private String subjectiveWrittenAnswer;

  private Float natSelectedAnswer;
  private String fbqSelectedAnswer;
  private String answerType;

  @Column(name = "ai_analysis", columnDefinition = "TEXT")
  private String aiAnalysis;

  @Column(name = "ai_marks")
  private Float aiMarks;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(name = "pbq_answers", columnDefinition = "jsonb")
  private PbqDto.Data pbqAnswers;

  @Column(columnDefinition = "VARCHAR(10000)")
  private String ddfbqAttemptedAnswer;

  @OneToMany(mappedBy = "answer", cascade = CascadeType.ALL)
  private List<ExamAnswerAttachment> examAnswerAttachments;

  private String answer;

  @Column(name = "answer_prev")
  private String prevAnswer;

  @Column(name = "updated_by")
  private String updatedBy;

  private String feedback;

  public List<ExamAnswerAttachment> getExamAnswerAttachments() {
    if (Objects.isNull(examAnswerAttachments)) {
      return new ArrayList<>();
    }
    return examAnswerAttachments;
  }
}
