package com.wexl.retail.student.worksheets.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public interface WorksheetResponse {

  @JsonProperty("test_definition_id")
  String getTestDefinitionId();

  @JsonProperty("worksheet_name")
  String getTestName();

  @JsonProperty("worksheet_message")
  String getMessage();

  @JsonProperty("questions_count")
  Integer getNoOfQuestions();

  @JsonProperty("exam_id")
  Long getExamId();

  @JsonProperty("correct_answers")
  Integer getCorrectAnswers();

  @JsonProperty("percentage")
  Integer getPercentage();
}
