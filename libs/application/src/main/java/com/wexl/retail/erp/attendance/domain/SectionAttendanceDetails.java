package com.wexl.retail.erp.attendance.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@Entity
@Table(name = "section_attendance_details")
public class SectionAttendanceDetails extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String attendanceStatus;

  @JsonProperty("afternoon_attendance_status")
  private String afternoonAttendanceStatus;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @Column(name = "entry_time")
  private LocalDateTime entryTime;

  @Column(name = "exit_time")
  private LocalDateTime exitTime;

  @Column(name = "note", columnDefinition = "TEXT")
  private String note;

  @ManyToOne
  @JoinColumn(name = "section_attendance_id")
  private SectionAttendance sectionAttendance;
}
