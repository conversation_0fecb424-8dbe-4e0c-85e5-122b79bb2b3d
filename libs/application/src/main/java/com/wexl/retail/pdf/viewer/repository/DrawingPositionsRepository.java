package com.wexl.retail.pdf.viewer.repository;

import com.wexl.retail.pdf.viewer.domain.DrawingPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface DrawingPositionsRepository
    extends JpaRepository<DrawingPosition, Long>, JpaSpecificationExecutor<DrawingPosition> {

  @Query(value = "select nextval('public.\"drawing_position_id_seq\"')", nativeQuery = true)
  long getNextValOfDrawingPositionIdSeq();
}
