package com.wexl.retail.term.repository;

import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.model.TermAssessment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TermAssessmentRepository extends JpaRepository<TermAssessment, Long> {
  Optional<TermAssessment> findBySlug(String slug);

  Optional<TermAssessment> findBySlugAndTerm(String slug, Term term);

  List<TermAssessment> getBySlug(String slug);

  List<TermAssessment> findAllBySlugIn(List<String> slugs);

  @Query(
      value =
          """
          select ta.* from  term_assessments ta
                                   join term_assessment_grades tag  on tag.term_assessments_id = ta.id
                                    join terms t on t.id = ta.term_id
                                   where (cast((:termSlugs) as varChar) is null or t.slug in (:termSlugs))
                                   and tag.grade_slug =:gradeSlug""",
      nativeQuery = true)
  List<TermAssessment> getAssessmentsByTermSlugAndGradeSlug(
      List<String> termSlugs, String gradeSlug);

  @Query(
      value =
          """
          select ta.* from report_card_jobs rcj
          join report_card_config rcc  on rcc.id = rcj.report_card_config_id
          join report_card_config_details rccd  on rccd.report_card_config_id  = rcc.id
          join term_assessments ta  on ta.id = rccd.term_assessment_id
          where rcj.id = :reportCardJob""",
      nativeQuery = true)
  List<TermAssessment> getAssessmentsByReportJob(Long reportCardJob);

  @Query(
      value =
          """
                  select ta.term_id from term_assessments ta join term_assessment_grades tag
                   on tag.term_assessments_id = ta.id  where board_slug = :boardSlug and grade_slug  =:gradeSlug """,
      nativeQuery = true)
  Long getTermIdByBoardAndGrade(String boardSlug, String gradeSlug);
}
