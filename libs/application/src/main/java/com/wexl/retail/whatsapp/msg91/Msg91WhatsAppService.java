package com.wexl.retail.whatsapp.msg91;

import com.wexl.retail.config.OrgSmtpConfigProperties;
import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.bot.dto.WhatsAppBotDto;
import com.wexl.retail.whatsapp.interakt.InteraktWhatsAppService;
import com.wexl.retail.whatsapp.interakt.dto.Request;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@Primary
@RequiredArgsConstructor
public class Msg91WhatsAppService implements WhatsAppService {

  @Value("${app.msg91.whatsAppOutBoundUrl}")
  private String whatsAppOutBoundUrl;

  @Value("${app.msg91.bulkwhatsAppOutBoundUrl}")
  private String bulkWhatsAppOutBoundUrl;

  @Value("${app.msg91.integratedNumber}")
  private String integratedNumber;

  @Value("${app.msg91.authkey}")
  private String msg91Authkey;

  private final InteraktWhatsAppService interaktWhatsAppService;
  private final OrgSmtpConfigProperties orgSmtpConfigProperties;

  @Override
  public void sendWhatsAppMessage(String templateId, List<Request.Recipient> recipients) {
    interaktWhatsAppService.sendWhatsAppMessage(templateId, recipients);
  }

  @Override
  public void sendWhatsAppBotMessage(String message, String recipientNumber) {
    RestTemplate restTemplate = new RestTemplate();
    String url =
        whatsAppOutBoundUrl
            + "?integrated_number="
            + integratedNumber
            + "&recipient_number="
            + recipientNumber
            + "&content_type=text";

    HttpHeaders headers = new HttpHeaders();
    headers.set("accept", "application/json");
    headers.set("authkey", msg91Authkey);
    headers.set("content-type", "application/json");

    Map<String, String> body = new HashMap<>();
    body.put("text", message);

    HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(body, headers);

    var response =
        restTemplate.exchange(url, HttpMethod.POST, requestEntity, WhatsAppBotDto.Response.class);
    if ("success".equals(response.getBody().status())) {
      log.info("Successfully sent message {}", response);
    } else {
      log.error("Failed to send message {}", response);
    }
  }

  @Override
  public String sendBulkWhatsAppMessage(
      String templateId, List<Request.Recipient> recipientsOriginal) {
    RestTemplate restTemplate = new RestTemplate();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("authkey", msg91Authkey);

    List<Map<String, Object>> toAndComponentsList = new ArrayList<>();

    recipientsOriginal.forEach(
        recipient -> {
          Map<String, Object> body1 = new HashMap<>();
          body1.put("type", "text");
          body1.put("value", recipient.name());

          Map<String, Object> body2 = new HashMap<>();
          body2.put("type", "text");
          body2.put("value", recipient.date());

          Map<String, Object> body3 = new HashMap<>();
          body3.put("type", "text");
          body3.put("value", recipient.orgName());

          Map<String, Object> button1 = new HashMap<>();
          button1.put("subtype", "url");
          button1.put("type", "text");
          button1.put("value", recipient.reportCardLink());

          Map<String, Object> components = new HashMap<>();
          components.put("body_1", body1);
          components.put("body_2", body2);
          components.put("body_3", body3);
          components.put("button_1", button1);

          Map<String, Object> toAndComponents = new HashMap<>();
          toAndComponents.put("to", Collections.singletonList(recipient.mobileNumber()));
          toAndComponents.put("components", components);

          toAndComponentsList.add(toAndComponents);
        });

    Map<String, Object> language = new HashMap<>();
    language.put("code", "en_US");
    language.put("policy", "deterministic");

    Map<String, Object> template = new HashMap<>();
    template.put("name", templateId);
    template.put("language", language);
    template.put("namespace", "3fbd8bec_b5ed_460d_b910_73a4da9c0e0c");
    template.put("to_and_components", toAndComponentsList);

    Map<String, Object> payload = new HashMap<>();
    payload.put("messaging_product", "whatsapp");
    payload.put("type", "template");
    payload.put("template", template);

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("integrated_number", integratedNumber);
    requestBody.put("content_type", "template");
    requestBody.put("payload", payload);

    String remark;
    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
    try {
      var response =
          restTemplate.postForEntity(
              bulkWhatsAppOutBoundUrl, entity, Request.BulkWhatsAppResponse.class);
      if (Objects.nonNull(response.getBody())
          && Boolean.FALSE.toString().equals(response.getBody().hasError())) {
        log.info("Successfully sent bulk WhatsApp message: {}", response.getBody());
        remark = "Successfully sent bulk WhatsApp message";
      } else {
        log.error("Failed to send bulk WhatsApp message");
        remark = "Failed to send bulk WhatsApp message";
      }
    } catch (Exception e) {
      log.error("Error occurred while sending WhatsApp message: {}", e.getMessage(), e);
      remark = "Error occurred while sending WhatsApp message: " + e.getMessage();
    }
    return remark;
  }

  private OrgSmtpConfigProperties.SmtpConfig getWhatsAppOrgConfig(String orgSlug) {
    if (orgSmtpConfigProperties != null && orgSmtpConfigProperties.getSmtpConfig() != null) {
      OrgSmtpConfigProperties.SmtpConfig smtpConfig =
          orgSmtpConfigProperties.getSmtpConfig().get(orgSlug);
      if (smtpConfig != null && smtpConfig.getFromEmail() != null) {
        return smtpConfig;
      }
    }
    return OrgSmtpConfigProperties.SmtpConfig.builder()
        .authKey(msg91Authkey)
        .integratedNumber(integratedNumber)
        .build();
  }

  @Override
  public String sendBulkWhatsAppMessageWithStaticMessage(
      String templateId, List<Request.Recipient> recipientsOriginal) {
    RestTemplate restTemplate = new RestTemplate();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    String orgSlug = recipientsOriginal.getFirst().orgSlug();
    OrgSmtpConfigProperties.SmtpConfig config = getWhatsAppOrgConfig(orgSlug);
    headers.set("authkey", config.getAuthKey());

    List<Map<String, Object>> toAndComponentsList = new ArrayList<>();

    recipientsOriginal.forEach(
        recipient -> {
          Map<String, Object> toAndComponents = new HashMap<>();

          toAndComponents.put("to", Collections.singletonList(recipient.mobileNumber()));
          toAndComponents.put("components", new HashMap<>());

          toAndComponentsList.add(toAndComponents);
        });

    Map<String, Object> language = new HashMap<>();
    language.put("code", "en_US");
    language.put("policy", "deterministic");

    Map<String, Object> template = new HashMap<>();
    template.put("name", templateId);
    template.put("language", language);
    template.put("namespace", "3fbd8bec_b5ed_460d_b910_73a4da9c0e0c");
    template.put("to_and_components", toAndComponentsList);

    Map<String, Object> payload = new HashMap<>();
    payload.put("messaging_product", "whatsapp");
    payload.put("type", "template");
    payload.put("template", template);

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("integrated_number", config.getIntegratedNumber());
    requestBody.put("content_type", "template");
    requestBody.put("payload", payload);

    String remark;
    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
    try {
      var response =
          restTemplate.postForEntity(
              bulkWhatsAppOutBoundUrl, entity, Request.BulkWhatsAppResponse.class);
      if (Objects.nonNull(response.getBody())
          && Boolean.FALSE.toString().equals(response.getBody().hasError())) {
        log.info("Successfully sent bulk WhatsApp message: {}", response.getBody());
        remark = "Successfully sent bulk WhatsApp message";
      } else {
        log.error("Failed to send bulk WhatsApp message");
        remark = "Failed to send bulk WhatsApp message";
      }
    } catch (Exception e) {
      log.error("Error occurred while sending WhatsApp message: {}", e.getMessage(), e);
      remark = "Error occurred while sending WhatsApp message: " + e.getMessage();
    }
    return remark;
  }

  @Override
  public String sendFeeDueBulkWhatsAppMessage(
      String templateId, List<Request.Recipient> recipients) {
    RestTemplate restTemplate = new RestTemplate();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("authkey", msg91Authkey);

    List<Map<String, Object>> toAndComponentsList = new ArrayList<>();

    recipients.forEach(
        recipient -> {
          Map<String, Object> body1 = new HashMap<>();
          body1.put("type", "text");
          body1.put("value", recipient.name());

          Map<String, Object> body2 = new HashMap<>();
          body2.put("type", "text");
          body2.put("value", recipient.sectionName());

          Map<String, Object> body3 = new HashMap<>();
          body3.put("type", "text");
          body3.put("value", recipient.orgName());

          Map<String, Object> components = new HashMap<>();
          components.put("body_1", body1);
          components.put("body_2", body2);
          components.put("body_3", body3);

          Map<String, Object> toAndComponents = new HashMap<>();
          toAndComponents.put("to", Collections.singletonList(recipient.mobileNumber()));
          toAndComponents.put("components", components);

          toAndComponentsList.add(toAndComponents);
        });

    Map<String, Object> language = new HashMap<>();
    language.put("code", "en_US");
    language.put("policy", "deterministic");

    Map<String, Object> template = new HashMap<>();
    template.put("name", templateId);
    template.put("language", language);
    template.put("namespace", "3fbd8bec_b5ed_460d_b910_73a4da9c0e0c");
    template.put("to_and_components", toAndComponentsList);

    Map<String, Object> payload = new HashMap<>();
    payload.put("messaging_product", "whatsapp");
    payload.put("type", "template");
    payload.put("template", template);

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("integrated_number", integratedNumber);
    requestBody.put("content_type", "template");
    requestBody.put("payload", payload);

    String remark;
    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
    try {
      var response =
          restTemplate.postForEntity(
              bulkWhatsAppOutBoundUrl, entity, Request.BulkWhatsAppResponse.class);
      if (Objects.nonNull(response.getBody())
          && Boolean.FALSE.toString().equals(response.getBody().hasError())) {
        log.info("Successfully sent bulk WhatsApp message: {}", response.getBody());
        remark = "Successfully sent bulk WhatsApp message";
      } else {
        log.error("Failed to send bulk WhatsApp message");
        remark = "Failed to send bulk WhatsApp message";
      }
    } catch (Exception e) {
      log.error("Error occurred while sending WhatsApp message: {}", e.getMessage(), e);
      remark = "Error occurred while sending WhatsApp message: " + e.getMessage();
    }
    return remark;
  }
}
