package com.wexl.retail.classroom.transfer;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/classrooms/{classroomId}/classroom-transfer")
public class ClassroomTransferController {
  private final ClassroomTransferService classroomTransferService;

  @IsOrgAdminOrTeacher
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void classroomTransfer(
      @PathVariable String orgSlug,
      @PathVariable("classroomId") Long oldClassroomId,
      @RequestBody ClassRoomTransferDto.Request request) {
    classroomTransferService.classroomTransfer(orgSlug, request, oldClassroomId);
  }
}
