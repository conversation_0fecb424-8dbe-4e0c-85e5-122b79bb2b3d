package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.model.ClassroomScheduleInstResults;
import com.wexl.retail.meetingroom.domain.MeetingRoom;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ClassroomScheduleRepository extends JpaRepository<ClassroomSchedule, Long> {

  List<ClassroomSchedule> findByOrgSlugAndDeletedAtIsNull(String orgSlug);

  Optional<ClassroomSchedule> findByIdAndOrgSlugAndDeletedAtIsNull(Long id, String orgSlug);

  List<ClassroomSchedule> findByOrgSlugAndMeetingRoom(String orgSlug, MeetingRoom meetingRoom);

  Optional<ClassroomSchedule> findByTitle(String title);

  @Query(
      value =
          """
              select cs.* from  classroom_schedules cs
                  inner join classrooms c on c.id = cs.classroom_id
                  inner join classroom_teachers ct on ct.classroom_id = c.id
                  where cs.classroom_id = :classroomId and  cs.day_of_week in (:dayOfWeek)
                  and ((cs.meeting_start_time >= :startTime and  cs.meeting_start_time <= :endTime)
                  or (cs.meeting_end_time <= :endTime and  cs.meeting_end_time >=  :startTime ))
                  and ct.teacher_id  in (:teacherIds)""",
      nativeQuery = true)
  List<ClassroomSchedule> checkScheduledByTime(
      LocalTime startTime,
      LocalTime endTime,
      String dayOfWeek,
      long classroomId,
      List<Long> teacherIds);

  @Query(
      value =
          """
                  select c.name as classroomName, c.id as classroomId ,count(crs.*) as studentCount ,
                     csi.id as scheduleInstId,csi.title as title,csi.start_time as startTime,csi.host_join_time as hostJoinTime,
                     csi.end_time as endTime,cs.id as ScheduleId,mr.host_link  as link,csi.status as status,
                     csi.day_of_week as dayOfWeek ,mr.id as  meetingRoomId ,cs.expiry_date as expiryDate,
                     cs.start_date as startDate,cs.created_at as createdAt
                     from classroom_schedules cs
                     inner join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                     inner join classrooms c on c.id = cs.classroom_id
                     inner join meeting_rooms mr on mr.id = cs.meeting_room_id or mr.id = csi.meeting_room_id
                     inner join classroom_students crs on crs.classroom_id = c.id
                     inner join classroom_teachers crt on crt.classroom_id = c.id
                     where cs.org_slug = :orgSlug
                     and to_char(csi.start_time,'yyyy-MM-dd') >=  :fromDate
                     and to_char(csi.start_time,'yyyy-MM-dd') <=  :toDate
                     and crt.teacher_id = :teacherId and csi.deleted_at is NULL
                     group by  c.name , c.id  ,csi.id ,csi.title ,csi.start_time ,csi.end_time,
                     cs.id ,mr.host_link ,csi.status ,csi.day_of_week  ,mr.id  ,cs.expiry_date ,
                     cs.start_date order by csi.start_time ASC""",
      nativeQuery = true)
  List<ClassroomScheduleInstResults> getTeacherScheduleInsts(
      String orgSlug, Long teacherId, String fromDate, String toDate);

  @Query(
      value =
          """
                          select cs.* from  classroom_schedules cs
                           inner join classrooms c on c.id = cs.classroom_id
                           inner join classroom_students css on css.classroom_id = c.id
                           where cs.day_of_week in (:dayOfWeek)
                           and ((cs.meeting_start_time >= :startTime and  cs.meeting_start_time <= :endTime)
                           or (cs.meeting_end_time <= :endTime and  cs.meeting_end_time >= :startTime ))
                           and css.student_id  in (:studentIds)""",
      nativeQuery = true)
  List<ClassroomSchedule> checkScheduledByTimeForStudents(
      LocalTime startTime, LocalTime endTime, String dayOfWeek, List<Long> studentIds);
}
