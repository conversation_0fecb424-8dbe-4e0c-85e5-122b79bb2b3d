package com.wexl.retail.util;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.Notification;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.model.StudentNotification;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.school.domain.TestType;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class NotificationUtils {

  private final OrganizationRepository organizationRepository;
  private final NotificationRepository notificationRepository;
  private final AuthService authService;
  private final StrapiService strapiService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ScheduleTestRepository scheduleTestRepository;

  public void sendNotification(
      String message,
      List<Student> students,
      String orgSlug,
      NotificationDto.TestAttributes attributes) {
    notificationRepository.save(buildNotification(message, students, orgSlug, attributes));
  }

  private Notification buildNotification(
      String message,
      List<Student> students,
      String orgSlug,
      NotificationDto.TestAttributes attributes) {
    var teacher = authService.getTeacherDetails().getTeacherInfo();
    var organization = organizationRepository.findBySlug(orgSlug);
    Notification notification = new Notification();
    notification.setMessage(message);
    notification.setOrganization(organization);
    notification.setNotificationType(NotificationType.SECTION);
    notification.setCreatedBy(teacher);
    notification.setTitle("Notification");
    notification.setStudentNotifications(
        buildStudentNotification(students, orgSlug, notification, attributes));
    return notification;
  }

  private List<StudentNotification> buildStudentNotification(
      List<Student> students,
      String orgSlug,
      Notification notification,
      NotificationDto.TestAttributes attributes) {
    List<StudentNotification> studentNotifications = new ArrayList<>();
    students.forEach(
        student ->
            studentNotifications.add(buildAttributes(student, orgSlug, notification, attributes)));
    return studentNotifications;
  }

  private StudentNotification buildAttributes(
      Student student,
      String orgSlug,
      Notification notification,
      NotificationDto.TestAttributes attributes) {
    if (attributes == null) {
      return buildStudentNotificationDetails(student, orgSlug, notification, null);
    }
    if (TestType.MOCK_TEST.name().equals(attributes.testType())
        || TestType.ASSIGNMENT.name().equals(attributes.testType())) {
      var scheduleTest =
          scheduleTestRepository
              .findById(attributes.testId())
              .orElseThrow(
                  () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule"));
      ScheduleTestStudent tss =
          scheduleTestStudentRepository
              .findByScheduleTestAndStudent(scheduleTest, student.getUserInfo())
              .orElseThrow(
                  () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule"));
      var mockAttributes =
          NotificationDto.TestAttributes.builder()
              .testUuid(tss.getUuid())
              .testDefinitionId(scheduleTest.getTestDefinition().getId())
              .testId(scheduleTest.getId())
              .testType(
                  attributes.testType().equals(TestType.MOCK_TEST.name())
                      ? TestType.MOCK_TEST.name()
                      : TestType.ASSIGNMENT.name())
              .testName(scheduleTest.getTestDefinition().getTestName())
              .subject(
                  strapiService.getSubjectNameBySlug(
                      scheduleTest.getTestDefinition().getSubjectSlug()))
              .subtopic(attributes.subtopic())
              .build();
      return buildStudentNotificationDetails(student, orgSlug, notification, mockAttributes);
    }
    return buildStudentNotificationDetails(student, orgSlug, notification, attributes);
  }

  private StudentNotification buildStudentNotificationDetails(
      Student student,
      String orgSlug,
      Notification notification,
      NotificationDto.TestAttributes attributes) {
    return StudentNotification.builder()
        .notification(notification)
        .orgSlug(orgSlug)
        .student(student)
        .testAttributes(attributes)
        .build();
  }
}
