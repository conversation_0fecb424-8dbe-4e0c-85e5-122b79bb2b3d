package com.wexl.retail.section.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherSubjectsRequest {

  @NotNull private String boardSlug;

  @NotNull private List<String> subject;

  @JsonProperty("class_teacher")
  private Boolean classTeacher;
}
