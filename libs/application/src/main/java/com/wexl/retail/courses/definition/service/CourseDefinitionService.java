package com.wexl.retail.courses.definition.service;

import static java.lang.String.format;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.courses.categories.dto.CourseCategoryDto;
import com.wexl.retail.courses.categories.model.CourseCategory;
import com.wexl.retail.courses.categories.repository.CourseCategoryRepository;
import com.wexl.retail.courses.definition.dto.CourseDefinitionRequest;
import com.wexl.retail.courses.definition.dto.CourseDefinitionResponse;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.model.CourseDefinitionStatus;
import com.wexl.retail.courses.definition.model.CourseOrg;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.courses.definition.repository.CourseOrgRepository;
import com.wexl.retail.courses.module.model.CourseModule;
import com.wexl.retail.courses.module.repository.CourseModuleRepository;
import com.wexl.retail.courses.module.service.CourseModuleService;
import com.wexl.retail.courses.step.model.CourseItem;
import com.wexl.retail.courses.step.model.CourseItemType;
import com.wexl.retail.courses.step.repository.CourseStepRepository;
import com.wexl.retail.courses.step.service.CourseItemFactory;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.telegram.service.UserService;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CourseDefinitionService {

  private final AuthService authService;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final CourseItemFactory courseItemFactory;
  private final CourseModuleService courseModuleService;
  private final CourseOrgRepository courseOrgRepository;
  private final CourseStepRepository courseStepRepository;
  private final CourseModuleRepository courseModuleRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;

  private final CourseCategoryRepository courseCategoryRepository;
  private final OrganizationRepository organizationRepository;
  private final StorageService storageService;

  private static final String DEFAULT_COURSE_THUMBNAIL =
      "wexl-internal/thumbnails/course_default_thumbnail.png";

  @SneakyThrows
  public CourseDefinitionResponse createCourseDefinition(CourseDefinitionRequest request) {
    var owner = userService.findUserById(authService.getUserDetails().getId());
    var courseDefinition = courseDefinitionRepository.save(buildCourseDefinition(request, owner));
    associateOrgsToCourseDefinition(request.getOrgAssociations(), courseDefinition);
    return buildCourseDefinitionResponse(courseDefinition);
  }

  private void associateOrgsToCourseDefinition(
      List<String> orgSlugs, CourseDefinition courseDefinition) {
    courseOrgRepository.saveAll(
        orgSlugs.stream()
            .map(
                orgSlug ->
                    CourseOrg.builder().courseDefinition(courseDefinition).orgSlug(orgSlug).build())
            .toList());
  }

  @SneakyThrows
  public CourseDefinitionResponse getCourseDefinitionById(long courseDefId, boolean preview) {

    var courseDefinition = findCourseDefinitionById(courseDefId);
    var courseDefinitionResponse = buildCourseDefinitionResponse(courseDefinition);

    if (!preview) {
      return courseDefinitionResponse;
    }

    return buildCoursePreviewResponse(
        courseDefinitionResponse,
        courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId));
  }

  private CourseDefinitionResponse buildCoursePreviewResponse(
      CourseDefinitionResponse courseDefinitionResponse, List<CourseModule> courseModules) {
    courseDefinitionResponse.setModules(
        courseModules.stream()
            .map(
                courseModule -> {
                  var courseModuleResponse =
                      courseModuleService.buildCourseModuleResponse(courseModule);
                  courseModuleResponse.setSteps(
                      courseStepRepository
                          .getAllItemsAssociatedToCourseModule(courseModule.getId())
                          .stream()
                          .map(courseItemFactory::buildCourseStepResponse)
                          .toList());

                  return courseModuleResponse;
                })
            .toList());

    return courseDefinitionResponse;
  }

  @SneakyThrows
  public CourseDefinition findCourseDefinitionById(long courseDefId) {
    return courseDefinitionRepository
        .findById(courseDefId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Course Definition Not Found for %s".formatted(courseDefId)));
  }

  @SneakyThrows
  public CourseDefinitionResponse updateCourseDefinitionById(
      CourseDefinitionRequest request, long courseDefId) {
    var courseDefinition = findCourseDefinitionById(courseDefId);
    if (CourseDefinitionStatus.ACTIVE.equals(courseDefinition.getStatus())) {
      return updateCourseDefinition(courseDefId, request);
    } else {
      checkIfCurrentUserIsHavingPermission(courseDefinition.getOwner().getAuthUserId());
      checkIfCourseDefinitionIsPublished(courseDefId, courseDefinition.getPublishedAt());

      courseDefinition.setDescription(request.getDescription());
      courseDefinition.setName(request.getName());
      courseDefinition.setPrivate(request.isPrivate());
      courseDefinition.setStatus(request.getStatus());

      courseOrgRepository.deleteAll(
          courseOrgRepository.getAllOrgsAssociatedToCourseDef(courseDefId));
      associateOrgsToCourseDefinition(request.getOrgAssociations(), courseDefinition);

      return buildCourseDefinitionResponse(courseDefinitionRepository.save(courseDefinition));
    }
  }

  public CourseDefinitionResponse cloneCourseDefinitionById(
      long courseDefId, List<String> orgSlugs) {
    var courseDefinition = findCourseDefinitionById(courseDefId);
    checkIfUserBelongsToSameOrganization(courseDefinition.getOwner().getOrganization());

    var owner = userService.findUserById(authService.getUserDetails().getId());
    var cloneCourseDefinition =
        courseDefinitionRepository.save(
            CourseDefinition.builder()
                .version(courseDefinition.getVersion() + 1)
                .status(CourseDefinitionStatus.DRAFT)
                .owner(owner)
                .name(courseDefinition.getName())
                .isPrivate(courseDefinition.isPrivate())
                .description(courseDefinition.getDescription())
                .orgSlug(owner.getOrganization())
                .courseCategory(courseDefinition.getCourseCategory())
                .duration(courseDefinition.getDuration())
                .imagePath(courseDefinition.getImagePath())
                .build());

    associateOrgsToCourseDefinition(orgSlugs, cloneCourseDefinition);
    var courseDefinitionResponse = buildCourseDefinitionResponse(cloneCourseDefinition);
    var courseModules = courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId);

    var courseSteps = new ArrayList<CourseItem>();
    var cloneModules = cloneModulesInCourse(cloneCourseDefinition, courseModules, courseSteps);
    courseStepRepository.saveAll(courseSteps);
    return buildCoursePreviewResponse(courseDefinitionResponse, cloneModules);
  }

  private List<CourseModule> cloneModulesInCourse(
      CourseDefinition cloneCourseDefinition,
      List<CourseModule> courseModules,
      ArrayList<CourseItem> courseSteps) {
    return courseModules.stream()
        .map(
            courseModule -> {
              var cloneModule =
                  courseModuleRepository.save(
                      CourseModule.builder()
                          .name(courseModule.getName())
                          .sequenceNumber(courseModule.getSequenceNumber())
                          .orgSlug(courseModule.getOrgSlug())
                          .courseDefinition(cloneCourseDefinition)
                          .build());

              courseSteps.addAll(cloneAllStepsInModule(courseModule, cloneModule));
              return cloneModule;
            })
        .toList();
  }

  private List<CourseItem> cloneAllStepsInModule(
      CourseModule courseModule, CourseModule cloneModule) {
    return courseStepRepository.getAllItemsAssociatedToCourseModule(courseModule.getId()).stream()
        .map(
            courseItem -> {
              if (courseItem.getItemType().equals(CourseItemType.PAGE)) {
                return courseItemFactory.buildCourseStepPage(
                    courseItem.getSequenceNumber(),
                    cloneModule,
                    courseItem.getCoursePage(),
                    courseItem.getTitle());
              } else if (courseItem.getItemType().equals(CourseItemType.ASSET)) {
                return courseItemFactory.buildCourseStepAsset(
                    courseItem.getSequenceNumber(),
                    cloneModule,
                    courseItem.getAssetSlug(),
                    courseItem.getTitle());
              } else if (courseItem.getItemType().equals(CourseItemType.SCHOOL_TEST)
                  || courseItem.getItemType().equals(CourseItemType.ASSIGNMENT)) {
                return courseItemFactory.buildTestCourseStep(
                    courseItem.getSequenceNumber(),
                    cloneModule,
                    courseItem.getAssetSlug(),
                    courseItem.getTitle(),
                    courseItem.getTestDefinition(),
                    courseItem.getAttributes(),
                    courseItem.getItemType());
              } else if (courseItem.getItemType().equals(CourseItemType.MOCK_TEST)) {
                return courseItemFactory.buildTestCourseStep(
                    courseItem.getSequenceNumber(),
                    cloneModule,
                    courseItem.getAssetSlug(),
                    courseItem.getTitle(),
                    courseItem.getTestDefinition(),
                    courseItem.getAttributes(),
                    courseItem.getItemType());
              } else if (courseItem.getItemType().equals(CourseItemType.CONCEPT_VIDEOS)) {
                return courseItemFactory.buildCourseStepConceptVideos(
                    courseItem.getSequenceNumber(),
                    cloneModule,
                    courseItem.getConceptVideoUuid(),
                    courseItem.getTitle(),
                    courseItem.getAttributes());
              }

              throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
            })
        .toList();
  }

  private void checkIfCourseDefinitionIsPublished(long courseDefId, Timestamp publishedAt) {
    if (Objects.nonNull(publishedAt)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "%s course definition is published, clone this course in case of any modifications."
              .formatted(courseDefId));
    }
  }

  public CourseDefinitionResponse publishCourseDefinitionById(long courseDefId) {
    var courseDefinition = findCourseDefinitionById(courseDefId);
    checkIfCurrentUserIsHavingPermission(courseDefinition.getOwner().getAuthUserId());
    var publishedDate = Timestamp.from(Instant.now());
    courseDefinition.setPublishedAt(publishedDate);
    courseDefinition.setStatus(CourseDefinitionStatus.ACTIVE);
    var publishedCourseDefinition = courseDefinitionRepository.save(courseDefinition);

    var courseItems = new ArrayList<CourseItem>();
    courseModuleRepository.saveAll(
        publishAllCourseRelatedModules(courseDefId, publishedDate, courseItems));
    courseStepRepository.saveAll(courseItems);

    return buildCourseDefinitionResponse(publishedCourseDefinition);
  }

  private List<CourseModule> publishAllCourseRelatedModules(
      long courseDefId, Timestamp publishedDate, ArrayList<CourseItem> courseItems) {
    var courseModules = courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId);
    courseModules.forEach(
        courseModule -> {
          courseModule.setPublishedAt(publishedDate);
          courseItems.addAll(publishAllModuleRelatedItems(publishedDate, courseModule.getId()));
        });
    return courseModules;
  }

  private List<CourseItem> publishAllModuleRelatedItems(Timestamp publishedDate, long moduleId) {
    var courseSteps = courseStepRepository.getAllItemsAssociatedToCourseModule(moduleId);
    courseSteps.forEach(courseStep -> courseStep.setPublishedAt(publishedDate));
    return courseSteps;
  }

  public List<CourseDefinitionResponse> getAllCourseDefinitions(boolean isActive, Long categoryId) {

    var courseDefinitionResponses =
        courseDefinitionRepository
            .getAllCourseDefinitions(authService.getUserDetails().getOrganization(), categoryId)
            .stream()
            .map(this::buildCourseDefinitionResponse)
            .toList();

    if (isActive) {
      return courseDefinitionResponses.stream()
          .filter(
              courseDefinitionResponse ->
                  Objects.equals(
                      courseDefinitionResponse.getStatus(), CourseDefinitionStatus.ACTIVE))
          .toList();
    }

    return courseDefinitionResponses;
  }

  @SneakyThrows
  public CourseDefinitionResponse deleteCourseDefinitionById(long courseDefId) {
    var courseDefinition = findCourseDefinitionById(courseDefId);
    checkIfCurrentUserIsHavingPermission(courseDefinition.getOwner().getAuthUserId());

    courseDefinition.setDeletedAt(new Date());
    courseDefinition.setStatus(CourseDefinitionStatus.ARCHIVED);

    return buildCourseDefinitionResponse(courseDefinitionRepository.save(courseDefinition));
  }

  public void checkIfCurrentUserIsHavingPermission(String ownerAuthId) {
    if (!Objects.equals(authService.getUserDetails().getAuthUserId(), ownerAuthId)) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          format(
              "%s user is not allowed to perform this operation",
              authService.getUserDetails().getAuthUserId()));
    }
  }

  public void checkIfUserBelongsToSameOrganization(String orgSlug) {
    if (!Objects.equals(authService.getUserDetails().getOrganization(), orgSlug)) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          format(
              "%s user is not allowed to perform this operation",
              authService.getUserDetails().getAuthUserId()));
    }
  }

  public List<CourseDefinitionResponse> getCoursesCreatedByTeacher() {
    return courseDefinitionRepository
        .getCoursesCreatedByTeacher(authService.getUserDetails().getId())
        .stream()
        .map(this::buildCourseDefinitionResponse)
        .toList();
  }

  private CourseDefinition buildCourseDefinition(CourseDefinitionRequest request, User owner) {
    CourseCategory category = null;
    Organization organization = organizationRepository.findBySlug(owner.getOrganization());
    category =
        courseCategoryRepository.findByIdAndOrganization(
            request.getCourseCategoryId(), organization);
    if (category == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Category");
    }
    var imagePath =
        StringUtils.isNotEmpty(request.getImageReference())
            ? createImagePath(
                organization.getSlug(), request.getImageReference(), request.getImageExtension())
            : DEFAULT_COURSE_THUMBNAIL;
    return CourseDefinition.builder()
        .orgSlug(owner.getOrganization())
        .description(request.getDescription())
        .isPrivate(request.isPrivate())
        .name(request.getName())
        .owner(owner)
        .status(request.getStatus())
        .version(1)
        .courseCategory(category)
        .imagePath(imagePath)
        .build();
  }

  public String createImagePath(String orgSlug, String reference, String extension) {
    if (extension == null) {
      return reference;
    }
    return "%s/%s.%s".formatted(orgSlug, reference, extension);
  }

  public CourseDefinitionResponse buildCourseDefinitionResponse(CourseDefinition courseDefinition) {

    Integer moduleCount = courseModuleRepository.getModuleCount(courseDefinition.getId());
    return CourseDefinitionResponse.builder()
        .id(courseDefinition.getId())
        .description(courseDefinition.getDescription())
        .name(courseDefinition.getName())
        .status(courseDefinition.getStatus())
        .version(courseDefinition.getVersion())
        .isPrivate(courseDefinition.isPrivate())
        .moduleCount(moduleCount)
        .orgSlug(courseDefinition.getOrgSlug())
        .thumbNail(
            courseDefinition.getImagePath() == null
                ? courseDefinition.getImagePath()
                : storageService.generatePreSignedUrlForFetch(courseDefinition.getImagePath()))
        .courseCategory(
            CourseCategoryDto.CourseCategories.builder()
                .id(courseDefinition.getCourseCategory().getId())
                .name(courseDefinition.getCourseCategory().getName())
                .status(courseDefinition.getCourseCategory().getStatus())
                .build())
        .publishedAt(
            Objects.nonNull(courseDefinition.getPublishedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(
                    courseDefinition.getPublishedAt().toLocalDateTime())
                : null)
        .build();
  }

  public CourseDefinitionResponse updateCourseDefinition(
      long courseDefId, CourseDefinitionRequest request) {
    CourseDefinition course = findCourseDefinitionById(courseDefId);
    course.setName(request.getName());
    course.setDescription(request.getDescription());
    var imagePath =
        !request.getImageReference().isBlank()
            ? createImagePath(
                course.getOrgSlug(), request.getImageReference(), request.getImageExtension())
            : course.getImagePath();
    course.setImagePath(imagePath);
    return buildCourseDefinitionResponse(courseDefinitionRepository.save(course));
  }
}
