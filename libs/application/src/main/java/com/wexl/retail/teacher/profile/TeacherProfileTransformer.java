package com.wexl.retail.teacher.profile;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wexl.retail.model.Addresses;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.storage.StorageService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TeacherProfileTransformer {

  private final StorageService storageService;

  public User mapContactDetails(User teacherUserDetails, ContactDetails contactDetails) {

    if (contactDetails.getMobile() != null) {
      teacherUserDetails.setMobileNumber(contactDetails.getMobile());
    }

    var address = teacherUserDetails.getAddresses();
    if (address == null) {
      address = new Addresses();
      address.setCountry("IN");
    }

    if (contactDetails.getAddress() != null) {
      if (contactDetails.getAddress().getLine1() != null) {
        address.setLine1(contactDetails.getAddress().getLine1());
      }

      if (contactDetails.getAddress().getLine2() != null) {
        address.setLine2(contactDetails.getAddress().getLine2());
      }

      if (contactDetails.getAddress().getCity() != null) {
        address.setCity(contactDetails.getAddress().getCity());
      }

      if (contactDetails.getAddress().getState() != null) {
        address.setState(contactDetails.getAddress().getState());
      }

      if (contactDetails.getAddress().getZipCode() != null) {
        address.setZipCode(contactDetails.getAddress().getZipCode());
      }
      address.setUser(teacherUserDetails);
    }
    teacherUserDetails.setAddresses(address);
    return teacherUserDetails;
  }

  public Teacher mapTeacherDetails(
      Teacher teacherDetails, TeacherProfileRequest teacherProfileRequest) {
    if (teacherProfileRequest.getInstituteDetails() != null) {
      if (teacherProfileRequest.getInstituteDetails().getName() != null) {
        teacherDetails.setInstituteName(teacherProfileRequest.getInstituteDetails().getName());
      }
      if (teacherProfileRequest.getInstituteDetails().getPanNumber() != null) {
        teacherDetails.setPanNumber(teacherProfileRequest.getInstituteDetails().getPanNumber());
      }
    }
    if (teacherProfileRequest.getBankDetails() != null) {
      String bankDetails = new Gson().toJson(teacherProfileRequest.getBankDetails());
      teacherDetails.setBankDetails(bankDetails);
    }
    if (teacherProfileRequest.getProfessionalDetails() != null) {
      if (teacherProfileRequest.getProfessionalDetails().getEducationalDetails() != null) {
        String educationalDetails =
            new Gson()
                .toJson(teacherProfileRequest.getProfessionalDetails().getEducationalDetails());
        teacherDetails.setEducationalDetails(educationalDetails);
      }
      if (teacherProfileRequest.getProfessionalDetails().getTeachingDetails() != null) {
        String teachingDetails =
            new Gson()
                .toJson(
                    teacherProfileRequest.getProfessionalDetails().getTeachingDetails(),
                    List.class);
        teacherDetails.setTeachingDetails(teachingDetails);
      }
    }
    if (teacherProfileRequest.getMetadata() != null) {
      teacherDetails.setMetadata(teacherProfileRequest.getMetadata());
    }
    return teacherDetails;
  }

  @SuppressWarnings("unchecked")
  public TeacherProfileResponse mapTeacherProfileResponse(
      User teacherUserDetails, Teacher teacherDetails) {
    return TeacherProfileResponse.builder()
        .profileImageUrl(
            Objects.isNull(teacherUserDetails.getProfileImage())
                ? null
                : getProfileImageUrl(teacherUserDetails.getProfileImage()))
        .bankDetails(
            new GsonBuilder().create().fromJson(teacherDetails.getBankDetails(), BankDetails.class))
        .teacherPersonalDetails(
            TeacherPersonalDetails.builder().userName(teacherUserDetails.getUserName()).build())
        .contactDetails(
            ContactDetails.builder()
                .address(teacherUserDetails.getAddresses())
                .email(teacherUserDetails.getEmail())
                .mobile(teacherUserDetails.getMobileNumber())
                .profilePicture(teacherUserDetails.getProfilePicture())
                .firstName(teacherUserDetails.getFirstName())
                .lastName(teacherUserDetails.getLastName())
                .teacherCode(teacherDetails.getTeacherCode())
                .build())
        .instituteDetails(
            InstituteDetails.builder()
                .name(teacherDetails.getInstituteName())
                .panNumber(teacherDetails.getPanNumber())
                .build())
        .professionalDetails(
            ProfessionalDetails.builder()
                .educationalDetails(
                    new GsonBuilder()
                        .create()
                        .fromJson(teacherDetails.getEducationalDetails(), List.class))
                .teachingDetails(
                    new GsonBuilder()
                        .create()
                        .fromJson(teacherDetails.getTeachingDetails(), List.class))
                .build())
        .metadata(teacherDetails.getMetadata())
        .build();
  }

  public String getProfileImageUrl(String profileImagePath) {
    return storageService.generatePreSignedUrlForFetch(profileImagePath);
  }
}
