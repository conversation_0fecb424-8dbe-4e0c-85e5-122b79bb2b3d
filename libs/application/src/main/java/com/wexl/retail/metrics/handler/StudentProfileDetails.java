package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentProfileDetails extends AbstractMetricHandler implements MetricHandler {

  private final StudentAttributeService studentAttributeService;

  @Override
  public String name() {
    return "student-profile";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return studentAttributeService.getStudentProfile(org);
  }
}
