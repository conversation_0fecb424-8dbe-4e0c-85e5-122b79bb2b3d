package com.wexl.retail.student.studentdata;

import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import com.wexl.retail.util.CsvUtils;
import com.wexl.retail.util.StrapiService;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class StudentDataService {

  @Autowired private final StudentRepository studentRepository;
  private final StrapiService strapiService;

  public void studentsFor(String organization, HttpServletResponse httpServletResponse) {
    final List<SkilliomaStudent> students = studentRepository.findAllStudentsData(organization);

    List<SkilliomaStudentResponse> studentsDataResponseFrom = studentsResponseFrom(students);
    csvFileForStudentDetails(studentsDataResponseFrom, httpServletResponse);
  }

  private List<SkilliomaStudentResponse> studentsResponseFrom(List<SkilliomaStudent> students) {
    List<Grade> allGrades = new ArrayList<>(strapiService.getAllGrades());
    List<Entity> allBoards = new ArrayList<>(strapiService.getAllBoards());
    return students.stream()
        .map(student -> getStudentResponse(allGrades, allBoards, student))
        .toList();
  }

  private SkilliomaStudentResponse getStudentResponse(
      List<Grade> allGrades, List<Entity> allBoards, SkilliomaStudent student) {
    final var studentResponse =
        SkilliomaStudentResponse.builder()
            .userName(student.getUsername())
            .firstName(student.getFirstName())
            .lastName(student.getLastName())
            .gender(student.getGender())
            .createdDate(student.getCreatedDate())
            .email(student.getEmail())
            .phoneNumber(student.getMobileNumber())
            .schoolName(student.getSchoolName())
            .attributeData(buildAttributesData(student))
            .build();

    final Optional<Grade> studentGrade =
        allGrades.stream().filter(grade -> grade.getId() == student.getClassId()).findFirst();
    if (studentGrade.isPresent()) {
      studentResponse.setGradeSlug(studentGrade.get().getName());
    } else {
      log.error("Student grade not found in strapi");
    }

    final Optional<Entity> studentBoard =
        allBoards.stream().filter(board -> board.getId() == student.getBoardId()).findFirst();
    if (studentBoard.isPresent()) {
      studentResponse.setBoardSlug(studentBoard.get().getName());
    } else {
      log.error("Student board not found in strapi");
    }
    return studentResponse;
  }

  private StudentAttributeData buildAttributesData(SkilliomaStudent student) {
    return StudentAttributeData.builder()
        .bloodGroup(student.getBloodGroup())
        .principalName(student.getPrincipalName())
        .schoolAddress(student.getSchoolAddress())
        .schoolCity(student.getSchoolCity())
        .schoolPin(student.getSchoolPin())
        .schoolState(student.getSchoolState())
        .dateOfBirth(student.getDateOfBirth())
        .build();
  }

  private void csvFileForStudentDetails(
      List<SkilliomaStudentResponse> studentsDataResponseFrom,
      HttpServletResponse httpServletResponse) {

    String[] csvHeader = {
      "User Name",
      "First Name",
      "Last Name",
      "Gender",
      "Principal Name",
      "Blood Group",
      "School State",
      "Postal Code",
      "School City",
      "School Address",
      "Date Of Birth",
      "Grade",
      "Board",
      "Email",
      "Created Date"
    };
    List<List<String>> csvBody = new ArrayList<>();
    studentsDataResponseFrom.forEach(
        studentDataResponse ->
            csvBody.add(
                Arrays.asList(
                    studentDataResponse.getUserName(),
                    studentDataResponse.getFirstName(),
                    studentDataResponse.getLastName(),
                    studentDataResponse.getGender(),
                    studentDataResponse.getAttributeData().getPrincipalName(),
                    studentDataResponse.getAttributeData().getBloodGroup(),
                    studentDataResponse.getAttributeData().getSchoolState(),
                    studentDataResponse.getAttributeData().getSchoolPin(),
                    studentDataResponse.getAttributeData().getSchoolCity(),
                    studentDataResponse.getAttributeData().getSchoolAddress(),
                    studentDataResponse.getAttributeData().getDateOfBirth(),
                    studentDataResponse.getGradeSlug(),
                    studentDataResponse.getBoardSlug(),
                    studentDataResponse.getEmail(),
                    studentDataResponse.getCreatedDate())));

    CsvUtils.generateCsv(csvHeader, csvBody, httpServletResponse);
  }
}
