package com.wexl.retail.staff.repository;

import com.wexl.retail.staff.model.Department;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
  Optional<Department> findByName(String name);

  Optional<Department> findByIdAndOrgSlug(Long id, String orgSlug);
}
