package com.wexl.retail.student.auth.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.idp.UserPasswordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class LocalAuthService implements UserIdpService {

  private final PasswordEncoder passwordEncoder;

  private final UserPasswordService userPasswordService;

  @Override
  public void adminSetUserPassword(String username, String password) {
    if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    final String encodedPassword = passwordEncoder.encode(password);
    userPasswordService.updateUserPassword(username, encodedPassword);
  }

  @Override
  public String loginUser(String username, String password) {
    if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    return userPasswordService.createJwtToken(username, password);
  }

  @Override
  public String retrieveUsernameFromIdpToken(String token) {
    return userPasswordService.retrieveUserNameFromToken(token);
  }
}
