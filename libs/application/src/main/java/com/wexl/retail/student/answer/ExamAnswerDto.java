package com.wexl.retail.student.answer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import java.util.List;

public record ExamAnswerDto() {

  public record ExamAnswerUpdateRequest(@JsonProperty("data") List<UpdateRequest> updateRequest) {}

  public record UpdateRequest(
      @JsonProperty("exam_answer_id") long examAnswerId,
      @JsonProperty("updated_answer") String updatedAnswer,
      @JsonProperty("question_type") QuestionType questionType) {}
}
