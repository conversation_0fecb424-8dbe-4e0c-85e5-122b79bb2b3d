package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class ClassroomScheduleUpdateRequest {

  @JsonProperty("expiry_date")
  private Long expiryDate;

  @JsonProperty("start_time")
  private Long startTime;

  @JsonProperty("end_time")
  private Long endTime;

  @JsonProperty("schedule_inst_request")
  private List<ClassRoomDto.ScheduleInstRequest> scheduleInstRequest;
}
