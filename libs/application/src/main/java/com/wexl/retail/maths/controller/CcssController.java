package com.wexl.retail.maths.controller;

import com.wexl.retail.maths.dto.CcssDto;
import com.wexl.retail.maths.service.CcssService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/orgs/{orgSlug}/ccss")
public class CcssController {

  private final CcssService ccssService;

  @GetMapping
  public CcssDto.CcssResponse getCcssCurriculum() {
    return ccssService.getCcssCurriculum();
  }

  @GetMapping("/standards")
  public CcssDto.CcssStandards getStandardsByGradeAndDomainAndCluster(
      @RequestParam("grade_slug") String ccssGradeSlug,
      @RequestParam(value = "domain_id", required = false) Long domainId,
      @RequestParam(value = "cluster_id", required = false) Long clusterId) {
    return ccssService.getStandardsByGradeAndDomainAndCluster(ccssGradeSlug, domainId, clusterId);
  }
}
