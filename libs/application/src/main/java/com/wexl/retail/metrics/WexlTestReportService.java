package com.wexl.retail.metrics;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.util.Constants;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WexlTestReportService {
  @Autowired private WexlTestRepository wexlTestRepository;

  public List<GenericMetricResponse> getWexlTestsReport(
      String org, List<String> subjects, List<String> gradeSlugs, Integer timePeriod) {
    LocalDate toDate = LocalDate.now();
    LocalDate fromDate = LocalDate.now().minusWeeks(timePeriod);
    var examType = Constants.TEST_EXAM;

    var wexlTestReport =
        wexlTestRepository.getWexlTestsReport(
            org, fromDate.toString(), toDate.toString(), subjects, gradeSlugs, examType);

    return wexlTestReport.stream()
        .map(
            wexlTestReports ->
                GenericMetricResponse.builder()
                    .data(data(wexlTestReports))
                    .summary(summary(org, subjects, gradeSlugs))
                    .date(
                        DateTimeUtil.convertIso8601ToEpoch(
                            wexlTestReports.getDate().toLocalDateTime()))
                    .build())
        .toList();
  }

  private Map<String, Object> summary(String org, List<String> subject, List<String> gradeSlugs) {
    Map<String, Object> map = new HashMap<>();
    map.put("org", org);
    map.put("grade_list", gradeSlugs);
    map.put("subject_list", subject);
    return map;
  }

  private Map<String, Object> data(WexlTestData wexlTestData) {
    Map<String, Object> map = new HashMap<>();
    map.put("grade_name", wexlTestData.getGradeName());
    map.put("student_name", wexlTestData.getName());
    map.put("subject_name", wexlTestData.getSubject());
    map.put("chapter_name", wexlTestData.getChapter());
    map.put(
        "percentage",
        (wexlTestData.getPercentage() == null ? "0" : wexlTestData.getPercentage().toString()));
    map.put(
        "marks_scored",
        (wexlTestData.getMarksScored() == null ? "0" : wexlTestData.getMarksScored().toString()));
    map.put(
        "total_marks",
        (wexlTestData.getMarksScored() == null ? "0" : wexlTestData.getTotalMarks().toString()));
    map.put("no_of_question", wexlTestData.getNoOfQuestion().toString());
    return map;
  }
}
