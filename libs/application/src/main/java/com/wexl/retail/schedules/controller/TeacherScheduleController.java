package com.wexl.retail.schedules.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.commons.security.annotation.LegacyApi;
import com.wexl.retail.schedules.dto.AggregatedScheduleResponse;
import com.wexl.retail.schedules.dto.ScheduleRequest;
import com.wexl.retail.schedules.dto.ScheduleResponse;
import com.wexl.retail.schedules.service.ScheduleService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@IsTeacher
@RestController
@RequestMapping("/orgs/{org}/teachers/{teacherId}")
public class TeacherScheduleController {

  @Autowired private AuthService authService;
  @Autowired private ScheduleService scheduleService;

  @PostMapping("/schedules")
  @LegacyApi
  public ScheduleResponse createSchedule(
      @PathVariable("org") String organization, @RequestBody ScheduleRequest scheduleRequest) {
    scheduleRequest.setOrganization(organization);
    return scheduleService.createSchedule(scheduleRequest);
  }

  @PutMapping("/schedules/{id}")
  @LegacyApi
  public ScheduleResponse editSchedule(
      @PathVariable("org") String organization,
      @PathVariable("id") Long scheduleId,
      @RequestBody ScheduleRequest scheduleRequest) {
    scheduleRequest.setOrganization(organization);
    scheduleRequest.setId(scheduleId);
    return scheduleService.editSchedule(scheduleRequest);
  }

  @GetMapping("/schedules")
  @LegacyApi
  public List<ScheduleResponse> getAllTeacherSchedules() {
    var teacherId = authService.getTeacherDetails().getTeacherInfo().getId();
    return scheduleService.getAllTeacherSchedules(teacherId);
  }

  @GetMapping("/schedules:aggregate")
  @LegacyApi
  public List<AggregatedScheduleResponse> getAllTeacherSchedulesAggregated() {
    var teacherId = authService.getTeacherDetails().getTeacherInfo().getId();
    return scheduleService.getAllTeacherSchedulesAggregated(teacherId);
  }

  @DeleteMapping("/schedules/{id}")
  @LegacyApi
  public void deleteSchedule(
      @PathVariable("org") String organization, @PathVariable("id") Long scheduleId) {
    scheduleService.deleteSchedule(organization, scheduleId);
  }
}
