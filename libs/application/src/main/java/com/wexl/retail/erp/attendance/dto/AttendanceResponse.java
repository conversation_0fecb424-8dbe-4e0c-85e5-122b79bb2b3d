package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AttendanceResponse {
  @JsonProperty("section_id")
  private String section;

  private Integer date;

  @JsonProperty("status")
  private CompletionStatus status;
}
