package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class CompetitiveExamValidatorProcessor {
  private final List<CompetitiveExamProcessor> processors;

  public TestDefinition buildTestDefinitionData(
      CompetitiveExamsDto.Request request, String orgSlug) {
    final CompetitiveExamProcessor competitiveExamProcessor =
        getCompetitiveExamProcessor(request.testCategory());
    return competitiveExamProcessor.buildTestDefinition(request, orgSlug);
  }

  public CompetitiveExamProcessor getCompetitiveExamProcessor(TestCategory testCategory) {
    final Optional<CompetitiveExamProcessor> possibleCompetitiveExamProcessor =
        processors.stream().filter(p -> p.supports(testCategory)).findFirst();
    if (possibleCompetitiveExamProcessor.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid ExamType");
    }
    return possibleCompetitiveExamProcessor.get();
  }

  public int getTotalMarks(TestCategory testCategory, Float totalMarks) {
    final Optional<CompetitiveExamProcessor> possibleCompetitiveExamProcessor =
        processors.stream().filter(p -> p.supports(testCategory)).findFirst();

    if (testCategory.equals(TestCategory.STANDARD)) {
      return totalMarks != null ? totalMarks.intValue() : 0;
    } else if (testCategory.equals(TestCategory.LWS)) {
      return totalMarks != null ? totalMarks.intValue() : 0;
    } else if (testCategory.equals(TestCategory.BET)) {
      return totalMarks != null ? totalMarks.intValue() : 0;
    } else if (testCategory.equals(TestCategory.BET_LWS)) {
      return totalMarks != null ? totalMarks.intValue() : 0;
    } else {
      return possibleCompetitiveExamProcessor
          .map(CompetitiveExamProcessor::getTotalMarks)
          .orElse(-1);
    }
  }
}
