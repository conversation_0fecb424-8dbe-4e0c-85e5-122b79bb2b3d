package com.wexl.retail.term.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "term_assessment_grades")
public class TermAssessmentGrade extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("board_slug")
  private String boardSlug;

  @JsonProperty("board_name")
  private String boardName;

  @ManyToOne(fetch = FetchType.LAZY)
  private TermAssessment termAssessments;
}
