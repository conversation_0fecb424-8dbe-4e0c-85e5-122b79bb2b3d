package com.wexl.retail.offlinetest.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateGradeRepository;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.reportcards.model.StudentReportCard;
import com.wexl.retail.reportcards.model.StudentReportCardStatus;
import com.wexl.retail.reportcards.repository.StudentReportCardRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.subjects.service.SubjectsMetaDataService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.io.*;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class ReportCardTemplateService {

  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final StorageService storageService;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final ReportCardTemplateGradeRepository reportCardTemplateGradeRepository;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final SubjectsMetaDataService subjectsMetaDataService;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final String REPORT_CARD = "report-cards";
  private final String ADMIT_CARD = "admit-cards";
  private final String PDF = "pdf";
  private final ValidationUtils validationUtils;
  private final StudentReportCardRepository studentReportCardRepository;

  public List<ReportCardTemplateDto.Response> getReportCardTemplatesByOrg(
      String orgSlug, ReportCardTemplateType type, String grade, String board) {

    List<ReportCardTemplateDto.Response> responseList = new ArrayList<>();

    List<ReportCardTemplate> reportCardTemplateData = new ArrayList<>();
    if (Objects.nonNull(board) && Objects.nonNull(grade)) {
      reportCardTemplateData.addAll(
          reportCardTemplateRepository.getTemplateByOrgGradeSlugAndBoardAndType(
              orgSlug, type.name(), grade, board));
    } else {
      reportCardTemplateData.addAll(
          reportCardTemplateRepository.findByReportCardTemplateTypeAndOrgSlug(type, orgSlug));
    }
    if (reportCardTemplateData.isEmpty()) {
      return Collections.emptyList();
    }
    reportCardTemplateData.forEach(
        report ->
            responseList.add(
                ReportCardTemplateDto.Response.builder()
                    .reportCardTemplateType(report.getReportCardTemplateType())
                    .id(report.getId())
                    .config(report.getConfig())
                    .name(report.getName())
                    .orgSlug(report.getOrgSlug())
                    .build()));
    return responseList;
  }

  @Async
  @Transactional
  public void generateReportCardAsync(
      String orgSlug, Long reportCardTemplateId, ReportCardDto.Request request) {
    try {
      var section = sectionRepository.findAllByUuid(UUID.fromString(request.sectionUuid()));
      List<Student> students = studentRepository.getStudentsBySection(section);
      var activeStudents =
          students.stream().filter(s -> s.getUserInfo().getDeletedAt() == null).toList();
      byte[] pdf =
          getAllStudentsReportCardTemplate(
              orgSlug, reportCardTemplateId, activeStudents, request.offlineTestDefinitionId());
      String uploadPath = createReportCardUploadPath(orgSlug, request.offlineTestDefinitionId());
      storageService.uploadFile(pdf, uploadPath, MediaType.APPLICATION_PDF_VALUE);
      offlineTestDefinitionRepository
          .findById(request.offlineTestDefinitionId())
          .ifPresentOrElse(
              offlineTestDefinition -> {
                offlineTestDefinition.setGeneratedReportCardPath(uploadPath);
                offlineTestDefinitionRepository.save(offlineTestDefinition);
              },
              () ->
                  log.error(
                      "No record found with that OfflineTestDefinitionID : {}",
                      request.offlineTestDefinitionId()));
    } catch (IOException e) {
      log.error("Could not generate report card : {}", e.getMessage(), e);
    }
  }

  public byte[] getAllStudentsReportCardTemplate(
      String orgSlug,
      Long reportCardTemplateId,
      List<Student> students,
      Long offlineTestDefinitionId)
      throws IOException {
    List<byte[]> reportCards = new ArrayList<>();
    var sortedStudents =
        students.stream()
            .filter(student -> student.getUserInfo().getDeletedAt() == null)
            .sorted(
                Comparator.comparing(
                    Student::getClassRollNumber,
                    Comparator.nullsLast(Comparator.comparingLong(Long::parseLong))))
            .toList();
    var reportCardTemplate = validateReportCardTemplateById(reportCardTemplateId);

    var studentReportCards =
        studentReportCardRepository.findByReportCardConfigAndOfflineTestDefinitionIdAndTemplateType(
            reportCardTemplate.getConfig(), offlineTestDefinitionId, ReportCardTemplateType.CANNED);

    var studentReportCardMap =
        studentReportCards.stream()
            .collect(Collectors.toMap(StudentReportCard::getStudentId, Function.identity()));
    List<StudentReportCard> updatedStudentReportCards = new ArrayList<>();
    var localDateTime = LocalDateTime.now();
    for (var student : sortedStudents) {
      var request =
          ReportCardDto.Request.builder()
              .offlineTestDefinitionId(offlineTestDefinitionId)
              .studentAuthId(student.getUserInfo().getAuthUserId())
              .build();
      byte[] studentReport =
          offlineTestReportService.getStudentReportByOfflineTestDefinition(
              orgSlug, reportCardTemplate.getId(), request);

      if (studentReport != null && studentReport.length > 0) {
        var studentReportCard =
            validateStudentReportCard(
                studentReportCardMap.get(student.getId()),
                student,
                offlineTestDefinitionId,
                reportCardTemplate);
        try {
          var tempDir =
              String.format(
                  "/temp/%s/%s.pdf", localDateTime, student.getUserInfo().getAuthUserId());
          var file = convertByteArrayToFile(studentReport, new File(tempDir));
          if (Objects.nonNull(file)) {
            studentReportCard.setReportCardPath(
                constructCannedReportPath(
                    orgSlug,
                    reportCardTemplate.getConfig(),
                    offlineTestDefinitionId,
                    student.getUserInfo().getAuthUserId()));
            studentReportCard.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
            updatedStudentReportCards.add(studentReportCard);
          }
          reportCards.add(studentReport);
        } catch (Exception e) {
          studentReportCard.setFailureReason(e.getMessage());
        }
      }
    }
    studentReportCardRepository.saveAll(updatedStudentReportCards);
    var fileDirectory = new File(String.format("/temp/%s/", localDateTime));
    storageService.uploadDirectoryInBulk(
        fileDirectory,
        constructDr(orgSlug, reportCardTemplate.getConfig(), offlineTestDefinitionId));
    return mergeReportCards(reportCards);
  }

  @NotNull
  private ReportCardTemplate validateReportCardTemplateById(Long reportCardTemplateId) {
    return reportCardTemplateRepository
        .findById(reportCardTemplateId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.ReportCardTemplateNotFound"));
  }

  public byte[] mergeReportCards(List<byte[]> reportCards) {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    try {
      PDFMergerUtility pdfMerger = new PDFMergerUtility();
      Set<String> seenReports = new HashSet<>();
      for (byte[] report : reportCards) {
        String reportString = Base64.getEncoder().encodeToString(report);
        if (seenReports.add(reportString)) {
          pdfMerger.addSource(new ByteArrayInputStream(report));
        }
      }
      pdfMerger.setDestinationStream(outputStream);
      pdfMerger.mergeDocuments(null);
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Error merging PDF report cards", e);
    }
    return outputStream.toByteArray();
  }

  public byte[] mergeHolisticReportCards(List<byte[]> reportCards) {
    File mergedFile = null;
    Set<String> seenReports = new HashSet<>();
    try {
      mergedFile = Files.createTempFile("merged_report", ".pdf").toFile();
      PDFMergerUtility pdfMerger = new PDFMergerUtility();
      pdfMerger.setDestinationFileName(mergedFile.getAbsolutePath());

      for (byte[] report : reportCards) {
        File tempPdf = Files.createTempFile("report_card", ".pdf").toFile();

        try (FileOutputStream fos = new FileOutputStream(tempPdf);
            BufferedOutputStream bos = new BufferedOutputStream(fos)) {
          bos.write(report);
        }
        String hash = computeMD5Hash(tempPdf);
        if (seenReports.add(hash)) {
          pdfMerger.addSource(tempPdf);
        } else {
          Files.deleteIfExists(tempPdf.toPath());
        }
      }
      pdfMerger.mergeDocuments(null);
      return Files.readAllBytes(mergedFile.toPath());

    } catch (IOException e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Error merging PDF report cards", e);
    } finally {
      deleteFileIfExists(mergedFile);
    }
  }

  private String computeMD5Hash(File file) throws IOException {
    try (InputStream fis = new FileInputStream(file);
        BufferedInputStream bis = new BufferedInputStream(fis)) {
      MessageDigest md = MessageDigest.getInstance("MD5");
      byte[] buffer = new byte[1024];
      int bytesRead;

      while ((bytesRead = bis.read(buffer)) != -1) {
        md.update(buffer, 0, bytesRead);
      }

      // Convert hash to hex string
      StringBuilder sb = new StringBuilder();
      for (byte b : md.digest()) {
        sb.append(String.format("%02x", b));
      }
      return sb.toString();
    } catch (Exception e) {
      throw new IOException("Failed to generate file checksum", e);
    }
  }

  private void deleteFileIfExists(File mergedFile) {
    try {
      if (Objects.nonNull(mergedFile) && mergedFile.exists()) {
        Files.exists(mergedFile.toPath());
      }
    } catch (Exception e) {
      log.error("Failed to delete file {}", mergedFile.getName(), e);
    }
  }

  public String getAllStudentsReportCard(String orgSlug, Long offlineTestDefinitionId) {
    var offlineTestDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    var getReportCardPath = offlineTestDefinition.getGeneratedReportCardPath();
    if (getReportCardPath != null && !getReportCardPath.isEmpty()) {
      return storageService.generatePreSignedUrlForFetch(getReportCardPath);
    }
    throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.OfflineTestDefinitionId");
  }

  public String createReportCardUploadPath(String orgSlug, Long offlineTestDefinitionId) {
    return String.format("%s/%s/%s.%s", orgSlug, REPORT_CARD, offlineTestDefinitionId, PDF);
  }

  public String constructAdmitCardUploadPath(String orgSlug, Long offlineTestDefinitionId) {
    return String.format("%s/%s/%s.%s", orgSlug, ADMIT_CARD, offlineTestDefinitionId, PDF);
  }

  public void generateStudentsOverallAdmitCard(
      String orgSlug, String sectionUuid, Long reportCardTemplateId, Long offlineTestDefinitionId) {
    try {
      var offlineTestDefinition =
          offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
      var section = validationUtils.findSectionByUuid(sectionUuid);
      var students = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
      var activeStudents =
          students.stream()
              .filter(student -> student.getUserInfo().getDeletedAt() == null)
              .toList();
      List<byte[]> reportCards = new ArrayList<>();
      var reportCardTemplate = validateReportCardTemplateById(reportCardTemplateId);

      var studentReportCards =
          studentReportCardRepository
              .findByReportCardConfigAndOfflineTestDefinitionIdAndTemplateType(
                  reportCardTemplate.getConfig(),
                  offlineTestDefinitionId,
                  ReportCardTemplateType.CANNED);

      var studentReportCardMap =
          studentReportCards.stream()
              .collect(Collectors.toMap(StudentReportCard::getStudentId, Function.identity()));
      List<StudentReportCard> updatedStudentReportCards = new ArrayList<>();
      var localDateTime = LocalDateTime.now();
      for (var student : activeStudents) {
        var request =
            ReportCardDto.Request.builder()
                .studentAuthId(student.getUserInfo().getAuthUserId())
                .offlineTestDefinitionId(offlineTestDefinitionId)
                .build();
        var studentReportCard =
            validateStudentReportCard(
                studentReportCardMap.get(student.getId()),
                student,
                offlineTestDefinitionId,
                reportCardTemplate);
        try {
          byte[] admitCard =
              offlineTestReportService.getStudentReportByOfflineTestDefinition(
                  orgSlug, reportCardTemplateId, request);
          if (admitCard != null && admitCard.length > 0) {
            var tempDir =
                String.format(
                    "/temp/%s/%s.pdf", localDateTime, student.getUserInfo().getAuthUserId());
            var file = convertByteArrayToFile(admitCard, new File(tempDir));
            if (Objects.nonNull(file)) {
              studentReportCard.setReportCardPath(
                  constructCannedReportPath(
                      orgSlug,
                      reportCardTemplate.getConfig(),
                      request.offlineTestDefinitionId(),
                      student.getUserInfo().getAuthUserId()));
              reportCards.add(admitCard);
              updatedStudentReportCards.add(studentReportCard);
            }
          }
        } catch (Exception e) {
          log.info("Ignored error While generating students report card", e);
          studentReportCard.setFailureReason(e.getMessage());
        }
      }
      studentReportCardRepository.saveAll(updatedStudentReportCards);
      var fileDirectory = new File(String.format("/temp/%s/", localDateTime));
      storageService.uploadDirectoryInBulk(
          fileDirectory,
          constructDr(orgSlug, reportCardTemplate.getConfig(), offlineTestDefinitionId));
      var overallAdmitCard = mergeReportCards(reportCards);
      var uploadPath = constructAdmitCardUploadPath(orgSlug, offlineTestDefinitionId);
      storageService.uploadFile(overallAdmitCard, uploadPath, MediaType.APPLICATION_PDF_VALUE);
      offlineTestDefinition.setGeneratedReportCardPath(uploadPath);
      offlineTestDefinition.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
      offlineTestDefinitionRepository.save(offlineTestDefinition);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.CouldNotGenerateAdmitCard", e);
    }
  }

  public void generateStudentsOverallEyReportCard(
      String orgSlug,
      String sectionUuid,
      Long reportCardTemplateId,
      ReportCardDto.Request request) {
    var section = validationUtils.findSectionByUuid(sectionUuid);
    var students = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    var reportTemplate = reportCardTemplateRepository.findById(reportCardTemplateId);
    if (reportTemplate.isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid Report Card TemplateId:" + reportCardTemplateId);
    }
    List<byte[]> eyReportCards = new ArrayList<>();
    List<Student> studentsList = new ArrayList<>();
    Optional<SubjectsMetaData> metaData = Optional.empty();
    if (reportTemplate.get().getConfig().equals("sports-report-card.xml")) {
      metaData = subjectsMetaDataRepository.findById(request.offlineTestDefinitionId());
      List<SubjectsMetadataStudents> subjectMetaDataStudents =
          subjectsMetadataStudentsRepository.getSubjectMetaDataStudents(
              metaData.get().getId(), section.getId());
      var studentIds =
          subjectMetaDataStudents.stream().map(SubjectsMetadataStudents::getStudentId).toList();
      List<Student> studentsDetails = studentRepository.findAllByIdInAndDeletedAtIsNull(studentIds);
      studentsList.addAll(studentsDetails);
    } else {
      List<Student> sortedStudents =
          students.stream()
              .sorted(
                  Comparator.comparing(
                      student ->
                          StringUtils.isNumeric(student.getClassRollNumber())
                              ? Long.valueOf(student.getClassRollNumber())
                              : null,
                      Comparator.nullsLast(Comparator.naturalOrder())))
              .toList();
      studentsList.addAll(sortedStudents);
    }
    var reportCardTemplate = validateReportCardTemplateById(reportCardTemplateId);

    var studentReportCards =
        studentReportCardRepository.findByReportCardConfigAndOfflineTestDefinitionIdAndTemplateType(
            reportCardTemplate.getConfig(),
            request.offlineTestDefinitionId(),
            ReportCardTemplateType.CANNED);

    var studentReportCardMap =
        studentReportCards.stream()
            .collect(Collectors.toMap(StudentReportCard::getStudentId, Function.identity()));

    List<StudentReportCard> updatedStudentReportCards = new ArrayList<>();
    var localDateTime = LocalDateTime.now();
    for (var student : studentsList) {
      var req =
          ReportCardDto.Request.builder()
              .studentAuthId(student.getUserInfo().getAuthUserId())
              .offlineTestDefinitionId(
                  metaData.isPresent() ? metaData.get().getId() : request.offlineTestDefinitionId())
              .termId(request.termId())
              .withMarks(request.withMarks())
              .build();
      var studentReportCard =
          validateStudentReportCard(
              studentReportCardMap.get(student.getId()),
              student,
              metaData.isPresent() ? metaData.get().getId() : request.offlineTestDefinitionId(),
              reportCardTemplate);
      try {
        byte[] eyReportCard =
            offlineTestReportService.getStudentReportByOfflineTestDefinition(
                orgSlug, reportCardTemplateId, req);
        if (eyReportCard != null) {

          var tempDir =
              String.format(
                  "/temp/%s/%s.pdf", localDateTime, student.getUserInfo().getAuthUserId());
          var file = convertByteArrayToFile(eyReportCard, new File(tempDir));
          if (Objects.nonNull(file)) {
            studentReportCard.setReportCardPath(
                constructCannedReportPath(
                    orgSlug,
                    reportCardTemplate.getConfig(),
                    request.offlineTestDefinitionId(),
                    student.getUserInfo().getAuthUserId()));
            eyReportCards.add(eyReportCard);
          }
        } else {
          studentReportCard.setFailureReason("Data not uploaded for the respective grade");
        }
        studentReportCard.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
        updatedStudentReportCards.add(studentReportCard);
      } catch (Exception e) {
        studentReportCard.setFailureReason(e.getMessage());
        log.info("Ignored error While generating students report card", e);
      }
    }
    try {
      studentReportCardRepository.saveAll(updatedStudentReportCards);
      var fileDirectory = new File(String.format("/temp/%s/", localDateTime));
      storageService.uploadDirectoryInBulk(
          fileDirectory,
          constructDr(orgSlug, reportCardTemplate.getConfig(), request.offlineTestDefinitionId()));
      var overallReportCardPath =
          constructOverallReportCardPath(orgSlug, sectionUuid, reportCardTemplateId);
      var mergedReportCard =
          reportTemplate.get().getConfig().equals("overall-holistic-progress-report-card.xml")
              ? mergeHolisticReportCards(eyReportCards)
              : mergeReportCards(eyReportCards);
      storageService.uploadFile(
          mergedReportCard, overallReportCardPath, MediaType.APPLICATION_PDF_VALUE);
      log.info("Overall report card stored path :{}", overallReportCardPath);
    } catch (Exception e) {
      log.error("Failed to upload overall report : {}", e.getMessage(), e);
    }
  }

  private String constructCannedReportPath(
      String orgSlug, String reportCardConfig, long otdId, String authUserId) {
    return String.format("%s/report-cards/%s/%s/%s", orgSlug, reportCardConfig, otdId, authUserId);
  }

  private String constructDr(String orgSlug, String reportCardConfig, long otdId) {
    return String.format("%s/report-cards/%s/%s", orgSlug, reportCardConfig, otdId);
  }

  private static File convertByteArrayToFile(byte[] byteArray, File file) {
    try {
      FileUtils.writeByteArrayToFile(file, byteArray);
      return file;
    } catch (Exception e) {
      log.error("Could not convert  bytes into file :{}", e.getMessage(), e);
      return null;
    }
  }

  private StudentReportCard validateStudentReportCard(
      StudentReportCard studentReportCard,
      Student student,
      Long otdId,
      ReportCardTemplate template) {
    if (Objects.nonNull(studentReportCard)) {
      return studentReportCard;
    }
    return StudentReportCard.builder()
        .studentId(student.getId())
        .status(StudentReportCardStatus.REPORT_CARD_GENERATED)
        .reportCardTemplate(template.getId())
        .reportCardConfig(template.getConfig())
        .offlineTestDefinitionId(otdId)
        .templateType(ReportCardTemplateType.CANNED)
        .build();
  }

  private String constructOverallReportCardPath(
      String orgSlug, String sectionUuid, Long reportCardTemplateId) {
    return String.format("%s/%s/%s.pdf", orgSlug, sectionUuid, reportCardTemplateId);
  }

  public ReportCardTemplate getTemplateById(Long templateId) {
    var template = reportCardTemplateRepository.findById(templateId);
    if (template.isEmpty()) {
      throw new ApiException(
          com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST,
          "Invalid Report Card TemplateId:" + templateId);
    }
    return template.get();
  }

  public S3FileUploadResult downloadReport(String orgSlug, String sectionUuid, Long templateId) {
    try {
      var filePath = constructOverallReportCardPath(orgSlug, sectionUuid, templateId);
      if (!storageService.isFileAvailable(filePath)) {
        throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.Refresh");
      }
      return S3FileUploadResult.builder()
          .previewUrl(storageService.generatePreSignedUrlForFetch(filePath))
          .build();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Unable to download overall report card : %s".formatted(e.getMessage()),
          e);
    }
  }
}
