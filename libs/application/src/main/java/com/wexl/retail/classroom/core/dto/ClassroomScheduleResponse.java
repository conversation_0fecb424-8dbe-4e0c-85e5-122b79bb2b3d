package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassroomScheduleResponse {

  private Long id;

  private String title;

  @JsonProperty("meeting_id")
  private Long meetingRoomId;

  @JsonProperty("meeting_name")
  private String meetingRoomName;

  @JsonProperty("meeting_link")
  private String meetingRoomLink;

  @JsonProperty("meeting_start_time")
  private Long meetingStartTime;

  @JsonProperty("meeting_end_time")
  private Long meetingEndTime;

  @JsonProperty("start_date")
  private Long startDate;

  @JsonProperty("expiry_date")
  private Long endDate;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("day_of_week")
  private String dayOfWeek;
}
