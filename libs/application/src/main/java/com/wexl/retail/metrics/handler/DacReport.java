package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DacReport extends AbstractMetricHandler {

  @Override
  public String name() {
    return "dac-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return teacherService.getDacReport(org);
  }
}
