package com.wexl.retail.dspace.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.dspace.dto.GraphResponse;
import com.wexl.retail.storage.StorageService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
public class DSpaceController {

  private final StorageService storageService;

  @GetMapping("/public/dspace-stats")
  @ResponseBody
  public GraphResponse getDSpaceStatistics() {
    try {
      return storageService.downloadFile("dspace/sunburst.json", GraphResponse.class);
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.CannotFindConfiguration");
    }
  }
}
