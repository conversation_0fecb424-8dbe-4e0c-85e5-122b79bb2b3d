package com.wexl.retail.classroom.core.controller;

import com.wexl.retail.classroom.core.handler.ClassroomScheduleInstHandler;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/students/{authUserId}")
public class ClassroomStudentController {

  private final ClassroomService classroomService;
  private final List<ClassroomScheduleInstHandler> classroomScheduleInstHandler;

  @GetMapping("/classroom:activity")
  public List<Long> getClassroomDates(
      @PathVariable("authUserId") String studentId,
      @RequestParam(required = false, defaultValue = "500") int limit) {
    return classroomScheduleInstHandler.getFirst().getClassroomDates(studentId, limit);
  }

  @GetMapping("/activity")
  public List<StudentScheduleResponse> getStudentClassroomsByDate(
      @PathVariable("authUserId") String studentId,
      @PathVariable String orgSlug,
      @RequestParam Long date,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    return classroomScheduleInstHandler
        .getFirst()
        .getStudentClassroomsByDate(studentId, orgSlug, date, limit);
  }
}
