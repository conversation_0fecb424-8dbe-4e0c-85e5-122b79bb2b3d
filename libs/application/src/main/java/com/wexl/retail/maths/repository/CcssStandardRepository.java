package com.wexl.retail.maths.repository;

import com.wexl.retail.maths.model.CcssStandard;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CcssStandardRepository extends JpaRepository<CcssStandard, Long> {

  @Query(
      value =
          """
            select cs.* from ccss_standards cs
            join ccss_clusters cc on cc.id = cs.ccss_cluster_id
            join ccss_domains cd on cd.id = cc.ccss_domain_id
            join ccss_grades cg on cg.id = cd.ccss_grade_id
            where cg.grade_slug = :gradeSlug
            and (cast((:domainId) as varChar) is null or cd.id  in (:domainId))
            and (cast((:clusterId) as varChar) is null or cc.id  in (:clusterId))""",
      nativeQuery = true)
  List<CcssStandard> getCcssStandardsByGradeSlugAndDomainAndCluster(
      String gradeSlug, Long domainId, Long clusterId);
}
