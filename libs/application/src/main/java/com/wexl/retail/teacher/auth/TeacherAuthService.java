package com.wexl.retail.teacher.auth;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.OtpVerificationRequest;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.otp.OtpServiceLegacy;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.section.service.TeacherSectionService;
import com.wexl.retail.util.Constants;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherAuthService {

  private final UserIdpService userIdpService;
  private final UserRepository userRepository;
  private final TeacherRepository teacherRepository;
  private final EmailService emailService;
  private final OtpServiceLegacy otpServiceLegacy;
  private final TeacherAuthTransformer teacherAuthTransformer;
  private final SectionService sectionService;
  private final TeacherSectionService teacherSectionService;
  private final List<EntityHandler<Teacher>> teacherHandler;

  public User createTeacherInBulk(TeacherSignupRequest teacherInfo) {
    try {
      // This is a random UUID as the teacher's username will have email address
      // difficult to send email address in REST API URL params
      String authUserId = UUID.randomUUID().toString();
      return createUserInDatabase(teacherInfo, authUserId, true, UserVerificationStatus.VERIFIED);
    } catch (ApiException one) {
      throw one;
    } catch (Exception une) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Username.Exists", une);
    }
  }

  private User createUserInDatabase(
      TeacherSignupRequest teacherInfo,
      String username,
      Boolean isEmailVerified,
      UserVerificationStatus userVerificationStatus)
      throws ApiException {
    if (username == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.TeacherCreate.Email",
          new String[] {teacherInfo.getEmailAddress()});
    }

    var teacherDetails =
        teacherAuthTransformer.mapTeacherSignupRequest(
            teacherInfo, username, isEmailVerified, userVerificationStatus);
    var user = userRepository.save(teacherDetails.getUserInfo());
    teacherDetails.setUserInfo(user);
    teacherDetails = teacherRepository.save(teacherDetails);
    teacherDetails.setTeacherCode("WEXL" + teacherDetails.getId());

    teacherRepository.save(teacherDetails);
    Teacher finalTeacherDetails = teacherDetails;
    teacherHandler.forEach(handler -> handler.postSave(finalTeacherDetails));
    return user;
  }

  public OtpResponse sendEmailOtp(long userId) {
    try {
      var user = userRepository.getById(userId);
      return otpServiceLegacy.sendOtp(
          user.getEmail(),
          user.getFirstName(),
          user.getLastName(),
          Constants.EMAIL_OTP_REFERENCE,
          user);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.EmailNotFound");
    }
  }

  public User verifyEmailOtp(OtpVerificationRequest verificationRequest) {

    var user = userRepository.getById(verificationRequest.getUserId());
    boolean otpStatus =
        otpServiceLegacy.verifyOtp(verificationRequest.getOtp(), verificationRequest.getOtpId());
    if (otpStatus) {
      if (user.getEmailVerified() == null || !user.getEmailVerified()) {
        emailService.sendEmailAfterTeacherRegistration(
            user.getFirstName(), user.getLastName(), user.getEmail());
        user.setEmailVerified(true);
        userRepository.save(user);
      }
      return user;
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OTP");
    }
  }

  public void deleteTeacher(User teacherUser) {
    teacherUser.setIsDeleted(true);
    teacherUser.setDeletedAt(new Date());
    userRepository.save(teacherUser);
  }

  public String deleteTeacher(String teacherAuthId, String orgSlug) {
    return userRepository.deleteTeacher(teacherAuthId, orgSlug);
  }

  public void updatePassword(String teacherId, String password) {
    userIdpService.adminSetUserPassword(teacherId, password);
  }
}
