package com.wexl.retail.courses.enrollment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.courses.enrollment.model.CourseEnrollmentMetadata.GradeCourseEnrollmentMetadata;
import com.wexl.retail.courses.enrollment.model.CourseEnrollmentMetadata.SectionCourseEnrollmentMetadata;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CourseEnrollmentRequest {

  @JsonProperty("start_date")
  private long startDate;

  @JsonProperty("end_date")
  private long endDate;

  private List<SectionCourseEnrollmentMetadata> sections;

  private List<GradeCourseEnrollmentMetadata> grades;

  private List<Long> students;

  private List<String> teachers;

  @JsonProperty("course_category")
  private Long courseCategory;

  private Long duration;
}
