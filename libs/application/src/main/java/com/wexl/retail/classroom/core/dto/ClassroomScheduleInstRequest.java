package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ClassroomScheduleInstRequest {
  @NotNull private String title;
  @NotNull private ClassroomMeetingStatus status;

  @JsonProperty("host_join_time")
  private Long hostJoinTime;
}
