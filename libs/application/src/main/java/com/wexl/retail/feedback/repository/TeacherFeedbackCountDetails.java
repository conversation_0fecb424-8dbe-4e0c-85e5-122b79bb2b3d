package com.wexl.retail.feedback.repository;

public interface TeacherFeedbackCountDetails {
  Long getTeacherId();

  Long getTaskInstId();

  String getClassroomName();

  String getChapterName();

  String getChapterSlug();

  String getSubjectName();

  String getSubjectSlug();

  String getSubtopicSlug();

  String getSubtopicName();

  Long getTaskId();

  String getTaskName();

  Long getStudentId();

  Boolean getFeedBack();

  Long getId();

  String getMessage();
}
