package com.wexl.retail.student.attributes.controller;

import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@AllArgsConstructor
@RestController
public class StudentAttributeController {

  private final StudentAttributeService studentAttributeService;

  @PostMapping("/orgs/{orgSlug}/students/{studentAuthId}/attributes")
  public void saveStudentAttributes(
      @RequestBody StudentAttributeDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId) {
    studentAttributeService.saveStudentDefinitionAttributes(studentAuthId, orgSlug, request);
  }

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/attributes")
  public Map<String, String> getStudentAttributes(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestParam(value = "academic_year", required = false) String academicYear) {
    return studentAttributeService.getStudentAttributes(studentAuthId, academicYear);
  }
}
