package com.wexl.retail.mlp.repository;

import com.wexl.retail.mlp.dto.MlpPracticeVideoSynopsisCount;
import com.wexl.retail.mlp.dto.StudentsDetailsByMlpResponse;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpInst;
import com.wexl.retail.model.Student;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface MlpInstRepository extends CrudRepository<MlpInst, Long> {

  List<MlpInst> findAllByMlpAndExamIsNotNull(Mlp mlp);

  @Transactional
  @Modifying
  @Query(
      value =
          "update mlp_inst set video_status = :status where mlp_id = :mlpId and student_id  = :studentId",
      nativeQuery = true)
  void updateVideoStatusForStudent(String status, Long mlpId, Long studentId);

  @Transactional
  @Modifying
  @Query(
      value =
          "update mlp_inst set synopsis_status=:status where mlp_id=:mlpId and student_id=:studentId",
      nativeQuery = true)
  void updateSynopsisStatusForStudent(String status, Long mlpId, Long studentId);

  Optional<MlpInst> getMlpInstByMlpAndStudent(Mlp mlp, Student student);

  @Query(
      value =
          """
              select mi. * from mlp_inst mi
                             join students s on mi.student_id = s.id where mlp_id in (:mlpIds)
                             and s.user_id is not null order by mlp_id desc limit 500
              """,
      nativeQuery = true)
  List<MlpInst> getAllMlpInst(List<Long> mlpIds);

  @Query(
      value =
          """
                          select sum(CASE  WHEN synopsis_Status='COMPLETED' THEN 1 else 0 end ) as synopsisCount,
                          sum(CASE  WHEN video_Status='COMPLETED' THEN 1 else 0 end ) as videoCount,
                          sum(CASE when  exam_id is not null THEN 1 else 0 end ) as practiceCount
                          from mlp_inst where mlp_id in (:mlpIds)
                          """,
      nativeQuery = true)
  MlpPracticeVideoSynopsisCount getAttemptedVideoAndSynopsisCount(List<Long> mlpIds);

  @Query(
      value =
          """
select count(*) > 0 from mlp_inst mi
inner join students s on mi.student_id = s.id
inner join users u on s.user_id = u.id
where u.id = :userId and mi.knowledge_percentage = 100
""",
      nativeQuery = true)
  Optional<Boolean> haveScoredFullMarksInAnyMlp(long userId);

  @Query(
      value =
          """
select count(mi.*) = count(exam_id) from mlp_inst mi
 inner join students s on mi.student_id = s.id
 inner join users u on s.user_id = u.id
where u.id = :userId
""",
      nativeQuery = true)
  Optional<Boolean> hasAttemptedAllMlps(long userId);

  @Query(
      value =
          """
                  select concat(u.first_name,' ',u.last_name) as fullName,u.auth_user_id as userName,se.name as sectionName,
                                                                            se.grade_name as gradeName,s.school_name as schoolName,
                                                                            s.board_id as boardId,m.id as mlpId,mi.attendance_percentage as attendancePercentage,
                                                                            mi.knowledge_percentage as knowledgePercentage,exam_id as examId,
                                                                            case when m.question_count is null then 'NOT_ASSIGNED'
                                                                            when m.question_count is not null and mi.exam_id is null then 'NOT_COMPLETED'
                                                                            when m.question_count is not null AND mi.exam_id is not null THEN 'COMPLETED' END as practiceStatus,
                                                                            case  when  m.question_count is not null AND mi.exam_id is not null THEN mi.updated_at else null END as date,
                                                                            case when m.video_slug is not null and mi.video_status='COMPLETED' then 'COMPLETED'\s
                                                                            when m.video_slug is null then 'NOT_ASSIGNED' else 'NOT_COMPLETED' end as videoStatus,
                                                                            case when m.synopsis_slug is not null and mi.synopsis_status='COMPLETED' then 'COMPLETED' \s
                                                                            when m.synopsis_slug is null then 'NOT_ASSIGNED' else 'NOT_COMPLETED' end as synopsisStatus ,
                                                                            m.title as mlpName from mlp_inst mi
                                                                            join mlp m on m.id=mi.mlp_id
                                                                            inner join students s on mi.student_id = s.id
                                                                            inner join sections se on se.id = s.section_id
                                                                            inner join users u on u.id = s.user_id
                                                                            where org_slug=:orgSlug and u.auth_user_id=:studentAuthId""",
      nativeQuery = true)
  List<StudentsDetailsByMlpResponse> getStudentDetailByMlp(String orgSlug, String studentAuthId);
}
