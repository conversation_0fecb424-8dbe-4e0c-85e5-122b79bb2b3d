package com.wexl.retail.mlp.controller;

import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.service.KnowledgeMeterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/knowledge-meter")
@RequiredArgsConstructor
public class KnowledgeMeterController {

  private final KnowledgeMeterService knowledgeMeterService;

  @PostMapping("/summary")
  public ResponseEntity<KMGenericSummaryResponse> getKmSummary(
      @PathVariable String orgSlug,
      @RequestBody KMBoardRequest request,
      @RequestParam String board) {

    return ResponseEntity.ok()
        .body(knowledgeMeterService.getKmSummary(request.getGrades(), orgSlug));
  }

  @PostMapping("/detail")
  public ResponseEntity<KMGenericGradesResponse> getKmGradesSummary(
      @PathVariable String orgSlug, @RequestParam String grades) {
    return ResponseEntity.ok(knowledgeMeterService.getKmGrades(orgSlug, grades));
  }

  @PostMapping("/sections/{sectionUuid}/knowledge-chapter-detail")
  ResponseEntity<KMGenericSectionResponse> getKmSectionSummary(
      @PathVariable String orgSlug, @PathVariable String sectionUuid) {

    return ResponseEntity.ok(knowledgeMeterService.getKmSectionSummary(orgSlug, sectionUuid));
  }
}
