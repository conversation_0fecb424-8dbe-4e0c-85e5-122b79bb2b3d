package com.wexl.retail.metrics;

import com.wexl.retail.metrics.dto.PracticeReportData;
import com.wexl.retail.student.exam.Exam;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PracticeReportRepository extends JpaRepository<Exam, Long> {
  @Query(
      value =
          """
           with practiceExams as(
             select e.* from exams e
             left join mlp m on m.exam_ref = e.ref
             where  m.exam_ref is null and e.exam_type =:examType
             and to_char(e.start_time , 'YYYY-MM-DD') >= :fromDate and e.end_time is not null
             and (cast((:subjectSlugs) as varChar) is null or e.subject_slug in (:subjectSlugs)) order by e.subtopic_slug  )
             select se.grade_name as gradeName ,pe.subject_name as subject, se.name as section ,pe.chapter_name as chapter, st.name as subTopic ,
              COALESCE(count(ea.question_id),0) as questionAttempted,COALESCE(count(distinct pe.id),0) as  examAttempted , COALESCE(pe.marks_scored/count(distinct pe.id),0) as average
              from practiceExams pe
              inner join sections se on se.id = pe.section_id
              inner join sub_topics st on st.chapter = pe.chapter_id
              inner join students ss on ss.id = pe.student_id
              inner join users u on u.id = ss.user_id
              inner join exam_answers ea on pe.id = ea.exam_id
              where  (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs)) and u.organization= :orgSlug
              group by pe.chapter_name,pe.start_time,pe.subject_name,pe.chapter_name, pe.no_of_questions,
              pe.marks_scored,pe.total_marks,se.grade_name ,subtopic_name , st.name ,se.name  """,
      nativeQuery = true)
  List<PracticeReportData> getPracticeTestReport(
      String orgSlug,
      LocalDate fromDate,
      List<String> subjectSlugs,
      List<String> gradeSlugs,
      long examType);
}
