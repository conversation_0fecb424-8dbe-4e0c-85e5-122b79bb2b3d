package com.wexl.retail.elp.repository;

import com.wexl.retail.model.StudentRegistrationCode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentRegistrationCodeRepository
    extends JpaRepository<StudentRegistrationCode, Long> {

  Optional<StudentRegistrationCode> findByCodeAndOrgSlugAndExpiryDateAfter(
      String code, String orgSlug, LocalDateTime expiryDate);

  @Query(
      value =
          """
          select * from student_registration_codes src  where src.org_slug  = :orgSlug and  src.code in (:codes) and
           src.is_used  is not true""",
      nativeQuery = true)
  List<StudentRegistrationCode> getAllByOrgSlugAndCodesAndIsNotUsed(
      List<String> codes, String orgSlug);

  List<StudentRegistrationCode> findAllByOrgSlugOrderByIdDesc(String orgSlug, Pageable limit);
}
