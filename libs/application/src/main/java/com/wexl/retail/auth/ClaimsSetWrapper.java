package com.wexl.retail.auth;

import com.nimbusds.jwt.JWTClaimsSet;
import java.util.Optional;

public class ClaimsSetWrapper {

  private static final String USERNAME = "cognito:username";
  private static final String GIVEN_NAME = "given_name";
  private static final String FAMILY_NAME = "family_name";
  private static final String EMAIL = "email";
  private JWTClaimsSet claimsSet;

  public ClaimsSetWrapper(JWTClaimsSet claimsSet) {
    this.claimsSet = claimsSet;
  }

  public String getIssuer() {
    return claimsSet.getIssuer();
  }

  public String getUsername() {
    return claimsSet.getClaims().get(USERNAME).toString();
  }

  public String getGivenName() {
    return claimsSet.getClaims().get(GIVEN_NAME).toString();
  }

  public String getFamilyName() {
    return getClaimOrDefault(FAMILY_NAME);
  }

  public String getEmail() {
    return getClaimOrDefault(EMAIL);
  }

  private String getClaimOrDefault(String claimName) {
    return Optional.ofNullable(claimsSet.getClaims().get(claimName))
        .map(Object::toString)
        .orElse("");
  }
}
