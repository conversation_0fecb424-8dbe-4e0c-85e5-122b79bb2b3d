package com.wexl.retail.student.badge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.UserBadgeContext;
import java.sql.Date;
import java.util.List;
import lombok.Data;

@Data
public class StudentBadgeDto {

  private String name;

  @JsonProperty("badge_id")
  private long badgeId;

  @JsonProperty("badge_expiry_in_days")
  private int badgeExpiryInDays;

  @JsonProperty("badge_image_url")
  private String badgeImageUrl;

  @JsonProperty("description")
  private String description;

  @JsonProperty("seq")
  private int seq;

  @JsonProperty("slug")
  private String slug;

  @JsonProperty("issued_date")
  private Date issuedDate;

  @JsonProperty("expiry_date")
  private Date expiryDate;

  @JsonProperty("badge_context")
  private List<UserBadgeContext> badgeContext;
}
