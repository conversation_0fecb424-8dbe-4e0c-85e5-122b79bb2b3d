package com.wexl.retail.metrics.handler;

import com.wexl.retail.feedback.service.TeacherFeedbackService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TeacherFeedbackCount extends AbstractMetricHandler implements MetricHandler {

  private final TeacherFeedbackService teacherFeedbackService;

  @Override
  public String name() {
    return "teacher-feedback-counts";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String orgSlug, GenericMetricRequest genericMetricRequest) {
    Long fromDate = (Long) genericMetricRequest.getInput().get("fromDate");
    Long toDate = (Long) genericMetricRequest.getInput().get("toDate");
    return teacherFeedbackService.getFeedbackCounts(orgSlug, fromDate, toDate);
  }
}
