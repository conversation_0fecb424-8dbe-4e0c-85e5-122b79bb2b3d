package com.wexl.retail.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mlp.dto.BulkMlpFileRequest;
import com.wexl.retail.mlp.dto.BulkMlpRequest;
import com.wexl.retail.mlp.dto.BulkMlpRequest.BulkMlpData;
import com.wexl.retail.mlp.dto.MlpFileResponse;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpInst;
import com.wexl.retail.mlp.repository.MlpInstRepository;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.mlp.service.omr.OmrResultFileProcessor;
import com.wexl.retail.omr.OmrDto;
import com.wexl.retail.storage.StorageService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MlpStudentOmrProcessor {

  private final StorageService storageService;
  private final MlpRepository mlpRepository;
  private final MlpInstRepository mlpInstRepository;
  private final OmrResultFileProcessor omrResultFileProcessor;

  @Value("${spring.profiles.active}")
  private String envProfile;

  public List<BulkMlpData> processMlpFile(String orgSlug, BulkMlpFileRequest bulkMlpFileRequest) {
    String filePath =
        getFilePath(
            orgSlug, bulkMlpFileRequest.getFileReference(), bulkMlpFileRequest.getExtension());
    Optional<Mlp> mlp = mlpRepository.findFirstByExamRef(bulkMlpFileRequest.getMlpReference());
    if (mlp.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MLPFind.ExamRef");
    }
    List<MlpInst> mlpInsts = mlpInstRepository.findAllByMlpAndExamIsNotNull(mlp.get());
    if (!mlpInsts.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.TimesUploaded");
    }
    var fileInputStream = storageService.getInputStream(filePath);
    final List<MlpFileResponse> mlpFileResponse = omrResultFileProcessor.process(fileInputStream);
    return buildMlpStudentData(mlpFileResponse, bulkMlpFileRequest.getMlpReference());
  }

  public String getFilePath(String orgSlug, String reference, String extension) {
    return "%s/omr/%s.%s".formatted(orgSlug, reference, extension);
  }

  public String getOmrFileUploadUrl(
      String orgSlug, String reference, OmrDto.OmrRequest omrRequest) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("fn", omrRequest.formName());
    metadata.put("tsi", String.valueOf(omrRequest.testScheduleId()));
    metadata.put("ns", envProfile);

    String path = "zips/%s/%s.zip".formatted(orgSlug, reference);
    String omrBucketName = "wexl-omr";
    return storageService.generatePreSignedUrlForUpload(omrBucketName, path, metadata);
  }

  private List<BulkMlpData> buildMlpStudentData(
      List<MlpFileResponse> mlpFileResponse, String mlpRef) {
    var mlp = mlpRepository.findFirstByExamRef(mlpRef);
    if (mlp.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MLPNotFound");
    }

    return mlpFileResponse.stream()
        .map(mlpResponse -> processMlpFileForStudent(mlp.get(), mlpResponse))
        .toList();
  }

  private BulkMlpData processMlpFileForStudent(Mlp mlp, MlpFileResponse mlpResponse) {
    return BulkMlpData.builder()
        .studentName(mlpResponse.getName())
        .authUserId(mlpResponse.getUserName())
        .questions(buildQuestions(mlpResponse, mlp.getQuestionUuids()))
        .build();
  }

  private List<BulkMlpRequest.Questions> buildQuestions(
      MlpFileResponse mlpFileResponse, List<String> questionUuid) {
    if (questionUuid.size() != mlpFileResponse.getSelectedQuestions().size()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Csv");
    }
    List<BulkMlpRequest.Questions> questionsList = new ArrayList<>();
    for (int i = 0; i < questionUuid.size(); i++) {
      questionsList.add(
          BulkMlpRequest.Questions.builder()
              .questionNo(i + 1L)
              .questionUuid(questionUuid.get(i))
              .rawAnswer(mlpFileResponse.getSelectedQuestions().get(i + 1))
              .selectedAnswer(getSelectedAnswer(mlpFileResponse.getSelectedQuestions().get(i + 1)))
              .build());
    }
    return questionsList;
  }

  public Integer getSelectedAnswer(String selectedAnswer) {
    return switch (selectedAnswer) {
      case "a" -> 1;
      case "b" -> 2;
      case "c" -> 3;
      case "d" -> 4;
      default -> null;
    };
  }
}
