package com.wexl.retail.device.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.device.dto.FirebaseTokenRequest;
import com.wexl.retail.device.dto.MobileAppUpdateStatus;
import com.wexl.retail.device.dto.UserDeviceInfoRequest;
import com.wexl.retail.device.service.DeviceService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{orgSlug}")
public class DeviceController {

  @Autowired private DeviceService deviceService;
  @Autowired private AuthService authService;

  @PostMapping("/users/{userId}/device-info")
  public ResponseEntity<HttpStatus> saveDeviceInfo(
      @RequestBody UserDeviceInfoRequest request, @PathVariable Long userId) {
    deviceService.addUserDeviceInfo(request, authService.getUserDetails().getAuthUserId());
    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @GetMapping("/users/{userId}/device-info")
  public ResponseEntity<String> getDeviceInfo() {
    String userAppVersion = deviceService.getUserAppVersion(authService.getUserDetails());
    return ResponseEntity.ok(userAppVersion);
  }

  @PostMapping("/users/{username}/firebase-tokens")
  public ResponseEntity<HttpStatus> saveFirebaseTokens(
      @RequestBody FirebaseTokenRequest request, @PathVariable String username) {
    deviceService.addUserFirebaseTokens(request, authService.getUserDetails());
    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @PostMapping("/users/{authUserId}/app-update-status")
  public ResponseEntity<MobileAppUpdateStatus> isAppUpdateAvailable(
      @RequestBody UserDeviceInfoRequest request, @PathVariable String authUserId) {
    return ResponseEntity.ok(deviceService.isAppUpdateAvailable(request, authUserId));
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/device-details", produces = "text/csv")
  public void getAppInfoDetails(
      @PathVariable String orgSlug, HttpServletResponse httpServletResponse) {

    String csvFileName = "studentsAppInfo.csv";
    httpServletResponse.setContentType("text/csv");
    httpServletResponse.setHeader(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + csvFileName);
    deviceService.writeAppInfoToCsv(orgSlug, httpServletResponse);
  }
}
