package com.wexl.retail.thread.controller;

import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.service.ThreadReplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class ThreadReplyController {

  private final ThreadReplyService threadReplyService;

  @IsTeacher
  @PostMapping("/teachers/{teacherAuthId}/threads/{threadId}")
  public void createThreadReply(
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable Long threadId,
      @PathVariable String teacherAuthId) {
    threadReplyService.createThreadReply(request, teacherAuthId, threadId);
  }

  @IsTeacher
  @PutMapping("/teachers/{teacherAuthId}/threads/{threadId}/thread-replies/{threadReplyId}")
  public void editThreadReply(
      @RequestBody ThreadDto.CreateThreadReplyRequest request,
      @PathVariable Long threadReplyId,
      @PathVariable String teacherAuthId) {
    threadReplyService.editThreadReply(threadReplyId, teacherAuthId, request);
  }
}
