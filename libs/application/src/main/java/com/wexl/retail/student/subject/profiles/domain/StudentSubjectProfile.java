package com.wexl.retail.student.subject.profiles.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(
    name = "student_subject_profiles",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"student_id", "subject_profile_id"})})
@EqualsAndHashCode(callSuper = true)
public class StudentSubjectProfile extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "subject_profile_id")
  private SubjectProfiles subjectProfile;
}
