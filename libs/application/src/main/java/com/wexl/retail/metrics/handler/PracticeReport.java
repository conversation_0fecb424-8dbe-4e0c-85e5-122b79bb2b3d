package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.PracticeReportService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PracticeReport extends AbstractMetricHandler implements MetricHandler {
  public final PracticeReportService practiceReportService;

  @Override
  public String name() {
    return "wexl-practice-report";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    List<String> subject =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());

    return practiceReportService.getPracticeTestByInstitute(
        org, subject, gradeList, genericMetricRequest.getTimePeriod());
  }
}
