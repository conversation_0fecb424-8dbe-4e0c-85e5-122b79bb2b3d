package com.wexl.retail.qpgen.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.security.annotation.LegacyApi;
import com.wexl.retail.qpgen.dto.QpGenDto;
import com.wexl.retail.qpgen.service.QpGenService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/qp-gens")
@RequiredArgsConstructor
public class QpGenController {

  private final QpGenService qpGenService;

  @LegacyApi
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping()
  public void saveQpGen(
      @PathVariable String orgSlug,
      @RequestBody QpGenDto.Request request,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    qpGenService.saveQpGen(orgSlug, request, bearerToken);
  }

  @LegacyApi
  @GetMapping()
  public List<QpGenDto.Response> getAllQpGens(@PathVariable String orgSlug) {
    return qpGenService.getAllQpGens(orgSlug);
  }

  @LegacyApi
  @GetMapping("/{id}")
  public QpGenDto.SectionsResponse getQpGenById(@PathVariable Long id) {
    return qpGenService.getQpGenById(id);
  }
}
