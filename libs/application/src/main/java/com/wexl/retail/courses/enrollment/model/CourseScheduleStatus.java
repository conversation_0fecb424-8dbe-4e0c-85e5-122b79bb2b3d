package com.wexl.retail.courses.enrollment.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CourseScheduleStatus {
  NOT_STARTED("NOT_STARTED"),
  IN_PROGRESS("IN_PROGRESS"),
  COMPLETED("COMPLETED");

  private final String value;

  public static CourseScheduleStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (var enumEntry : CourseScheduleStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
