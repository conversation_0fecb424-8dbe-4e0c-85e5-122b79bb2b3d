package com.wexl.retail.student.exam.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ExamDto {
  private String subtopic;
  private String subject;
  private String chapter;
  private Long scheduleId;
  private Boolean corrected;
  private String type;
  private Float marksScored;
  private Float totalMarks;
  private Float percentage;

  @JsonProperty("exam_id")
  private Long examId;

  @JsonProperty("completed_at")
  private Long completedAt;

  @JsonProperty("test_name")
  private String testName;
}
