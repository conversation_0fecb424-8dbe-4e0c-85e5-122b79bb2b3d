package com.wexl.retail.section.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.section.domain.TeacherSubjectsRequest;
import com.wexl.retail.section.dto.response.TeacherSubjectsResponse;
import com.wexl.retail.section.service.TeacherSubjectsService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/orgs/{orgId}/teachers/{authUserId}")
public class TeacherSubjectsController {

  private final TeacherSubjectsService teacherSubjectsService;

  @IsOrgAdmin
  @PostMapping(path = "/sections/{sectionUuid}/subjects")
  public void mapSubjectToTeacherSection(
      @PathVariable String orgId,
      @PathVariable("authUserId") String teacherUuid,
      @PathVariable String sectionUuid,
      @RequestBody TeacherSubjectsRequest teacherSubjectsRequest) {

    teacherSubjectsService.save(
        orgId,
        teacherSubjectsRequest.getBoardSlug(),
        teacherUuid,
        sectionUuid,
        teacherSubjectsRequest.getSubject(),
        teacherSubjectsRequest.getClassTeacher());
  }

  @IsTeacher
  @GetMapping(path = "/subjects")
  public List<TeacherSubjectsResponse> getSubjectsMappedToTeacher(
      @PathVariable("authUserId") String teacherUuid) {
    return teacherSubjectsService.findByTeacher(teacherUuid);
  }

  @IsOrgAdmin
  @DeleteMapping(path = "/sections/{sectionUuid}/subjects/{subjectSlug}")
  public void removeMappedSubjectToTeacher(
      @PathVariable String orgId,
      @PathVariable("authUserId") String teacherUuid,
      @PathVariable String sectionUuid,
      @PathVariable String subjectSlug,
      @RequestBody TeacherSubjectsRequest teacherSubjectsRequest) {
    try {
      teacherSubjectsService.deleteTeacherSectionSubject(
          orgId, teacherSubjectsRequest.getBoardSlug(), teacherUuid, sectionUuid, subjectSlug);
    } catch (Exception e) {
      log.error("Unable to delete subjects for given teacher", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.SubjectDelete.Teacher");
    }
  }
}
