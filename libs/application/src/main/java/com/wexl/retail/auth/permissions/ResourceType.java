package com.wexl.retail.auth.permissions;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
public enum ResourceType {
  VIDEOS("VIDEOS"),
  POINTS("POINTS"),
  REFERRAL("REFERRAL"),
  SUBSCRIPTION("SUBSCRIPTION"),
  SCHEDULE("SCHEDULE");

  private final String value;

  public static ResourceType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ResourceType enumEntry : ResourceType.values()) {
      if (enumEntry.toString().equals(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
  }

  @Override
  public String toString() {
    return this.value;
  }
}
