package com.wexl.retail.task.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StudentTasksResponse {

  @JsonProperty("task_inst_id")
  private Long taskInstId;

  @JsonProperty("task_id")
  private Long taskId;

  private String name;

  private String status;

  private String description;

  @JsonProperty("task_type")
  private String taskType;

  @JsonProperty("completed_at")
  private Long completedAt;

  @JsonProperty("subject_name")
  private String subjectName;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("chapter_slug")
  private String chapterSlug;

  @JsonProperty("chapter_id")
  private Long chapterId;

  @JsonProperty("chapter_name")
  private String chapterName;

  private float percentage;

  @JsonProperty("sub_topic_slug")
  private String subTopicSlug;

  @JsonProperty("sub_topic_id")
  private Long subTopicId;

  @JsonProperty("sub_topic_name")
  private String subTopicName;

  @JsonProperty("video_title")
  private String videoTitle;

  @JsonProperty("video_slug")
  private String videoSlug;

  @JsonProperty("alt_vimeo_link")
  private String altVimeoLink;

  @JsonProperty("video_source")
  private String videoSource;

  @JsonProperty("synopsis_slug")
  private String synopsisSlug;

  @JsonProperty("synopsis_title")
  private String synopsisTitle;

  @JsonProperty("question_count")
  private String questionCount;

  @JsonProperty("exam_id")
  private long examId;

  @JsonProperty("assignment_id")
  private Long assignmentId;

  @JsonProperty("scheduled_date")
  private Long scheduledDate;

  @JsonProperty("classroom_name")
  private String classRoomName;

  @JsonProperty("classroom_id")
  private String classRoomId;

  private String attendance;
}
