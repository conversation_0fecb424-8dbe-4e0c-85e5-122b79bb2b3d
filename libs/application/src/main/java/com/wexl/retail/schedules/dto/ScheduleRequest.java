package com.wexl.retail.schedules.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.schedules.domain.MeetingType;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.SneakyThrows;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScheduleRequest {
  @NonNull private String url;
  @NonNull private String name;
  @JsonIgnore private Long id;
  @JsonIgnore private String organization;

  @NonNull
  @JsonProperty("section_uuid")
  private String sectionUuid;

  @NonNull
  @JsonProperty("meeting_type")
  private MeetingType meetingType;

  @NonNull
  @JsonProperty("to_time")
  private Long toTime;

  @NonNull
  @JsonProperty("from_time")
  private Long fromTime;

  public Timestamp getScheduleTime() {
    return convertEpochToTimestamp(this.fromTime);
  }

  @SneakyThrows
  private Timestamp convertEpochToTimestamp(Long epochMillis) {
    var epochToTimestamp = DateTimeUtil.class.getMethod("convertEpochToTimestamp", Long.class);
    var dateTimeUtilInstance = new DateTimeUtil();
    return (Timestamp) epochToTimestamp.invoke(dateTimeUtilInstance, epochMillis);
  }

  @SneakyThrows
  private Long anonymizeDateInEpoch(Long epochMillis) {
    var anonymizeDateInEpoch = DateTimeUtil.class.getMethod("anonymizeDateInEpoch", Long.class);
    var dateTimeUtilInstance = new DateTimeUtil();
    return (Long) anonymizeDateInEpoch.invoke(dateTimeUtilInstance, epochMillis);
  }
}
