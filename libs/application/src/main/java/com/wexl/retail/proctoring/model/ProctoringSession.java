package com.wexl.retail.proctoring.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@Entity
@RequiredArgsConstructor
@AllArgsConstructor
@Table(name = "proctoring_sessions")
public class ProctoringSession extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private LocalDateTime startTime;

  private LocalDateTime endTime;

  private String tssUuid;

  private Long examId;

  private Long studentId;

  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private ProctoringStatus status;

  private Long testScheduleId;

  private String testName;

  private String studentName;

  private String message;

  private String videoUrl;

  @Column(columnDefinition = "TEXT")
  private String failureReason;
}
