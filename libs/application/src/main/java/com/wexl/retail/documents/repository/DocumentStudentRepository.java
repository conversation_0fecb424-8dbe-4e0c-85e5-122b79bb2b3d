package com.wexl.retail.documents.repository;

import com.wexl.retail.documents.model.DocumentStudent;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DocumentStudentRepository extends JpaRepository<DocumentStudent, Long> {

  List<DocumentStudent> findAllByStudentIdAndOrgSlug(Long studentId, String orgSlug);

  @Query(
      value =
          """
                          select ds.* from document_students ds
                           inner join documents d on ds.document_id = d.id
                           where ds.student_id = :studentId and ds.org_slug = :orgSlug
                           and (cast((:subjectSlugs) as varChar) is null or d.subject_slug in (:subjectSlugs))
                           and (cast((:docTypeIds) as varChar) is null or d.document_type in (:docTypeIds))
                           and (cast((:chapterSlugs) as varChar) is null or d.chapter_slug in (:chapterSlugs))
                             order by d.created_at desc""",
      nativeQuery = true)
  List<DocumentStudent> getDocumentsByStudent(
      Long studentId,
      String orgSlug,
      List<String> subjectSlugs,
      List<String> chapterSlugs,
      List<Integer> docTypeIds);
}
