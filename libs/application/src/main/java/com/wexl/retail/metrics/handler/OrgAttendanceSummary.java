package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrgAttendanceSummary extends AbstractMetricHandler implements MetricHandler {
  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Integer date = genericMetricRequest.getTimePeriod();
    String status = genericMetricRequest.getInput().get(STATUS).toString();
    String session = genericMetricRequest.getInput().get(SESSION).toString();
    return erpAttendanceService.getOrgAttendanceSummary(org, date, session, status);
  }

  @Override
  public String name() {
    return "org-attendance-summary";
  }
}
