package com.wexl.retail.section.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TeacherCurriculumResponse {

  private String boardSlug;

  private Integer gradeId;

  private String subjectSlug;

  private UUID sectionUuid;
}
