package com.wexl.retail.task.repository;

import com.wexl.retail.elp.dto.ElpCountsByGrade;
import com.wexl.retail.elp.dto.ElpCountsByStudents;
import com.wexl.retail.elp.dto.ElpCountsByTeacher;
import com.wexl.retail.migration.MigrationSubtopicResults;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskType;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {

  Optional<Task> findByIdAndOrgSlug(Long id, String orgSlug);

  List<Task> findAllByTeacherAndDueDateBetween(
      Teacher teacher, LocalDateTime startTime, LocalDateTime endTime);

  List<Task> findAllByOrgSlugAndCreatedAtBetween(
      String orgSlug, Timestamp startTime, Timestamp endTime);

  @Query(
      value =
          """
          select distinct to_char(csi.start_time ,'yyyy-MM-dd') from task_inst ti
          inner join tasks t on  t.id =  ti.task_id
          inner join classroom_schedule_inst csi on csi.id = t.classroom_schedule_inst_id
          where ti.student_id = :studentId
          and ti.completion_status in ('NOT_STARTED','PENDING','COMPLETED','DRAFT')
          and ti.deleted_at is null
          order by to_char(csi.start_time , 'yyyy-MM-dd') desc  limit :limit""",
      nativeQuery = true)
  List<String> getStudentActivityDates(Long studentId, int limit);

  @Query(
      value =
          """
              select distinct t.id from tasks t
                 inner join classroom_schedule_inst csi on t.classroom_schedule_inst_id=csi.id
                 inner join classroom_schedules cs on  csi.classroom_schedule_id=cs.id
                 inner join classroom_teachers ct on cs.classroom_id=ct.classroom_id
                  where ct.classroom_id in :classroomIds""",
      nativeQuery = true)
  List<Long> getTaskIdByClassroom(List<Long> classroomIds);

  @Query(
      value =
          """
                          select t.id as TaskId ,tq.subtopic_slug as SubtopicSlug from tasks t join  test_definitions td on t.test_definition_id = td.id
                          join test_definition_sections tds  on  tds.test_definition_id = td.id
                          join test_questions tq on tq.test_definition_section_id  = tds.id
                          where task_type = 'ASSIGNMENT' and t.subtopic_slug is null
                          """,
      nativeQuery = true)
  List<MigrationSubtopicResults> getMigrationData();

  Long countByTaskTypeAndSubtopicSlugIsNull(TaskType taskType);

  boolean existsByOrgSlugAndSubtopicSlugAndTaskType(
      String orgSlug, String subtopicSlug, TaskType elp);

  List<Task> findAllByGradeSlugAndBoardSlugInAndOrgSlugAndTaskType(
      String gradeSlug, List<String> boardSlug, String orgSlug, TaskType elp);

  List<Task> findAllByGradeSlugAndOrgSlugAndTaskTypeAndDueDateIsNotNull(
      String gradeSlug, String orgSlug, TaskType elp);

  List<Task> findAllByOrgSlugAndTaskTypeAndDueDateNull(String orgSlug, TaskType taskType);

  @Query(
      value =
          """
                  select distinct t.grade_name as gradeName, t.grade_slug as gradeSlug, count(distinct t.chapter_name) as count,
                      count(distinct case when ti.completion_status = 'COMPLETED' then student_id else null end)as studentsAttemted,CASE
                      WHEN t.grade_slug = 'i' THEN 1
                      WHEN t.grade_slug = 'ii' THEN 2
                      WHEN t.grade_slug = 'iii' THEN 3
                      WHEN t.grade_slug = 'iv' THEN 4
                      WHEN t.grade_slug = 'v' THEN 5
                      WHEN t.grade_slug = 'vi' THEN 6
                      WHEN t.grade_slug = 'vii' THEN 7
                      WHEN t.grade_slug = 'viii' THEN 8
                      WHEN t.grade_slug = 'ix' THEN 9
                      WHEN t.grade_slug = 'x' THEN 10
                      ELSE NULL
                      end as grade_order
                      from tasks t
                      join task_inst  ti on t.id = ti.task_id
                      join students s on ti.student_id =s.id
                      join users u on s.user_id =u.id
                      join sections s2 on s.section_id = s2.id
                      where task_type = :type and org_slug  = :orgSlug
                      and to_char(ti.created_at , 'YYYY-MM-DD') >= :createdAt
                      and u.deleted_at is null and s.deleted_at is null and s2.deleted_at is null
                      group by t.grade_slug, t.grade_name
                      order by grade_order
                  """,
      nativeQuery = true)
  List<ElpCountsByGrade> getTasksCountByOrgAndType(String orgSlug, String type, String createdAt);

  @Query(
      value =
          """
                  WITH latest_exams AS (
                      SELECT * FROM (
                          SELECT *,
                                 ROW_NUMBER() OVER (PARTITION BY task_inst_id ORDER BY created_at DESC) as rn
                          FROM exams
                          WHERE is_completed = true
                      ) sub
                      WHERE rn = 1
                  ),
                  assigned_tasks AS (
                      SELECT COUNT(DISTINCT chapter_name) AS total_assigned_count
                      FROM tasks t
                      join sections s on  s.grade_slug = t.grade_slug
                      WHERE task_type = :type
                        AND org_slug = :orgSlug
                         and s."uuid" in (:section)
                  )
                  SELECT
                      se.grade_name as gradeName,
                      se."name" as sectionName,
                      CONCAT(u.first_name, ' ', u.last_name) AS fullName,
                      at.total_assigned_count AS assignedCount,
                      COUNT(distinct CASE WHEN ti.completion_status = 'COMPLETED' THEN t.chapter_name END) AS attendedCount,
                      COUNT(CASE WHEN t.subtopic_name = 'Grammar' AND ti.completion_status = 'COMPLETED' THEN ti.id END) AS grammarCount,
                      COUNT(CASE WHEN t.subtopic_name = 'Listening' AND ti.completion_status = 'COMPLETED' THEN ti.id END) AS listeningCount,
                      COUNT(CASE WHEN t.subtopic_name = 'Reading' AND ti.completion_status = 'COMPLETED' THEN ti.id END) AS readingCount,
                      MAX(CASE WHEN t.subtopic_name = 'Speaking' AND ti.completion_status = 'COMPLETED' THEN ti.id END) AS taskInstId,
                      COUNT(CASE WHEN t.subtopic_name = 'Vocabulary' AND ti.completion_status = 'COMPLETED' THEN ti.student_id END) AS vocabularyCount
                  FROM students s
                  JOIN users u ON u.id = s.user_id
                  JOIN sections se ON se.id = s.section_id
                  LEFT JOIN task_inst ti ON s.id = ti.student_id
                  LEFT JOIN tasks t ON t.id = ti.task_id AND t.task_type = :type AND t.org_slug = :orgSlug
                  LEFT JOIN latest_exams e ON e.task_inst_id = ti.id
                  CROSS JOIN assigned_tasks at
                  WHERE se.uuid IN (:section)
                    AND TO_CHAR(e.created_at , 'yyyy-MM-dd') >= :createdAt
                    AND u.deleted_at IS NULL
                  GROUP BY fullName, t.grade_slug, ti.student_id, se."uuid", se.grade_name, se."name", at.total_assigned_count
                  ORDER BY t.grade_slug;

                      """,
      nativeQuery = true)
  List<ElpCountsByStudents> getElpCountsByStudent(
      String orgSlug, String type, String createdAt, UUID section);

  @Query(
      value =
          """
                  SELECT  CONCAT(u.first_name, ' ', u.last_name) AS fullName,count(distinct t.chapter_name) AS assignedCount,
                  count(distinct (ti.student_id)) as totalStudents,grade_name AS gradeName,
                  count(distinct case when ti.completion_status = 'COMPLETED' then ti.student_id  else null end) AS attendedCount,t.grade_slug
                  FROM  tasks t JOIN task_inst ti ON t.id = ti.task_id
                  JOIN teacher_details td ON td.id = t.assignee
                  JOIN users u ON u.id = td.user_id
                  join orgs o on o.slug = u.organization
                  WHERE t.task_type = :type AND (cast((:grades) as varChar) is null or t.grade_slug in (:grades))
                  AND t.org_slug = :orgSlug AND TO_CHAR(ti.created_at, 'YYYY-MM-DD') >= :createdAt
                  GROUP BY fullName,t.grade_slug,grade_name
                  ORDER BY t.grade_slug;
                                          """,
      nativeQuery = true)
  List<ElpCountsByTeacher> getElpCountsTeacher(
      String orgSlug, String type, String createdAt, List<String> grades);

  List<Task> findAllByOrgSlugInAndTaskType(List<String> orgSlug, TaskType taskType);

  @Query(
      value =
          """
                          SELECT o.slug AS orgSlug,
                                                o.name AS instituteName,
                                                COALESCE(COUNT(DISTINCT ti.task_id) / 5, 0) AS count
                                         FROM orgs o
                                         LEFT JOIN tasks t ON t.org_slug = o.slug AND t.task_type = 'ELP'
                                         LEFT JOIN task_inst ti ON ti.task_id = t.id
                                         WHERE o.slug IN :orgSlugs
                                         GROUP BY o.slug, o.name
                                         order by count Desc
                               """,
      nativeQuery = true)
  List<ElpCounts> getElpCountsByOrg(List<String> orgSlugs);

  List<Task> findAllByOrgSlugAndElpSlug(String orgSlug, String elpSlug);
}
