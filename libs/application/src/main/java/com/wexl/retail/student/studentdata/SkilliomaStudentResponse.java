package com.wexl.retail.student.studentdata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkilliomaStudentResponse {
  private String email;
  private String userName;
  private String firstName;
  private String lastName;
  private String phoneNumber;
  private String gender;
  private String schoolName;
  private String gradeSlug;
  private String boardSlug;

  @JsonProperty("created_date")
  private String createdDate;

  private StudentAttributeData attributeData;
}
