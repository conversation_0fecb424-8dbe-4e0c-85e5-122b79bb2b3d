package com.wexl.retail.term.repository;

import com.wexl.retail.term.model.Term;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TermRepository extends JpaRepository<Term, Long> {
  Optional<Term> findBySlug(String slug);

  List<Term> findBySlugIn(List<String> slugs);
}
