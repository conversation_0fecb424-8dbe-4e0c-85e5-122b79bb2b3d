package com.wexl.retail.student.attributes.repository;

import com.wexl.retail.student.attributes.model.StudentAttributeDefinitionModel;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentAttributeDefinitionRepository
    extends JpaRepository<StudentAttributeDefinitionModel, Long> {

  StudentAttributeDefinitionModel findByOrgSlugAndName(String orgSlug, String key);

  List<StudentAttributeDefinitionModel> findAllByOrgSlug(String orgSlug);
}
