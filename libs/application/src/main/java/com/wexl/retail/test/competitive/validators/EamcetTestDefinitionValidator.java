package com.wexl.retail.test.competitive.validators;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.Comparator;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class EamcetTestDefinitionValidator implements TestDefinitionValidator {

  private final String[] sectionNames = new String[] {"Mathematics", "Physics", "Chemistry"};
  private final Integer[] sectionQuestionCount = new Integer[] {80, 40, 40};

  @Override
  public void validate(TestDefinition testDefinition) {
    if (testDefinition.getTestDefinitionSections().size() != 3) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Sections.EAMCET");
    }

    List<TestDefinitionSection> sortedSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparingLong(TestDefinitionSection::getSequenceNumber))
            .toList();

    for (int i = 0; i < sortedSections.size(); i++) {
      if (!sortedSections.get(i).getName().equals(sectionNames[i])) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionNames.EAMCET");
      }
    }
    for (int i = 0; i < sortedSections.size(); i++) {
      if (sortedSections.get(i).getTestQuestions().size() != sectionQuestionCount[i]) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionQuestionCount.EAMCET");
      }
    }

    validateTestQuestions(testDefinition);
  }

  @Override
  public boolean supports(TestCategory name) {
    return TestCategory.EAMCET.equals(name);
  }

  @Override
  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections) {
    return tssa;
  }

  public void validateTestQuestions(TestDefinition testDefinition) {
    TestDefinitionSection testDefinitionSection = testDefinition.getTestDefinitionSections().get(0);
    testDefinitionSection
        .getTestQuestions()
        .forEach(
            testQuestion -> {
              if (!testQuestion.getType().equals("MCQ")) {
                throw new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Invalid.QuestionType",
                    new String[] {testDefinitionSection.getName(), "MCQ"});
              }
              if (testQuestion.getMarks() != 1) {
                throw new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Invalid.QuestionMarks",
                    new String[] {testDefinitionSection.getName()});
              }
            });
  }
}
