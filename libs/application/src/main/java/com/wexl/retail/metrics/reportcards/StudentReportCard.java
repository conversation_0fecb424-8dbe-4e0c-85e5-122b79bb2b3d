package com.wexl.retail.metrics.reportcards;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentReportCard extends AbstractMetricHandler implements MetricHandler {

  private final ReportCardService reportCardService;

  @Override
  public String name() {
    return "student-report-card";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<String> authUserId =
        Optional.ofNullable(genericMetricRequest.getInput().get(AUTHUSERID))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> examType =
        Optional.ofNullable(genericMetricRequest.getInput().get(EXAM_TYPE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> section =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTIONS))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    String academicYear =
        Optional.ofNullable(genericMetricRequest.getInput().get(YEAR))
            .map(String.class::cast)
            .orElse(null);
    return reportCardService.getStudentReportCard(
        org, authUserId.getFirst(), examType, gradeList, section, academicYear);
  }
}
