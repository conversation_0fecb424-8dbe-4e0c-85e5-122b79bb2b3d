package com.wexl.retail.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import org.hibernate.annotations.Immutable;

/**
 * Marker entity – never loaded or modified by your code. ShedLock itself updates the rows via plain
 * SQL.
 */
@Entity
@Table(name = "shedlock")
@Immutable
public class ShedLockEntity {

  @Id
  @Column(name = "name", nullable = false, length = 64)
  private String name; // job identifier

  @Column(name = "lock_until", nullable = false)
  private Instant lockUntil;

  @Column(name = "locked_at", nullable = false)
  private Instant lockedAt;

  @Column(name = "locked_by", nullable = false, length = 255)
  private String lockedBy;

  /* ---- getters (no setters needed) ---- */

  public String getName() {
    return name;
  }

  public Instant getLockUntil() {
    return lockUntil;
  }

  public Instant getLockedAt() {
    return lockedAt;
  }

  public String getLockedBy() {
    return lockedBy;
  }
}
