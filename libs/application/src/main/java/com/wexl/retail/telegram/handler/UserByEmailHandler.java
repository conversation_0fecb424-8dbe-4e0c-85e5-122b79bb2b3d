package com.wexl.retail.telegram.handler;

import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.telegram.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserByEmailHandler implements TelegramBotHandler {

  @Autowired UserRepository userRepository;

  @Override
  public String handleCommand(String[] params) {
    if (!isValid(params, 2)) {
      return Constants.INVALID_ARGS.formatted(getHelpText());
    }
    var email = params[1];
    final var user = userRepository.findFirstUserByEmail(email);
    var responseMessage = UserService.mapFromUser(user);
    return convertToString(responseMessage);
  }

  @Override
  public String getCommandName() {
    return "/user-email";
  }

  @Override
  public String getHelpText() {
    return "/user-email [parent or teacher email]";
  }
}
