package com.wexl.retail.student.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class StudentDetails {

  @JsonProperty("user_name")
  private String userName;

  @JsonProperty("full_name")
  private String fullName;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("monthly_attendance")
  private Integer monthlyAttendance;

  private List<StudentMlpReport> mlpReports;
}
