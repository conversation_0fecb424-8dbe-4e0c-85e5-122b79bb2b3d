package com.wexl.retail.classroom.mfr;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/students/{authUserId}/mfr")
public class MfrController {
  private final MfrService mfrService;

  @GetMapping()
  public MfrDto.Response getMfr(
      @PathVariable String authUserId,
      @RequestParam Long fromDateInEpoch,
      @RequestParam Long toDateInEpoch) {
    return mfrService.getMfr(authUserId, fromDateInEpoch, toDateInEpoch);
  }
}
