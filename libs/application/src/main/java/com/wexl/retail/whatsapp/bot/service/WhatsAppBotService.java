package com.wexl.retail.whatsapp.bot.service;

import static org.apache.xmlgraphics.util.MimeConstants.MIME_PDF;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.ExamResultDto;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.bot.dto.WhatsAppBotDto;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@RequiredArgsConstructor
@Service
@Slf4j
public class WhatsAppBotService {
  private final UserRepository userRepository;
  private final OrganizationRepository organizationRepository;
  private final WhatsAppService whatsAppService;
  private final StudentAuthService studentAuthService;
  private static final String CORRECT = "/correct";
  private static final String TEXT = "text";
  private static final String COMPLETED = "COMPLETED";
  private final ValidationUtils validationUtils;
  private final RestTemplate restTemplate;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;

  @Value("${urls.correction}")
  private String correctionUrl;

  public void checkUser(WhatsAppBotDto.UserCheckRequest userCheckRequest) {

    WhatsAppBotDto.UserCheckRequest updatedRequest = updatedUserCheckRequest(userCheckRequest);

    for (WhatsAppBotDto.Contacts contact : updatedRequest.contacts()) {
      List<User> users = userRepository.findUsersByMobileNumber(contact.mobile());
      String message;
      if (!users.isEmpty()) {
        Optional<User> matchedUser =
            users.stream()
                .filter(
                    user ->
                        contact
                            .profile()
                            .name()
                            .toLowerCase()
                            .trim()
                            .contains(user.getFirstName().toLowerCase()))
                .findFirst();
        if (matchedUser.isPresent()) {
          if (TEXT.equals(updatedRequest.contentType()) && CORRECT.equals(updatedRequest.text())) {
            message = "Please upload your PDF file.";
          } else {
            message = constructExistUserResponse(matchedUser, contact);
          }
        } else {
          message = constructNotExistedUserResponse(contact);
        }
      } else {
        message = constructNotExistedUserResponse(contact);
      }

      whatsAppService.sendWhatsAppBotMessage(message, contact.mobile());
    }

    log.info("completed");
  }

  private WhatsAppBotDto.UserCheckRequest updatedUserCheckRequest(
      WhatsAppBotDto.UserCheckRequest userCheckRequest) {
    return new WhatsAppBotDto.UserCheckRequest(
        userCheckRequest.customerName(),
        userCheckRequest.sender(),
        userCheckRequest.integratedNumber(),
        userCheckRequest.companyId(),
        userCheckRequest.contentType(),
        userCheckRequest.receivedAt(),
        userCheckRequest.repliedMessageId(),
        userCheckRequest.messageUuid(),
        userCheckRequest.text(),
        userCheckRequest.contacts().stream()
            .map(
                contact ->
                    new WhatsAppBotDto.Contacts(
                        contact.mobile().startsWith("91")
                            ? contact.mobile().substring(2)
                            : contact.mobile(),
                        contact.profile()))
            .collect(Collectors.toList()),
        userCheckRequest.messages(),
        userCheckRequest.templateName(),
        userCheckRequest.templateLanguage(),
        userCheckRequest.newPassword(),
        userCheckRequest.url(),
        userCheckRequest.fileName());
  }

  private String constructNotExistedUserResponse(WhatsAppBotDto.Contacts contact) {
    return "Hi "
        + contact.profile().name()
        + "! It looks like you're not yet registered with WeXL. No worries — let me know how I can assist you today!";
  }

  private String constructExistUserResponse(Optional<User> user, WhatsAppBotDto.Contacts contact) {

    Organization organization = organizationRepository.findBySlug(user.get().getOrganization());
    Teacher teacher = user.get().getTeacherInfo();
    String isTeacherOrStudent = teacher != null ? "is a teacher" : "is a student";

    String response =
        "Hi "
            + contact.profile().name()
            + ", you are part of "
            + organization.getName()
            + " and "
            + isTeacherOrStudent
            + " on WeXL platform."
            + " How may I help you today?  Some ideas - You can create a question paper by simply giving the blueprint";
    return response;
  }

  public void updatePassword(WhatsAppBotDto.UserCheckRequest request) {
    WhatsAppBotDto.UserCheckRequest updatedRequest = updatedUserCheckRequest(request);

    for (WhatsAppBotDto.Contacts contact : updatedRequest.contacts()) {
      List<User> users = userRepository.findUsersByMobileNumber(contact.mobile());

      if (!users.isEmpty()) {
        Optional<User> matchedUser =
            users.stream()
                .filter(
                    user ->
                        contact
                            .profile()
                            .name()
                            .toLowerCase()
                            .trim()
                            .contains(user.getFirstName().toLowerCase()))
                .findFirst();
        if (matchedUser.isPresent()) {
          User user = matchedUser.get();
          studentAuthService.updateStudentPassword(
              user.getOrganization(), user.getAuthUserId(), request.newPassword());
          log.info("Update Successfully");
        } else {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUser");
        }
      } else {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidUser");
      }
    }
  }

  public void subjectiveCorrection(WhatsAppBotDto.UserCheckRequest userCheckRequest) {

    Optional<String> mimeTypeOptional =
        userCheckRequest.messages().stream()
            .map(message -> message.document().mimeType())
            .findFirst();
    var contact = userCheckRequest.contacts().getFirst();
    String mobileNumber =
        contact.mobile().startsWith("91") ? contact.mobile().substring(2) : contact.mobile();

    if (mimeTypeOptional.isPresent() && MIME_PDF.equals(mimeTypeOptional.get())) {

      whatsAppService.sendWhatsAppBotMessage(
          "Please wait it will take some time to processing", mobileNumber);
      ScheduleTest scheduleTest = validationUtils.isTestScheduleValid(4263854L);
      String analysisUrl =
          UriComponentsBuilder.fromUriString(correctionUrl)
              .pathSegment("public", "subjective-correction:pdf")
              .build()
              .toUriString();
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      WhatsAppBotDto.CorrectionRequest requestBody =
          new WhatsAppBotDto.CorrectionRequest(
              scheduleTest.getOrgSlug(), scheduleTest.getId(), userCheckRequest.url());

      HttpEntity<WhatsAppBotDto.CorrectionRequest> entity = new HttpEntity<>(requestBody, headers);

      ResponseEntity<Void> response =
          restTemplate.exchange(analysisUrl, HttpMethod.POST, entity, Void.class);
      if (response.getStatusCode().is2xxSuccessful()) {
        List<User> users = userRepository.findUsersByMobileNumber(mobileNumber);
        Optional<User> matchedUser =
            users.stream()
                .filter(
                    user ->
                        contact
                            .profile()
                            .name()
                            .toLowerCase()
                            .trim()
                            .contains(user.getFirstName().toLowerCase()))
                .findFirst();
        if (matchedUser.isPresent()) {
          User user = matchedUser.get();
          for (int attempt = 0; attempt < 6; attempt++) {
            Optional<ScheduleTestStudent> scheduleTestStudent =
                scheduleTestStudentRepository.findByScheduleTestAndStudent(scheduleTest, user);
            if (scheduleTestStudent.isPresent()
                && COMPLETED.equals(scheduleTestStudent.get().getStatus())) {
              ExamResultDto.Response resultResponse =
                  scheduleTestStudentService.generateStudentsScheduleTestResult(
                      scheduleTest.getId(), true);
              whatsAppService.sendWhatsAppBotMessage(resultResponse.url(), mobileNumber);
              break;
            }
            if (attempt < 5) {
              try {
                Thread.sleep(30000);
              } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
              }
            }
          }
        } else {
          whatsAppService.sendWhatsAppBotMessage("No matching user found", mobileNumber);
        }
      } else {
        whatsAppService.sendWhatsAppBotMessage(
            "Unable to process correction. Please try again later", mobileNumber);
      }
    } else {
      log.info("Invalid MIME type received " + mimeTypeOptional);
      whatsAppService.sendWhatsAppBotMessage(
          "Unable process correction. please upload PDF file", mobileNumber);
    }
  }
}
