package com.wexl.retail.mlp.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MlpItemStatus {
  NOT_STARTED("NOT_STARTED"),
  COMPLETED("COMPLETED");

  private final String value;

  public static MlpItemStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (MlpItemStatus enumEntry : MlpItemStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
