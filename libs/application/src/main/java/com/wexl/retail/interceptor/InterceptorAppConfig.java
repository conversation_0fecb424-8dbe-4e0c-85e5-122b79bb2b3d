package com.wexl.retail.interceptor;

import com.wexl.retail.commons.security.filter.LegacyApiInterceptor;
import com.wexl.retail.monitoring.interceptor.EndpointInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Component
@RequiredArgsConstructor
public class InterceptorAppConfig implements WebMvcConfigurer {

  private final DeviceLoginInterceptor deviceLoginInterceptor;
  private final EndpointInterceptor endpointInterceptor;
  private final LegacyApiInterceptor legacyApiInterceptor;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(deviceLoginInterceptor);
    registry.addInterceptor(endpointInterceptor);
    registry.addInterceptor(legacyApiInterceptor);
  }
}
