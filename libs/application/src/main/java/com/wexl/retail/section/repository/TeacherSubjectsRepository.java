package com.wexl.retail.section.repository;

import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TeacherSubjectsRepository extends JpaRepository<TeacherSubjects, Long> {

  @Query(
      value =
          """
                      select
                                       ts.*
                                   from
                                       teacher_subjects ts
                                           inner join sections s on ts.section_id = s.id
                                           inner join teacher_details t on ts.teacher_id = t.id
                                           inner join users u on t.user_id=u.id
                                   where u.auth_user_id = :authUserId and s.deleted_at is null""",
      nativeQuery = true)
  List<TeacherSubjects> findByTeacher(String authUserId);

  @Transactional
  @Modifying
  @Query(
      value =
          """
          delete from teacher_subjects where board_slug = :boardSlug and teacher_id = :teacherId and section_id = :sectionId
          and subject_slug = :subject
          """,
      nativeQuery = true)
  void deleteByTeacherSectionSubject(
      String boardSlug, Long teacherId, Long sectionId, String subject);

  Optional<TeacherSubjects> findFirstByTeacherAndSectionAndSubjectAndBoard(
      Teacher teacher, Section section, String subjectSlug, String boardSlug);

  @Query(
      value =
          """
                          select * from teacher_subjects where subject_slug in (:subject) and section_id =:sectionId
                          """,
      nativeQuery = true)
  List<TeacherSubjects> getTeachersBySubjectsAndSection(List<String> subject, Long sectionId);

  List<TeacherSubjects> findBySubjectAndSectionId(String subject, Long sectionId);

  List<TeacherSubjects> findAllByTeacher(Teacher teacher);
}
