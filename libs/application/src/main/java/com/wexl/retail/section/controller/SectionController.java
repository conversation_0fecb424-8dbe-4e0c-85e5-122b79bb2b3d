package com.wexl.retail.section.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.ConnectedStudent;
import com.wexl.retail.section.dto.ConnectedTeacher;
import com.wexl.retail.section.dto.request.ConnectedStudentsRequest;
import com.wexl.retail.section.dto.request.SectionCreateRequest;
import com.wexl.retail.section.dto.response.GenericSectionResponse;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.dto.response.SectionResponse;
import com.wexl.retail.section.service.SectionService;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{org}/sections")
public class SectionController {

  @Autowired AuthService authService;

  @Autowired SectionService sectionService;

  @PostMapping
  @IsOrgAdmin
  @Transactional
  public SectionResponse createSection(
      @PathVariable String org, @Valid @RequestBody SectionCreateRequest sectionCreateRequest) {
    try {
      var section = sectionService.createSection(org, sectionCreateRequest);

      sectionService.addTeachersToSection(sectionCreateRequest.getTeachers(), section);

      return sectionService.getSection(org, section.getUuid().toString());
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionAlredayExists", e);
    }
  }

  @PutMapping("/{sectionUuid}")
  @IsOrgAdmin
  public SectionResponse editSection(
      @PathVariable String org,
      @PathVariable("sectionUuid") String secUuid,
      @Valid @RequestBody SectionCreateRequest sectionRequest) {

    return sectionService.editSection(org, secUuid, sectionRequest);
  }

  @GetMapping
  @IsOrgAdminOrTeacher
  public List<SectionEntityDto.Response> getSections(
      @RequestParam(required = false) String grade,
      @RequestParam(required = false) String board,
      @RequestParam(required = false) boolean studentCount,
      @RequestParam(required = false) String teacherId,
      @PathVariable String org) {

    if (Objects.nonNull(grade)) {
      if (Objects.nonNull(teacherId)) {
        return sectionService.getSectionsAndGradeByTeacher(teacherId, grade, board);
      } else if (Objects.nonNull(board)) {
        return sectionService.getSectionsByBoardAndGrade(org, grade, board);
      }
      return sectionService.getSectionsByGrade(org, grade);
    }
    if (Boolean.TRUE.equals(studentCount)) {
      return sectionService.getAllSectionsByOrg(org);
    }

    return sectionService.getAllSections(org, true);
  }

  @GetMapping("/{sectionUuid}")
  @IsOrgAdminOrTeacher
  public SectionResponse getSection(@PathVariable String org, @PathVariable String sectionUuid) {

    return sectionService.getSection(org, sectionUuid);
  }

  @PostMapping("/{sectionUuid}/teacher/{teacherId}")
  @IsOrgAdmin
  public Section addTeacherToSection(
      @PathVariable String sectionUuid, @PathVariable Long teacherId) {

    return sectionService.addTeacherToSectionById(teacherId, sectionUuid);
  }

  @DeleteMapping("/{sectionUuid}/teacher/{teacherId}")
  @IsOrgAdmin
  public void removeTeacherFromSection(
      @PathVariable String sectionUuid, @PathVariable Long teacherId) {

    sectionService.removeTeacherFromSection(teacherId, sectionUuid);
  }

  @DeleteMapping("/{sectionUuid}")
  @IsOrgAdmin
  public void removeSection(@PathVariable String sectionUuid) {

    sectionService.removeSection(authService.getUserDetails().getOrganization(), sectionUuid);
  }

  @GetMapping("/teachers/{userName}/students")
  @IsTeacher
  public Set<ConnectedStudent> getAllConnectedStudents() {

    return sectionService.getAllConnectedStudents(authService.getTeacherDetails());
  }

  @PostMapping("/teachers/{userName}/students")
  @IsTeacher
  public Set<ConnectedStudent> getAllConnectedStudents(
      @RequestBody ConnectedStudentsRequest connectedStudentsRequest) {

    return sectionService.getAllConnectedStudents(connectedStudentsRequest.getSections());
  }

  @GetMapping("/{sectionUuid}/teachers/{userName}/students")
  @IsTeacher
  public Set<ConnectedStudent> getAllConnectedStudents(@PathVariable String sectionUuid) {

    return sectionService.getAllConnectedStudents(sectionUuid);
  }

  @GetMapping("/teachers/{userName}")
  @IsTeacher
  public List<SectionEntityDto.Response> getAllConnectedSectionsToTeacher() {

    return sectionService.getAllConnectedSectionsToTeacher(authService.getTeacherDetails());
  }

  @GetMapping("/{sectionUuid}/teachers")
  public Set<ConnectedTeacher> getAllConnectedTeachers(@PathVariable String sectionUuid) {

    return sectionService.getAllConnectedTeachers(sectionUuid);
  }

  @GetMapping("/students/{userName}")
  @IsStudent
  public ResponseEntity<Set<GenericSectionResponse>> getAllConnectedSectionsToStudent() {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(Duration.ofDays(1)))
        .body(sectionService.getAllConnectedSectionsToStudent(authService.getStudentDetails()));
  }

  @PutMapping("/migration")
  public void migrateSections() {
    sectionService.updateBoardForSections();
  }
}
