package com.wexl.retail.pdf.viewer.repository;

import com.wexl.retail.pdf.viewer.domain.CommentsReviewHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface CommentsReviewHistoryRepository
    extends JpaRepository<CommentsReviewHistory, Long>,
        JpaSpecificationExecutor<CommentsReviewHistory> {

  @Query(value = "select nextval('public.\"comment_review_history_id_seq\"')", nativeQuery = true)
  long getNextValOfReviewHistoryIdSeq();
}
