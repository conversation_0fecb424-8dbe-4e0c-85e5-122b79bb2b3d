package com.wexl.retail.metrics.handler;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.dac.knowledgemeter.service.DacKmService;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.erp.attendance.service.ErpAttendanceService;
import com.wexl.retail.metrics.AllStudentTestReportService;
import com.wexl.retail.metrics.classroom.ClassroomAttendanceService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.WeeklyReportService;
import com.wexl.retail.mlp.service.KMeterService;
import com.wexl.retail.mlp.service.MlpAttendanceService;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.syllabustracking.service.SyllabusTrackingService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.team.service.TeamService;
import com.wexl.retail.test.school.service.TestDefinitionService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Autowired;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractMetricHandler implements MetricHandler {

  public static final String ORG_KEY = "orgs";
  public static final String SUBJECT = "subject";
  public static final String BOARD = "board";
  public static final String GRADE = "grade";
  public static final String SESSION_TYPE = "sessionType";
  public static final String SECTIONS = "sections";
  public static final String AUTHUSERID = "authUserId";
  public static final String TEST_SCHEDULE_ID = "testScheduleId";
  public static final String OFFLINE_TEST_SCHEDULE_STUDENT_ID = "offlineTestScheduleStudentId";
  public static final String OFFLINE_TEST_DEFINITION_ID = "offlineTestDefinitionId";
  public static final String TEST_DEFINITION_ID = "testDefinitionId";
  public static final String EXAM_TYPE = "examType";
  public static final String FROM_DATE = "from_date";
  public static final String TO_DATE = "to_date";
  public static final String TASK_IDS = "task_ids";
  public static final String CLASSROM_ID = "classroom_id";
  public static final String SECTION = "section";
  public static final String SCHEDULE = "schedule";
  public static final String YEAR = "year";
  public static final String STATUS = "status";
  public static final String SESSION = "session";
  public static final String BRANCH = "branch";

  @Autowired protected MlpService mlpService;
  @Autowired protected AuthService authService;
  @Autowired protected TeamService teamService;

  @Autowired protected MlpAttendanceService mlpAttendanceService;
  @Autowired protected TeacherOrgsService teacherOrgsService;

  @Autowired protected ErpAttendanceService erpAttendanceService;
  @Autowired protected ClassroomAttendanceService classroomAttendanceService;
  @Autowired protected SyllabusTrackingService syllabusTrackingService;

  @Autowired protected KMeterService kMeterService;
  @Autowired protected AllStudentTestReportService leaderBoardReportService;
  @Autowired protected WeeklyReportService weeklyReportService;
  @Autowired protected ElpService elpService;
  @Autowired protected TeacherService teacherService;
  @Autowired protected DacKmService dacKmService;
  @Autowired protected NotificationsService notificationsService;
  @Autowired protected TestDefinitionService testDefinitionService;

  protected abstract List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest);

  @Override
  public List<GenericMetricResponse> execute(
      String org, GenericMetricRequest genericMetricRequest) {
    return executeInternal(org, genericMetricRequest);
  }

  protected void validateIfMapped(String parentOrg, String childOrg) {
    if (!getAllChildOrgs().contains(childOrg)) {
      throw new ApiException(
          InternalErrorCodes.UN_AUTHORIZED,
          "Child organization [" + childOrg + "] is not mapped to [" + parentOrg + "]");
    }
  }

  protected List<String> getAllChildOrgs() {
    User user = authService.getUserDetails();
    List<Organization> childOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());

    return childOrgs.stream().map(Organization::getSlug).toList();
  }
}
