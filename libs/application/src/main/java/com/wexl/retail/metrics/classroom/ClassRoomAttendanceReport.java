package com.wexl.retail.metrics.classroom;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ClassRoomAttendanceReport extends AbstractMetricHandler implements MetricHandler {
  @Override
  public String name() {
    return "classroom-attendance-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String attendanceStatus = (String) genericMetricRequest.getInput().get("attendanceStatus");
    String classRoomName = (String) genericMetricRequest.getInput().get("classRoomName");
    Long fromDate = (Long) genericMetricRequest.getInput().get("fromDate");
    Long toDate = (Long) genericMetricRequest.getInput().get("toDate");
    return classroomAttendanceService.getClassRoomReportDetailsBySlug(
        org, fromDate, toDate, attendanceStatus, classRoomName);
  }
}
