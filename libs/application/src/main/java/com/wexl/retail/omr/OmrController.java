package com.wexl.retail.omr;

import static java.time.ZoneOffset.UTC;

import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.util.MlpStudentOmrProcessor;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class OmrController {

  private final OmrService omrService;
  private final MlpStudentOmrProcessor mlpStudentOmrProcessor;

  @PostMapping("/orgs/{orgSlug}/test-schedules:omr-upload")
  @IsTeacher
  public void uploadTestScheduleOmr(
      @PathVariable String orgSlug, @RequestBody OmrDto.TestScheduleRequest request) {
    omrService.uploadTestScheduleOmr(request, orgSlug);
  }

  @PostMapping("/orgs/{orgSlug}/test-schedules-v2:omr-upload")
  @IsTeacher
  public Map<String, String> getMlpUploadPresignedUrl(
      @PathVariable String orgSlug, @Valid @RequestBody OmrDto.OmrRequest omrRequest) {
    String reference =
        DateTimeFormatter.ofPattern("yyyyMMddhhmmssSSS").format(LocalDateTime.now(UTC));
    return Map.of(
        "reference",
        reference,
        "url",
        mlpStudentOmrProcessor.getOmrFileUploadUrl(orgSlug, reference, omrRequest));
  }

  @PostMapping("/orgs/{orgSlug}/test-schedules-v2:omr-submit")
  @IsTeacher
  @ResponseStatus(HttpStatus.CREATED)
  public void omrSubmit(
      @Valid @RequestBody OmrDto.OmrProcessingResponse omrProcessingResponse,
      @PathVariable String orgSlug) {
    omrService.submitOmr(omrProcessingResponse, orgSlug);
  }

  @PostMapping("/public/omr/callback")
  @ResponseStatus(HttpStatus.OK)
  public void processOmr(@RequestBody OmrDto.OmrProcessResult omrProcessResult) {
    omrService.processOmr(omrProcessResult);
  }

  @GetMapping("/orgs/{orgSlug}/omrs")
  @ResponseStatus(HttpStatus.OK)
  public OmrDto.OmrRetailTaskResponse getOmrTaskResponse(
      @PathVariable String orgSlug, @RequestParam("test_schedule_id") Long testScheduleId) {
    return omrService.getOmrTaskResponse(orgSlug, testScheduleId);
  }

  @GetMapping("/orgs/{orgSlug}/omr-templates")
  public OmrDto.OmrTemplateDownloadResponse downloadOmrTemplate(
      @PathVariable String orgSlug, @RequestParam TestCategory category) {
    return omrService.downloadOmrTemplate(orgSlug, category);
  }
}
