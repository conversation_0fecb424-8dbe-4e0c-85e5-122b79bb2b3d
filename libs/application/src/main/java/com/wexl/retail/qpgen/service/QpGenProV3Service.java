package com.wexl.retail.qpgen.service;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.qpgen.dto.QPGenProV2Dto;
import com.wexl.retail.qpgen.model.BluePrintSections;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QpGenProV3Service {

  private final ValidationUtils validationUtils;
  private final BluePrintService bluePrintService;
  private final ContentService contentService;
  private final QpGenProV2Service qpGenProV2Service;

  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummary(
      String orgSlug, QPGenProV2Dto.QuestionSummaryRequest request) {
    validationUtils.isOrgValid(orgSlug);
    var bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList = new ArrayList<>();
    var chaptersList =
        contentService.getChaptersByBoardGradeAndSubject(
            orgSlug, request.boardSlug(), request.gradeSlug(), request.subjectSlug());
    var bluePrintSections = bluePrint.getBluePrintSections();
    bluePrintSections.forEach(
        section ->
            summaryResponseList.add(
                QPGenProV2Dto.QuestionSummaryResponse.builder()
                    .sectionName(section.getSectionName())
                    .sectionsResponses(buildSectionResponse(request, chaptersList))
                    .build()));
    return mergeDuplicateSectionsByName(summaryResponseList, bluePrintSections);
  }

  private List<QPGenProV2Dto.QuestionSummaryResponse> mergeDuplicateSectionsByName(
      List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList,
      List<BluePrintSections> bluePrintSections) {
    List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponsesList = new ArrayList<>();
    var sectionNames =
        bluePrintSections.stream().map(BluePrintSections::getSectionName).distinct().toList();
    sectionNames.forEach(
        name -> {
          var data =
              summaryResponseList.stream().filter(x -> x.sectionName().equals(name)).toList();
          summaryResponsesList.add(
              QPGenProV2Dto.QuestionSummaryResponse.builder()
                  .sectionName(name)
                  .sectionsResponses(modifyResponse(data))
                  .build());
        });

    return summaryResponsesList;
  }

  private List<QPGenProV2Dto.SectionsResponse> modifyResponse(
      List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList) {
    List<QPGenProV2Dto.SectionsResponse> responses = new ArrayList<>();
    summaryResponseList.forEach(
        summaryResponse -> responses.addAll(summaryResponse.sectionsResponses()));
    return responses.stream().distinct().toList();
  }

  private List<QPGenProV2Dto.SectionsResponse> buildSectionResponse(
      QPGenProV2Dto.QuestionSummaryRequest request, List<ChapterResponse> chaptersList) {

    List<QPGenProV2Dto.SectionsResponse> responses = new ArrayList<>();
    List<String> chapterSlugs = request.chapterSlug();

    for (String slug : chapterSlugs) {
      var chapterData =
          chaptersList.stream().filter(x -> x.getChapterSlug().equals(slug)).findFirst();
      chapterData.ifPresent(chapterResponse -> responses.add(buildResponse(chapterResponse)));
    }
    return responses;
  }

  private QPGenProV2Dto.SectionsResponse buildResponse(ChapterResponse chapterResponse) {
    return QPGenProV2Dto.SectionsResponse.builder()
        .chapterName(chapterResponse.getName())
        .chapterSlug(chapterResponse.getChapterSlug())
        .build();
  }

  public List<QPGenProV2Dto.SectionsResponse> getChapterSummary(
      String orgSlug, QPGenProV2Dto.QuestionSummaryRequest request, String chapterSlug) {
    var data = qpGenProV2Service.getQuestionSummary(orgSlug, request);
    var sectionData =
        data.stream()
            .filter(x -> x.sectionName().equals(request.sectionName()))
            .findFirst()
            .orElse(null);

    if (sectionData == null) {
      return Collections.emptyList();
    }

    var chapterData =
        sectionData.sectionsResponses().stream()
            .filter(x -> x.chapterSlug().equals(chapterSlug))
            .toList();

    if (chapterData.isEmpty()) {
      return Collections.emptyList();
    }

    return chapterData;
  }
}
