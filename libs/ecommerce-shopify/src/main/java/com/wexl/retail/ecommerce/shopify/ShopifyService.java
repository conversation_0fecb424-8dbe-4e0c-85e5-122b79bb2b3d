package com.wexl.retail.ecommerce.shopify;

import com.wexl.retail.ecommerce.CommerceService;
import com.wexl.retail.ecommerce.CommerceStore;
import com.wexl.retail.ecommerce.ProductDto;
import com.wexl.retail.ecommerce.ProductDto.Orders;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ShopifyService implements CommerceService {

  public static final String PRODUCT_URL = "https://%s.myshopify.com/admin/api/%s/products.json";
  public static final String ORDERS_URL =
      "https://%s.myshopify.com/admin/api/%s/orders.json?status=any&query=created_at:%s";
  public static final String SHOPIFY_API_VERSION = "2023-01";

  private final RestTemplate restTemplate;

  public ShopifyService(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }

  @Override
  public List<ProductDto.ProductResponse> getProducts(CommerceStore commerceStore) {
    var productData = getProductsByStore(commerceStore);
    if (productData == null || productData.products().isEmpty()) {
      return Collections.emptyList();
    }
    return buildResponse(productData);
  }

  private List<ProductDto.ProductResponse> buildResponse(ShopifyDto.ProductResponse productData) {
    return productData.products().stream()
        .map(
            product ->
                ProductDto.ProductResponse.builder()
                    .id(product.id())
                    .description(product.description())
                    .status(product.status())
                    .thumbNail(product.image() != null ? product.image().src() : null)
                    .extRef(product.id().toString())
                    .title(product.title())
                    .build())
        .toList();
  }

  private ShopifyDto.ProductResponse getProductsByStore(CommerceStore commerceStore) {
    String endPoint = PRODUCT_URL.formatted(commerceStore.getName(), SHOPIFY_API_VERSION);
    return restTemplate
        .exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(commerceStore.getToken()),
            ShopifyDto.ProductResponse.class)
        .getBody();
  }

  private HttpEntity<String> getRequestEntity(String bearerToken) {
    var headers = new HttpHeaders();
    headers.add("X-Shopify-Access-Token", bearerToken);
    return new HttpEntity<>(null, headers);
  }

  @Override
  public List<Orders> processCustomerOrders(CommerceStore commerceStore, Long fromDate) {
    var ordersResponse = fetchOrder(commerceStore, fromDate);
    if (ordersResponse == null || ordersResponse.Orders().isEmpty()) {
      return new ArrayList<>();
    }
    return buildOrders(ordersResponse.Orders());
  }

  private List<Orders> buildOrders(List<ShopifyDto.Orders> orders) {
    return orders.stream()
        .map(
            order ->
                Orders.builder()
                    .id(order.id())
                    .Customer(buildCustomers(order.Customer()))
                    .LineItems(buildLineItems(order.LineItems()))
                    .build())
        .toList();
  }

  private List<ProductDto.LineItems> buildLineItems(List<ShopifyDto.LineItems> lineItems) {
    return lineItems.stream()
        .map(
            m ->
                ProductDto.LineItems.builder()
                    .id(m.id())
                    .productId(m.productId())
                    .title(m.title())
                    .build())
        .toList();
  }

  private ProductDto.Customer buildCustomers(ShopifyDto.Customer customer) {
    final String phone = customer.defaultAddress().phone();

    return ProductDto.Customer.builder()
        .id(customer.id())
        .firstName(customer.firstName())
        .email(customer.email())
        .lastName(customer.lastName())
        .phone(massagePhone(phone))
        .build();
  }

  private String massagePhone(String phone) {
    if (phone == null || phone.trim().length() == 0) {
      return "";
    }
    return phone.replace(" ", "");
  }

  private ShopifyDto.OrdersResponse fetchOrder(CommerceStore commerceStore, Long fromDate) {
    LocalDate date =
        fromDate != null ? convertEpochToIso8601(fromDate).toLocalDate() : LocalDate.now();

    String endPoint =
        ORDERS_URL.formatted(commerceStore.getName(), commerceStore.getProductId(), date);

    return restTemplate
        .exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(commerceStore.getToken()),
            ShopifyDto.OrdersResponse.class)
        .getBody();
  }

  public LocalDateTime convertEpochToIso8601(Long epochMillis) {
    var sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    sdf.setTimeZone(TimeZone.getDefault());
    return LocalDateTime.parse(sdf.format(new Date(epochMillis)));
  }
}
