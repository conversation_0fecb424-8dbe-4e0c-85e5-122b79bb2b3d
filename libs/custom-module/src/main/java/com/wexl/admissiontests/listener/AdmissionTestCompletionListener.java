package com.wexl.admissiontests.listener;

import com.wexl.admissiontests.service.AdmissionTestService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.publisher.AdmissionTestCompletionEvent;
import com.wexl.retail.test.school.domain.TestCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AdmissionTestCompletionListener
    implements ApplicationListener<AdmissionTestCompletionEvent> {
  private final AdmissionTestService admissionTestService;

  @Override
  public void onApplicationEvent(AdmissionTestCompletionEvent admissionTestCompletionEvent) {
    Object source = admissionTestCompletionEvent.getSource();
    if (source instanceof Exam exam
        && TestCategory.BET.equals(exam.getTestDefinition().getCategory())) {
      admissionTestService.sendWhatsAppMessage(exam);
    }
  }
}
