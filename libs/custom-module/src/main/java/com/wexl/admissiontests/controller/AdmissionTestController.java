package com.wexl.admissiontests.controller;

import com.wexl.admissiontests.dto.AdmissionTestDto;
import com.wexl.admissiontests.service.AdmissionTestService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.model.Grade;
import com.wexl.retail.student.auth.StudentProfileImageRequest;
import com.wexl.retail.student.exam.publisher.AdmissionTestCompletionEventPublisher;
import com.wexl.retail.util.ValidationUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class AdmissionTestController {

  private final AdmissionTestService admissionTestService;

  private final AdmissionTestCompletionEventPublisher admissionTestCompletionEventPublisher;

  private final ValidationUtils validationUtils;

  @PostMapping("/students/{studentAuthId}/admission-tests")
  public AdmissionTestDto.Response createTest(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestBody AdmissionTestDto.Request request) {
    return admissionTestService.createTest(orgSlug, request, studentAuthId);
  }

  @GetMapping("/admission-tests/grades")
  public List<Grade> getGradesByTestDefinition(@PathVariable String orgSlug) {
    return admissionTestService.getGradesByTestDefinition(orgSlug);
  }

  @GetMapping("/admission-tests/summary")
  public AdmissionTestDto.AdmissionTestSummary getStartAdmissionTestSummary(
      @PathVariable String orgSlug) {
    return admissionTestService.getStartAdmissionTestSummary(orgSlug);
  }

  @IsOrgAdmin
  @GetMapping("/admission-tests")
  public List<AdmissionTestDto.AdmissionTestsResponse> getAdmissionTests(
      @PathVariable String orgSlug,
      @RequestParam(value = "fromDate", required = false) Long fromDate,
      @RequestParam(value = "toDate", required = false) Long toDate,
      @RequestParam(value = "showTestDetails", defaultValue = "true") boolean showTestDetails,
      @RequestParam(value = "referer", required = false) String referer,
      @RequestParam(value = "branch", required = false) String location,
      @RequestParam(value = "limit", required = false) Integer limit) {
    return admissionTestService.getAdmissionTests(
        orgSlug, fromDate, toDate, showTestDetails, referer, location, limit);
  }

  @PostMapping("/admission-tests/{testId}/profile-images")
  public S3FileUploadResult uploadProfileImage(
      @PathVariable String orgSlug,
      @PathVariable("testId") Long testId,
      @RequestBody StudentProfileImageRequest profileImageRequest) {
    return admissionTestService.uploadProfileImage(
        orgSlug, testId, profileImageRequest.getImageName(), profileImageRequest.getImageType());
  }

  @PostMapping("/exams/{examId}/admission-tests:whatsApp")
  public void sendWhatsAppMessage(@PathVariable("examId") Long examId) {
    admissionTestCompletionEventPublisher.publishAdmissionTestCompletion(
        validationUtils.findByExamId(examId));
  }
}
