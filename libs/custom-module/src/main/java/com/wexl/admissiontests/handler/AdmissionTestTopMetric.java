package com.wexl.admissiontests.handler;

import com.wexl.admissiontests.service.AdmissionTestService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AdmissionTestTopMetric extends AbstractMetricHandler {

  protected final AdmissionTestService admissionTestService;

  @Override
  public String name() {
    return "top-admissions-by-branch-referer";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return admissionTestService.getAdmissionTestByTopMetrics(org);
  }
}
