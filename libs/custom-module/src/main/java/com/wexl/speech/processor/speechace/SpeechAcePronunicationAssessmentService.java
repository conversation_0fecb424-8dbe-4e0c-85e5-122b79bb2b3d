package com.wexl.speech.processor.speechace;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.speech.PronunciationAssessmentService;
import com.wexl.retail.speech.dto.SpeechEvaluation.Phoneme;
import com.wexl.retail.speech.dto.SpeechEvaluation.PronunciationAssessment;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.speech.dto.SpeechEvaluation.Syllable;
import com.wexl.retail.speech.dto.SpeechEvaluation.Word;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse.PhoneScore;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse.Response;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse.SyllableScore;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse.TextScore;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse.WordScore;
import java.io.File;
import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jsoup.Jsoup;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
@Order(1)
@Slf4j
@Service
public class SpeechAcePronunicationAssessmentService implements PronunciationAssessmentService {

  private static final String SPEECH_ACE_KEY =
      "0nCNw%2BbQOFLtkoezyo2rWy5g0uR7QFKlrm5QoV6Ys6AS75YXx%2FJ9N7tD%2BWYvUP3539KmfiKzKcio1rEU2BT35mFr4DJRxSifXa6gUeU7OOLoIBvo9SODzRdq3qqUrXGi";
  private static final String SPEECH_ACE_URL =
      "https://api5.speechace.com/api/scoring/text/v9/json";
  private static final String SPEECH_ACE_SCORING_SPEECH_URL =
      "https://api5.speechace.com/api/scoring/speech/v9/json";
  private final RestTemplate restTemplate;

  @Override
  public SpeechResponse pronunciationAssessment(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    final File file = downloadAudioUrl(audioUrl);

    final String speechEvaluationApiUrl;
    if (isImpromptuSpeech) {
      speechEvaluationApiUrl =
          "%s?key=%s&user_id=XYZ-ABC-9900122222&dialect=en-us"
              .formatted(SPEECH_ACE_SCORING_SPEECH_URL, SPEECH_ACE_KEY);
    } else {
      speechEvaluationApiUrl = "%s?key=%s&dialect=en-us".formatted(SPEECH_ACE_URL, SPEECH_ACE_KEY);
    }
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);

    try {
      var questionText = Jsoup.parse(text).text();
      MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
      parts.add("text", questionText);
      parts.add("relevance_context", questionText);
      parts.add("include_ielts_feedback", 1);
      parts.add("user_audio_file", new FileSystemResource(file));
      HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(parts, headers);

      ResponseEntity<Response> responseEntity =
          restTemplate.postForEntity(speechEvaluationApiUrl, requestEntity, Response.class);

      final Response body = responseEntity.getBody();
      if (Objects.nonNull(body) && Objects.equals("error_no_speech", body.shortMessage())) {
        log.error("Silently ignored  when no speech is detected");
        return null;
      }
      if (responseEntity.getStatusCode().is2xxSuccessful()
          && body != null
          && body.status().equals("success")) {
        return transformToSpeechResponse(body);
      }

      return SpeechResponse.builder()
          .words(Collections.emptyList())
          .assessment(
              PronunciationAssessment.builder()
                  .remarks(
                      Objects.nonNull(body)
                          ? String.format("%s : %s", body.shortMessage(), body.detailMessage())
                          : null)
                  .pronunciationScore(0.0)
                  .completenessScore(0.0)
                  .fluencyScore(0.0)
                  .accuracyScore(0.0)
                  .ieltsScore(0.0)
                  .pteScore(0.0)
                  .toeicScore(0.0)
                  .build())
          .build();
    } catch (ApiException aie) {
      throw aie;
    } catch (Exception ex) {
      log.error("Unable to get response from speechace, Message:" + ex.getMessage(), ex);

      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.speechGenericServerError");
    }
  }

  private SpeechResponse transformToSpeechResponse(Response response) {
    if (response.textScores() == null
        || response.textScores().scores() == null
        || response.textScores().scores().isEmpty()) {
      return (response.speechScore() == null)
          ? null
          : SpeechResponse.builder().speechScore(response.speechScore()).build();
    }
    List<Word> wordList = response.textScores().scores().stream().map(this::constructWord).toList();

    return SpeechResponse.builder()
        .assessment(constructPronunciationAssessment(response.textScores()))
        .words(wordList)
        .build();
  }

  private PronunciationAssessment constructPronunciationAssessment(TextScore textScore) {
    final double pronunciation = textScore.speechAceScore().pronunciation();
    final double pronunciationScoreUpdated = massagePronunciationScore(pronunciation);

    return PronunciationAssessment.builder()
        .pronunciationScore(pronunciation)
        .accuracyScore(pronunciation)
        .completenessScore(pronunciationScoreUpdated)
        .fluencyScore(pronunciationScoreUpdated)
        .ieltsScore((double) textScore.ieltsScore().pronunciation())
        .pteScore((double) textScore.pteScore().pronunciation())
        .toeicScore((double) textScore.toeicScore().pronunciation())
        .cefrScore(textScore.cefrScore().pronunciation())
        .remarks(getRemarks(pronunciation))
        .build();
  }

  private double massagePronunciationScore(double pronunciationScore) {
    if (pronunciationScore <= 60) {
      return 0;
    }
    return pronunciationScore;
  }

  private String getRemarks(double pronunciationScore) {
    if (pronunciationScore <= 60) {
      return "Poor and must be reattempted.";
    }
    if (pronunciationScore >= 60 && pronunciationScore < 70) {
      return "Fair. Possibly not intelligible with several evident mistakes.";
    }

    if (pronunciationScore >= 70 && pronunciationScore < 80) {
      return "Good. Intelligible but with one or two evident mistakes.";
    }

    if (pronunciationScore >= 80 && pronunciationScore < 90) {
      return "Very Good and clearly intelligible.";
    }
    return "Excellent. Native or native-like pronunciation.";
  }

  private Word constructWord(WordScore wordScore) {
    final List<Phoneme> phonemes =
        wordScore.phoneScoreList().stream().map(this::constructPhoneme).toList();

    final List<Syllable> syllables =
        wordScore.syllableScoreList().stream().map(this::constructSyllable).toList();

    return Word.builder()
        .word(wordScore.word())
        .score(wordScore.qualityScore() == null ? 0 : (double) Math.round(wordScore.qualityScore()))
        .phonemes(phonemes)
        .syllables(syllables)
        .extent(constructWordExtent(syllables.stream().flatMap(s -> s.extent().stream()).toList()))
        .build();
  }

  private List<Long> constructWordExtent(List<Long> extents) {
    Long start = extents.getFirst();
    Long end = extents.get(extents.size() - 1);
    return List.of(start, end);
  }

  private Syllable constructSyllable(SyllableScore syllableScore) {
    return Syllable.builder()
        .syllable(syllableScore.letters())
        .score(
            syllableScore.qualityScore() == null
                ? 0
                : (double) Math.round(syllableScore.qualityScore()))
        .extent(syllableScore.extent())
        .build();
  }

  private Phoneme constructPhoneme(PhoneScore phoneScore) {
    return Phoneme.builder()
        .phoneme(phoneScore.phone())
        .score(
            phoneScore.qualityScore() == null ? 0 : (double) Math.round(phoneScore.qualityScore()))
        .extent(phoneScore.extent())
        .build();
  }

  private File downloadAudioUrl(String audioUrl) {
    // The audio url will be of the format
    // https://images.wexledu.com/live-worksheets/elp073276/20231027040648986.wav
    // We need to download the file and save it in the local file system
    // The file name will be the last part of the url
    String[] urlParts = audioUrl.split("/");
    String fileName = urlParts[urlParts.length - 1];
    String filePath = "/tmp/" + fileName;
    try {
      FileUtils.copyURLToFile(URI.create(audioUrl).toURL(), new File(filePath));
    } catch (Exception ex) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unable to download audioUrl", ex);
    }

    return new File(filePath);
  }
}
