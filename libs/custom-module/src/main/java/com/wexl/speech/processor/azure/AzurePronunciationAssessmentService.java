package com.wexl.speech.processor.azure;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.speech.processor.azure.dto.AzureSpeechResponse.Request;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
@Order(5)
@Slf4j
@Service
public class AzurePronunciationAssessmentService {

  private final RestTemplate restTemplate;

  @Value("${app.speechEvaluationUrl:http://speech-service:8080}")
  private String speechEvaluationUrl;

  public SpeechResponse pronunciationAssessment(String text, String audioUrl, String reference) {
    final String speechEvaluationApiUrl =
        "%s/evaluations/%s".formatted(speechEvaluationUrl, reference);
    final Request requestObject = Request.builder().audioUrl(audioUrl).text(text).build();
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    final HttpEntity<Request> requestHttpEntity = new HttpEntity<>(requestObject, headers);
    var responseEntity =
        restTemplate.exchange(
            speechEvaluationApiUrl, HttpMethod.POST, requestHttpEntity, SpeechResponse.class);
    if (responseEntity.getStatusCode().is2xxSuccessful()) {
      return responseEntity.getBody();
    }

    throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.speechGenericServerError");
  }
}
