package com.wexl.speech.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.speech.PronunciationAssessmentService;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.speech.domain.SpeechTask;
import com.wexl.speech.repository.SpeechRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
@Order(1)
public class SpeechServiceImpl implements SpeechService {

  private final SpeechRepository speechRepository;
  private final PronunciationAssessmentService pronunciationAssessmentService;

  @Override
  public SpeechResponse pronunciationAssessment(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    final SpeechResponse speechResponse =
        evaluateSpeech(text, audioUrl, reference, isImpromptuSpeech);
    saveSpeechResponse(reference, speechResponse);
    return speechResponse;
  }

  @Override
  public SpeechResponse pronunciationAssessment(String reference) {
    final Optional<SpeechTask> possibleSpeechTask =
        speechRepository.findAllBySpeechRefOrderByCreatedAtDesc(reference).stream().findFirst();
    return possibleSpeechTask.map(this::constructSpeechResponse).orElse(null);
  }

  @Override
  public void pronunciationAssessment(String reference, Boolean delete) {
    speechRepository.deleteBySpeechRef(reference);
  }

  private SpeechResponse evaluateSpeech(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    return pronunciationAssessmentService.pronunciationAssessment(
        text, audioUrl, reference, isImpromptuSpeech);
  }

  private void saveSpeechResponse(String reference, SpeechResponse speechResponse) {
    if (speechResponse == null) {
      return;
    }

    try {
      ObjectMapper objectMapper = new ObjectMapper();
      String json = objectMapper.writeValueAsString(speechResponse);

      SpeechTask speechTask = SpeechTask.builder().speechRef(reference).response(json).build();

      speechRepository.save(speechTask);

    } catch (Exception ex) {
      log.error("Unable to serialize the speech response for reference {}", reference);
    }
  }

  private SpeechResponse constructSpeechResponse(SpeechTask speechTask) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      String azureSpeechResponse = speechTask.getResponse();
      return objectMapper.readValue(azureSpeechResponse, SpeechResponse.class);
    } catch (Exception ex) {
      log.info("Unable to serialize the speech task for task id {}", speechTask.getId());
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.speechGenericServerError", ex);
    }
  }
}
