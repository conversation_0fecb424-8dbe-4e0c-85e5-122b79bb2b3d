package com.wexl.syllabustracking;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.syllabustracking.model.SyllabusTracking;
import com.wexl.retail.syllabustracking.model.SyllabusTrackingStatus;
import com.wexl.retail.syllabustracking.publisher.SyllabusTrackingEvent;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class SyllabusTrackingEventListener implements ApplicationListener<SyllabusTrackingEvent> {

  private final NotificationsService notificationService;

  private final AuthService authService;

  @Value("${app.mobile.notifications.syllabusTracking:sai681502}")
  private List<String> notificationForOrgs;

  @Override
  public void onApplicationEvent(SyllabusTrackingEvent syllabusTrackingEvent) {
    Object source = syllabusTrackingEvent.getSource();
    if (source instanceof SyllabusTracking syllabusTracking
        && shouldSendMlpNotification(syllabusTracking.getOrgSlug())) {
      sendNotificationIfSyllabusCompleted(syllabusTracking);
    }
  }

  private void sendNotificationIfSyllabusCompleted(SyllabusTracking syllabusTracking) {
    if (SyllabusTrackingStatus.COMPLETED.equals(syllabusTracking.getStatus())) {
      notificationService.createNotificationByTeacher(
          syllabusTracking.getOrgSlug(),
          buildNotificationRequest(syllabusTracking),
          authService.getTeacherDetails().getAuthUserId(),
          false);
    }
  }

  private NotificationDto.NotificationRequest buildNotificationRequest(SyllabusTracking st) {
    return NotificationDto.NotificationRequest.builder()
        .title("Syllabus completed")
        .notificationType(NotificationType.INDIVIDUAL)
        .message(
            String.format(
                "Dear Teacher, %s   %s - %s - %s. Syllabus is completed. Please assign MLP.",
                st.getGradeName(), st.getSubjectName(), st.getChapterName(), st.getSubtopicName()))
        .build();
  }

  private boolean shouldSendMlpNotification(String orgSlug) {
    return notificationForOrgs.contains(orgSlug);
  }
}
