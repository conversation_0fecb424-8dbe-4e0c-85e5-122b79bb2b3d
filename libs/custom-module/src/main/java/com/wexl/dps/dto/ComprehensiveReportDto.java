package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record ComprehensiveReportDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(String schoolName, String address, String academicYear) {}

  @Builder
  public record Body(
      String className,
      String rollNumber,
      String name,
      String gradeSlug,
      String attendance,
      String termSlug,
      List<FirstTable> firstTable,
      AttendanceTable attendanceTable,
      Term1AttendanceTotal term1AttendanceTotal,
      Term2AttendanceTotal term2AttendanceTotal,
      String achievements,
      String comments) {}

  @Builder
  public record FirstTable(String subjects, String color) {}

  @Builder
  public record AttendanceTable(
      Long nwdMarch,
      Long npdMarch,
      Long npMarchAttendancePercentage,
      Long nwdApril,
      Long npdApril,
      Long npAprilAttendancePercentage,
      Long nwdMay,
      Long npdMay,
      Long npMayAttendancePercentage,
      Long nwdJune,
      Long npdJune,
      Long npJuneAttendancePercentage,
      Long nwdJuly,
      Long npdJuly,
      Long npJulyAttendancePercentage,
      Long nwdAug,
      Long npdAug,
      Long npAugAttendancePercentage,
      Long nwdSept,
      Long npdSept,
      Long npSeptAttendancePercentage,
      Long nwdOct,
      Long npdOct,
      Long npOctAttendancePercentage,
      Long nwdNov,
      Long npdNov,
      Long npNovAttendancePercentage,
      Long nwdDec,
      Long npdDec,
      Long npDecAttendancePercentage,
      Long nwdJan,
      Long npdJan,
      Long npJanAttendancePercentage,
      Long nwdFeb,
      Long npdFeb,
      Long npFebAttendancePercentage,
      Double attendancePercentage1,
      Double attendancePercentage2) {}

  @Builder
  public record Term1AttendanceTotal(Long term1TotalWorkingDays, Long term1TotalPresentDays) {}

  @Builder
  public record Term2AttendanceTotal(Long term2TotalWorkingDays, Long term2TotalPresentDays) {}
}
