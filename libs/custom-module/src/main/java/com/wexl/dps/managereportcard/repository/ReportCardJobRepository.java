package com.wexl.dps.managereportcard.repository;

import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobStatus;
import com.wexl.retail.reportcards.model.ReportCardConfig;
import com.wexl.retail.reportcards.model.ReportCardJob;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardJobRepository extends JpaRepository<ReportCardJob, Long> {
  List<ReportCardJob> findAllByOrgSlugAndReportCardConfigAndStatus(
      String orgSlug, ReportCardConfig reportCardConfig, ReportCardJobStatus status);

  List<ReportCardJob> findByOrgSlugOrderByIdDesc(String orgSlug, Pageable pageable);

  @Query(
      value =
          """
          SELECT rcj.* FROM report_card_jobs rcj
          JOIN (
              SELECT report_card_config_id, MAX(id) AS latest_job_id
              FROM report_card_jobs
              GROUP BY report_card_config_id
          ) latest_rcj ON rcj.id = latest_rcj.latest_job_id;
          """,
      nativeQuery = true)
  List<ReportCardJob> getLatestJobsGroupByReportConfig(String orgSlug);

  Optional<ReportCardJob> findTop1ByReportCardConfigOrderByCreatedAtDesc(
      ReportCardConfig reportCardConfig);
}
