package com.wexl.dps.learningmilestones.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import java.util.List;
import lombok.Builder;

public record LmrStudentDto() {

  public record LmrStudentRequest(
      @JsonProperty("category_id") long categoryId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("term_id") Long termId,
      String achievements,
      String comments,
      @JsonProperty("attendance_present") Long attendancePresent,
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("skill_requests") List<StudentSkillRequest> skillRequests) {}

  public record StudentSkillRequest(
      @JsonProperty("category_attribute_id") Long lmrCategoryAttributeId,
      @JsonProperty("skill_value") String skillValue) {}

  @Builder
  public record LmrStudentResponse(
      @JsonProperty("category_id") Long categoryId,
      @JsonProperty("category_name") String categoryName,
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("student_details") List<StudentDetailResponse> studentResponses) {}

  @Builder
  public record LmrSportsSubjectRequest(
      @JsonProperty("subject_metadata_id") Long subjectMetaDataId,
      @JsonProperty("type") SubjectsTypeEnum subjectType,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("show_student") Boolean showStudent) {}

  @Builder
  public record StudentDetailResponse(
      @JsonProperty("student_id") Long studentId,
      String comments,
      String achievements,
      @JsonProperty("attendance_present") Long attendancePresent,
      List<AttributeResponse> attributes) {}

  @Builder
  public record AttributeResponse(
      @JsonProperty("student_detail_id") Long studentDetailId,
      @JsonProperty("attribute_id") Long attributeId,
      @JsonProperty("skill_value") String skillValue) {}

  public record TermMigrationRequest(Long subjectMetadataId, List<Long> termId) {}

  public record AttributeMigrationRequest(Long termId) {}
}
