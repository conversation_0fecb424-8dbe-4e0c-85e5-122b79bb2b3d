package com.wexl.dps.service;

import com.wexl.dps.dto.ErpIntegrationDto;
import com.wexl.dps.dto.ErpIntegrationDto.DpsEntityChange;
import com.wexl.dps.dto.ErpIntegrationDto.DpsStudentResponse;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.model.Gender;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.student.auth.StudentAuthService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class StudentProcessor implements CommandProcessor {

  private final StudentAuthService studentAuthService;
  private final UserRepository userRepository;
  private final SectionRepository sectionRepository;
  private final StudentRepository studentRepository;
  private final RoleTemplateRepository roleTemplateRepository;
  private final StudentAttributeService studentAttributeService;

  public void process(ErpIntegrationDto.DpsEntityChange request) {
    switch (request.changeType()) {
      case "ADD" -> createStudent(request);
      case "DELETE" -> deleteStudent(request);
      case "UPDATE" -> updateStudent(request);
      default -> log.error("change Type not found");
    }
  }

  private void updateStudent(ErpIntegrationDto.DpsEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        createStudent(request);
        return;
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      var student = isStudentExists.get();
      studentAuthService.editStudent(
          student.getUserInfo().getOrganization(),
          student.getUserInfo().getAuthUserId(),
          buildStudentRequest(request));
      saveAttributes(request);
    } catch (Exception e) {
      log.error("Error in updating a student [" + e.getMessage() + "]");
    }
  }

  private void deleteStudent(ErpIntegrationDto.DpsEntityChange request) {
    var externalRef = request.employeeCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return;
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      isStudentExists.ifPresent(
          value ->
              studentAuthService.deleteStudent(
                  value.getUserInfo().getOrganization(), value.getUserInfo().getAuthUserId()));
    } catch (Exception e) {
      log.error("Error in deleting a student [" + e.getMessage() + "]");
    }
  }

  public void createStudent(ErpIntegrationDto.DpsEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    try {
      var user = userRepository.findByExternalRef(externalRef);
      if (user.isEmpty()) {
        var newStudentRequest = buildStudentRequest(request);
        studentAuthService.createStudent(newStudentRequest, request.studentResponse().orgSlug());
        saveAttributes(request);
      } else {
        var userDetails = user.get();
        userDetails.setDeletedAt(null);
        userDetails.setIsDeleted(null);
        userRepository.save(userDetails);
        updateStudent(request);
        saveAttributes(request);
      }
    } catch (Exception e) {
      log.error("Error in creating a student [" + e.getMessage() + "]", e);
    }
  }

  private StudentRequest buildStudentRequest(ErpIntegrationDto.DpsEntityChange request) {
    var studentRequest = request.studentResponse();
    var roleTemplate = roleTemplateRepository.findBySlug("dps student-dps profile");
    var possibleSection =
        sectionRepository.findByUuid(UUID.fromString(studentRequest.sectionUuid()));
    if (possibleSection.isEmpty()) {
      log.error("Section is not present: " + studentRequest.sectionUuid());
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.possibleSection not found");
    }
    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.roleTemplate not found");
    }
    final Section section = possibleSection.get();
    return StudentRequest.builder()
        .section(section.getName())
        .boardSlug(section.getBoardSlug())
        .email(studentRequest.email())
        .rollNumber(getRollNumber(studentRequest))
        .classRollNumber(String.valueOf(studentRequest.rollNumber()))
        .roleTemplate(roleTemplate.getFirst())
        .externalRef(studentRequest.studentCode())
        .lastName("")
        .userName(getUserNamePrefix(studentRequest))
        .firstName(studentRequest.studentName())
        .password("password@123")
        .gender(getGender(request))
        .mobileNumber(studentRequest.phone())
        .academicYearSlug("24-25")
        .gradeSlug(section.getGradeSlug())
        .isFeePaid(Objects.isNull(studentRequest.feeDefaulter()) || !studentRequest.feeDefaulter())
        .build();
  }

  private String getRollNumber(DpsStudentResponse studentRequest) {
    if (!isDpsStudent(studentRequest)) {
      return String.valueOf(studentRequest.rollNumber());
    }
    String studentCode = studentRequest.studentCode();
    if (studentCode != null && studentCode.toUpperCase().startsWith("P")) {
      return studentCode.substring(1);
    }
    return studentCode;
  }

  private String getUserNamePrefix(DpsStudentResponse studentRequest) {
    Map<String, String> userNamePrefixMap = new HashMap<>();
    userNamePrefixMap.put("NH", "dpsnh");
    userNamePrefixMap.put("NG", "dpsng");
    userNamePrefixMap.put("SN", "dpssn");
    userNamePrefixMap.put("MH", "dpsmh");
    userNamePrefixMap.put("AT", "dpsat");
    userNamePrefixMap.put("1", "pmsb");
    userNamePrefixMap.put("2", "pmsa");
    userNamePrefixMap.put("4", "paiss");
    userNamePrefixMap.put("5", "pisg");
    userNamePrefixMap.put("8", "pmst");
    userNamePrefixMap.put("9", "pissg");

    final String usernamePrefix =
        userNamePrefixMap.getOrDefault(studentRequest.branchCode(), "unknown");

    if (usernamePrefix.startsWith("dps")) {
      return usernamePrefix + studentRequest.studentCode().toLowerCase();
    }

    return usernamePrefix + studentRequest.rollNumber();
  }

  public boolean isDpsStudent(DpsStudentResponse studentRequest) {
    Set<String> dpsOrgs =
        Set.of(
            "pal751735",
            "pal556078",
            "pal174599",
            "pal633535",
            "pal988947",
            "pal332908",
            "pal233196",
            "del765517",
            "pal296004",
            "dps688668",
            "del909850",
            "del189476",
            "del217242",
            "aks426844",
            "pal106418",
            "pal454783");
    return dpsOrgs.contains(studentRequest.orgSlug());
  }

  private Gender getGender(DpsEntityChange request) {
    try {
      return Gender.valueOf(request.studentResponse().gender());
    } catch (Exception ex) {
      return Gender.FEMALE;
    }
  }

  private void saveAttributes(ErpIntegrationDto.DpsEntityChange request) {
    var user =
        userRepository.findByExternalRef(request.studentResponse().studentCode()).orElseThrow();
    var fatherName = request.studentResponse().fatherName();
    var motherName = request.studentResponse().motherName();
    var dateOfBirth = request.studentResponse().dob();
    var phone = request.studentResponse().phone();
    Map<String, String> attributes =
        Map.of(
            "father_name",
            fatherName,
            "mother_name",
            motherName,
            "date_of_birth",
            dateOfBirth,
            "phone_number",
            phone);
    var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
    studentAttributeService.saveStudentDefinitionAttributes(
        user.getAuthUserId(), request.studentResponse().orgSlug(), buildAttributes);
  }
}
