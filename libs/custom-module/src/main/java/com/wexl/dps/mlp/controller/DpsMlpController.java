package com.wexl.dps.mlp.controller;

import com.wexl.dps.mlp.dto.MlpDto;
import com.wexl.dps.mlp.service.DpsMlpService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/dps-mlps")
@RequiredArgsConstructor
public class DpsMlpController {

  private final DpsMlpService dpsMlpService;

  @GetMapping()
  public List<MlpDto.Subject> getMlps(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam(required = false) String boardSlug,
      @RequestParam(required = false) String childOrg,
      @RequestParam(required = false) String gradeSlug) {
    return dpsMlpService.getMlps(orgSlug, boardSlug, gradeSlug, childOrg);
  }
}
