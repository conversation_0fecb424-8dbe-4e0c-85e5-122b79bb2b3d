package com.wexl.dps.service;

import static com.wexl.retail.util.Constants.ACCEPTED_ORGS_FOR_TXT_LOCAL;

import com.wexl.dps.config.DpsClassTeacherPreference;
import com.wexl.retail.offlinetest.service.ClassTeacherSubjectHandler;
import com.wexl.retail.offlinetest.service.DefaultClassTeacherSubjectHandler;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.response.TeacherCurriculumResponse;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1)
@Component
@RequiredArgsConstructor
public class DpsClassTeacherSubjectHandler implements ClassTeacherSubjectHandler {

  private final DefaultClassTeacherSubjectHandler defaultClassTeacherSubjectHandler;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final DpsClassTeacherPreference dpsClassTeacherPreference;
  private final TeacherRepository teacherRepository;

  @Override
  public List<SubjectsMetaData> getClassTeacherPreferredSubjectSlugs(Section section) {
    if (!ACCEPTED_ORGS_FOR_TXT_LOCAL.contains(section.getOrganization())) {
      return defaultClassTeacherSubjectHandler.getClassTeacherPreferredSubjectSlugs(section);
    }

    for (DpsClassTeacherPreference.ClassTeacherPreference classTeacherPreference :
        dpsClassTeacherPreference.getClassTeacherPreference()) {
      if (classTeacherPreference.getSectionName().equals(section.getName())
          && classTeacherPreference.getBoardSlug().equals(section.getBoardSlug())) {
        return subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            section.getOrganization(), classTeacherPreference.getSubjectSlugs(),
            section.getGradeSlug(), section.getBoardSlug());
      }
    }

    return defaultClassTeacherSubjectHandler.getClassTeacherPreferredSubjectSlugs(section);
  }

  @Override
  public List<TeacherCurriculumResponse> getClassTeacherSubjects(Section section) {
    if (!ACCEPTED_ORGS_FOR_TXT_LOCAL.contains(section.getOrganization())
        || !List.of("xig", "xiig").contains(section.getGradeSlug())) {
      return defaultClassTeacherSubjectHandler.getClassTeacherSubjects(section);
    }
    return getTeacherCurriculumForSection(section);
  }

  private List<TeacherCurriculumResponse> getTeacherCurriculumForSection(Section section) {
    List<TeacherCurriculumResponse> responses = new ArrayList<>();
    for (DpsClassTeacherPreference.ClassTeacherPreference classTeacherPreference :
        dpsClassTeacherPreference.getClassTeacherPreference()) {
      if (classTeacherPreference.getSectionName().equals(section.getName())
          && classTeacherPreference.getBoardSlug().equals(section.getBoardSlug())) {
        classTeacherPreference
            .getSubjectSlugs()
            .forEach(
                subjectSlug ->
                    responses.add(
                        TeacherCurriculumResponse.builder()
                            .boardSlug(section.getBoardSlug())
                            .gradeId(section.getGradeId())
                            .subjectSlug(subjectSlug)
                            .sectionUuid(section.getUuid())
                            .build()));
      }
    }
    return responses;
  }
}
