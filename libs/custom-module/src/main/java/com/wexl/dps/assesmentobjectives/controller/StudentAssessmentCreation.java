package com.wexl.dps.assesmentobjectives.controller;

import com.wexl.dps.assesmentobjectives.dto.AssessmentObjectiveDto;
import com.wexl.dps.assesmentobjectives.model.AreaRemark;
import com.wexl.dps.assesmentobjectives.service.AssessmentObjectiveService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class StudentAssessmentCreation {
  private final AssessmentObjectiveService assessmentObjectiveService;

  @GetMapping("/ao-details")
  public AssessmentObjectiveDto.AoStudentCreationResponse getAoDetails(
      @PathVariable String orgSlug,
      @RequestParam String boardSlug,
      @RequestParam String gradeSlug,
      @RequestParam String subject,
      @RequestParam Long termId) {
    return assessmentObjectiveService.getAoDetails(orgSlug, boardSlug, gradeSlug, subject, termId);
  }

  @PostMapping("/ao-student-details")
  public void createStudentAo(
      @RequestBody AssessmentObjectiveDto.AssessmentObjectiveStudentRequest request) {
    assessmentObjectiveService.createAoStudent(request);
  }

  @GetMapping("/ao-student-details")
  public AssessmentObjectiveDto.AssessmentObjectiveStudentRequest getStudentAos(
      @RequestParam Long aoId, @RequestParam Long studentId) {
    return assessmentObjectiveService.getStudentAos(aoId, studentId);
  }

  @GetMapping("/ao-remarks")
  public List<AssessmentObjectiveDto.AoRemarkResponse> getAoRemarks(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("board") String board,
      @RequestParam("grade") String grade,
      @RequestParam("subject") String subject,
      @RequestParam("area_remark") AreaRemark areaRemark,
      @RequestParam("term_id") Long termId) {
    return assessmentObjectiveService.getAoRemarks(
        orgSlug, board, grade, subject, areaRemark, termId);
  }
}
