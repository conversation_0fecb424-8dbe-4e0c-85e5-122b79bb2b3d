package com.wexl.dps.service;

import com.wexl.dps.dto.ErpIntegrationDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.exceptions.logger.ParseableLogService;
import com.wexl.retail.commons.exceptions.logger.dto.LogManagement.Request;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.teacher.TeacherRequest;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.teacher.auth.TeacherAuthService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class TeacherProcessor implements CommandProcessor {

  private final TeacherService teacherService;
  private final RoleTemplateRepository roleTemplateRepository;
  private final TeacherAuthService teacherAuthService;
  private final UserRepository userRepository;
  private final TeacherRepository teacherRepository;
  private final ParseableLogService logService;

  public void process(ErpIntegrationDto.DpsEntityChange request) {
    switch (request.changeType()) {
      case "ADD" -> createTeacher(request);
      case "DELETE" -> deleteTeacher(request);
      case "UPDATE" -> updateTeacher(request);
      default -> log.error("change Type not found");
    }
  }

  private void handleAvailableUser(
      User userDetails, String externalRef, ErpIntegrationDto.DpsEntityChange request) {
    userDetails.setDeletedAt(null);
    userDetails.setIsDeleted(null);
    userDetails.setExternalRef(externalRef);
    userRepository.save(userDetails);

    teacherService.editTeacher(
        userDetails.getOrganization(), userDetails.getAuthUserId(), buildTeacherRequest(request));
  }

  private void createTeacher(ErpIntegrationDto.DpsEntityChange request) {
    var externalRef = request.teacherResponse().employeeCode();
    var branchCode = request.teacherResponse().branchCode();
    if (StringUtils.isBlank(externalRef)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.externalRef is empty");
    }
    try {
      var user = userRepository.findByExternalRef(externalRef);
      if (user.isPresent()) {
        handleAvailableUser(user.get(), externalRef, request);
        return;
      }

      var newRequest = buildTeacherRequest(request);
      teacherService.createTeacher(request.teacherResponse().orgSlug(), newRequest);

    } catch (Exception e) {
      log.error("Error in creating a teacher [" + e.getMessage() + "]", e);
      logService.saveLog(
          Request.builder()
              .message("TEACHER CREATION Failed for user with ref " + externalRef)
              .datetime(
                  LocalDateTime.now()
                      .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")))
              .id(UUID.randomUUID().toString())
              .stackTrace(ExceptionUtils.getStackTrace(e))
              .status(0)
              .user(externalRef)
              .email("<EMAIL>")
              .orgSlug(branchCode)
              .build());
    }
  }

  private void deleteTeacher(ErpIntegrationDto.DpsEntityChange teacher) {
    final String externalRef = teacher.employeeCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return;
      }
      var isExistsTeacher = teacherRepository.findByUserInfo(possibleUser.get());
      isExistsTeacher.ifPresent(value -> teacherAuthService.deleteTeacher(value.getUserInfo()));
    } catch (Exception e) {
      log.error("Error in deleting a teacher [" + e.getMessage() + "]");
      logService.saveLog(
          Request.builder()
              .message("TEACHER DELETION Failed for user with ref " + externalRef)
              .datetime(
                  LocalDateTime.now()
                      .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")))
              .id(UUID.randomUUID().toString())
              .stackTrace(ExceptionUtils.getStackTrace(e))
              .status(0)
              .user(externalRef)
              .email("<EMAIL>")
              .orgSlug("")
              .build());
    }
  }

  private void updateTeacher(ErpIntegrationDto.DpsEntityChange request) {
    final String externalRef = request.teacherResponse().employeeCode();
    final String branchCode = request.teacherResponse().branchCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        createTeacher(request);
        return;
      }
      var isExistsTeacher = teacherRepository.findByUserInfo(possibleUser.get());
      var teacher = isExistsTeacher.get();
      teacherService.editTeacher(
          teacher.getUserInfo().getOrganization(),
          teacher.getUserInfo().getAuthUserId(),
          buildTeacherRequest(request));
    } catch (Exception e) {
      log.error("Error in updating a teacher [" + e.getMessage() + "]");
      logService.saveLog(
          Request.builder()
              .message("TEACHER UPDATION Failed for user with ref " + externalRef)
              .datetime(
                  LocalDateTime.now()
                      .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")))
              .id(UUID.randomUUID().toString())
              .stackTrace(ExceptionUtils.getStackTrace(e))
              .status(0)
              .user(externalRef)
              .email("<EMAIL>")
              .orgSlug(branchCode)
              .build());
    }
  }

  private TeacherRequest buildTeacherRequest(ErpIntegrationDto.DpsEntityChange request) {
    var teacherRequest = request.teacherResponse();
    var roleTemplate = roleTemplateRepository.findBySlug("dps teacher-dps profile");
    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.roleTemplate not found");
    }
    return TeacherRequest.builder()
        .firstName(teacherRequest.employeeName())
        .email(teacherRequest.email())
        .mobileNumber(teacherRequest.phone())
        .lastName("")
        .externalRef(teacherRequest.employeeCode())
        .roleTemplate(roleTemplate.getFirst())
        .countryCode("+91")
        .password("password@123")
        .build();
  }
}
