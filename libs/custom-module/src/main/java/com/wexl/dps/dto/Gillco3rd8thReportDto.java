package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record Gillco3rd8thReportDto() {
  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(String schoolName, String address) {}

  @Builder
  public record Body(
      String name,
      String admissionNumber,
      String rollNo,
      String fatherName,
      String motherName,
      String sectionName,
      String height,
      String dateOfBirth,
      String weight,
      List<ScholasticMandatory> scholosticMandatory,
      String scholosticTotalPercentage,
      String scholosticTotalGrade,
      List<ScholasticOptional> scholosticOptional,
      String attendance,
      List<CoScholastic> coScholostic,
      String remarks,
      String issueDate) {}

  @Builder
  public record ScholasticMandatory(
      String subject,
      double pt,
      double ma,
      double portfolio,
      double se,
      double hye,
      double total,
      String grade) {}

  @Builder
  public record ScholasticOptional(
      String subject, double theory, double practical, double total, String grade) {}

  @Builder
  public record CoScholastic(String skillName, String term, List<Skill> skill) {}

  @Builder
  public record Skill(String subject, String grade) {}

  @Builder
  public record Marks(Double marksScored, Double totalMarks) {}
}
