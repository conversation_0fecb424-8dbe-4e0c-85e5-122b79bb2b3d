package com.wexl.dps.managereportcard.dto;

import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import java.util.List;
import lombok.Builder;

public record EyReportDto() {
  @Builder
  public record Body(
      String name,
      String gradeFacilitatorName,
      String grade,
      String centreNo,
      String dateId,
      String orgSlug,
      String cambridgeCurriculum,
      String schoolSectionName,
      String gradeFacilitatorComments,
      String assessment,
      String assessmentName,
      String attendance,
      String termId,
      FirstPageFirstTable firstTable,
      List<SecondPage> secondReportTables,
      AoReport aoReport) {}

  @Builder
  public record FirstPageFirstTable(
      String title,
      String column1,
      String column2,
      String column3,
      String column4,
      List<Marks> marks) {}

  @Builder
  public record Marks(
      String subjectSlug,
      String subject,
      Paper1 paper1,
      Paper2 paper2,
      Paper3 paper3,
      Paper4 paper4,
      String percentage,
      String grade,
      Long seqNo,
      String startDate,
      Long otdId,
      Integer sno) {}

  @Builder
  public record Paper1(String maxMarks, String maxSecured) {}

  @Builder
  public record Paper2(String maxMarks, String maxSecured) {}

  @Builder
  public record Paper3(String maxMarks, String maxSecured) {}

  @Builder
  public record Paper4(String maxMarks, String maxSecured) {}

  @Builder
  public record TableMarks(List<Marks> firstTableMarks) {}

  @Builder
  public record SecondPage(
      String title,
      SecondTables skillsOfFutures,
      SecondTables performanceRecords,
      SecondTables grades) {}

  @Builder
  public record SecondTables(
      String column1,
      String column2,
      String column3,
      String column4,
      String column5,
      String column6,
      String column7) {}

  @Builder
  public record AoReport(ReportCardConfigDto.AoReport aoTables, List<GradeDetails> gradeDetails) {}

  @Builder
  public record GradeDetails(String grade, String name) {}
}
