package com.wexl.dps.preprimary.repository;

import com.wexl.dps.preprimary.model.AcademicPerformanceRecord;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DpsPreprimaryAprRepository extends JpaRepository<AcademicPerformanceRecord, Long> {
  AcademicPerformanceRecord findByStudentIdAndTermId(Long id, Long termId);

  List<AcademicPerformanceRecord> findAllByTermId(Long termId);
}
