package com.wexl.dps.mlp.repository;

import com.wexl.retail.mlp.repository.MlpRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DpsMlpRepository extends MlpRepository {

  @Query(
      value =
          """
                  SELECT grade_slug as gradeSlug,
                   CASE WHEN subject_slug LIKE 'science%' THEN 'science'
                        WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                        WHEN subject_slug LIKE 'english%' THEN 'english'
                        WHEN subject_slug LIKE 'social%' THEN 'social'
                        ELSE subject_slug
                   END as subjectSlug,
                   CASE WHEN subject_slug LIKE 'science%' THEN 'Science'
                        WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                        WHEN subject_slug LIKE 'english%' THEN 'English'
                        WHEN subject_slug LIKE 'social%' THEN 'Social'
                        ELSE subject_name
                   END as subjectName,
                   COUNT(*) AS totalCount,SUM(knowledge_percentage) AS totalKnowledge,
                   SUM(knowledge_percentage) / CAST(COUNT(*) AS float) AS knowledgePercentage
                   FROM mlp WHERE org_slug = :orgSlug AND (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR
                   subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%') and knowledge_percentage is not null
                   GROUP BY grade_slug,subjectSlug,subjectName;

                  """,
      nativeQuery = true)
  List<DpsMlpData> getDpsMlpData(String orgSlug);

  @Query(
      value =
          """
                    SELECT s."uuid" as sectionUuid,
                     CASE WHEN subject_slug LIKE 'science%' THEN 'science'
                          WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                          WHEN subject_slug LIKE 'english%' THEN 'english'
                          WHEN subject_slug LIKE 'social%' THEN 'social'
                          ELSE subject_slug
                     END as subjectSlug,
                     CASE WHEN subject_slug LIKE 'science%' THEN 'Science'
                          WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                          WHEN subject_slug LIKE 'english%' THEN 'English'
                          WHEN subject_slug LIKE 'social%' THEN 'Social'
                          ELSE subject_name
                     END as subjectName,
                     COUNT(*) AS totalCount,SUM(knowledge_percentage) AS totalKnowledge,
                     SUM(knowledge_percentage) / CAST(COUNT(*) AS float) AS knowledgePercentage
                     FROM mlp m join sections s on s.id = m.section_id WHERE org_slug = :orgSlug AND (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR
                     subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%') and knowledge_percentage is not null
                     and s.grade_slug =:gradeSlug GROUP BY sectionUuid,subject_slug,subject_name

                    """,
      nativeQuery = true)
  List<DpsMlpData> getDpsMlpSubjectsData(String orgSlug, String gradeSlug);

  @Query(
      value =
          """
                  SELECT  CASE WHEN subject_slug LIKE 'science%' THEN 'science'
                   WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                   WHEN subject_slug LIKE 'english%' THEN 'english'
                   WHEN subject_slug LIKE 'social%' THEN 'social'
                   ELSE subject_slug END as subjectSlug,CASE WHEN subject_slug LIKE 'science%' THEN 'Science'
                   WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                   WHEN subject_slug LIKE 'english%' THEN 'English'
                   WHEN subject_slug LIKE 'social%' THEN 'Social'
                   ELSE subject_name END as subjectName,
                   o.slug as orgSlug,o.name, COUNT(*) AS totalCount,SUM(knowledge_percentage) AS totalKnowledge,
                   SUM(knowledge_percentage) / CAST(COUNT(*) AS float) AS knowledgePercentage FROM
                   users u join teacher_details td on u.id = td.user_id join teacher_orgs tog on tog.teacher_id = td.id
                   join orgs o on o.id = tog.child_org join mlp m on m.org_slug = o.slug
                   WHERE u.auth_user_id =:authId AND (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                   and knowledge_percentage is not null
                   GROUP BY m.subject_slug,m.subject_name,o.slug,o.name
                   order by m.subject_slug,o.slug,o.name

                          """,
      nativeQuery = true)
  List<DpsMlpData> getDpsManagerMlpData(String authId);

  @Query(
      value =
          """
                          SELECT s.grade_slug AS gradeSlug,o.slug as orgSlug,
                           COUNT(*) AS totalCount,SUM(knowledge_percentage) AS totalKnowledge,
                           SUM(knowledge_percentage) / CAST(COUNT(*) AS FLOAT) AS knowledgePercentage, o.slug,o.name
                           FROM users u JOIN teacher_details td ON u.id = td.user_id
                           JOIN teacher_orgs tog ON tog.teacher_id = td.id
                           JOIN orgs o ON o.id = tog.child_org JOIN  mlp m ON m.org_slug = o.slug
                           JOIN sections s ON s.id = m.section_id
                           WHERE u.auth_user_id =:authId AND knowledge_percentage IS NOT NULL
                           AND (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                           AND s.grade_slug =:gradeSlug GROUP BY s.grade_slug,o.slug, o.name;
                  """,
      nativeQuery = true)
  List<DpsMlpData> getDpsManagerSectionMlpData(String authId, String gradeSlug);
}
