package com.wexl.dps.learningmilestones.controller;

import com.wexl.dps.learningmilestones.dto.LmrDto;
import com.wexl.dps.learningmilestones.dto.LmrStudentDto;
import com.wexl.dps.learningmilestones.model.LmrCategoryType;
import com.wexl.dps.learningmilestones.service.LmrStudentService;
import com.wexl.retail.services.StudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class LmrStudentController {

  private final LmrStudentService lmrStudentService;
  private final StudentService studentService;

  @PostMapping("/lmr-student-details")
  public void createLmrStudentDetail(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody LmrStudentDto.LmrStudentRequest lmrStudentRequest) {
    lmrStudentService.createLmrStudentDetail(orgSlug, lmrStudentRequest);
  }

  @GetMapping("/sections/{sectionUuid}/terms/{termId}/lmr-student-details")
  public LmrStudentDto.LmrStudentResponse getLmrStudentDetail(
      @PathVariable("sectionUuid") String sectionUuid,
      @PathVariable("termId") Long termId,
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("category") Long categoryId,
      @RequestParam("subjectMetaDataId") Long subjectMetaDataId) {
    return lmrStudentService.getLmrStudentDetail(
        orgSlug, sectionUuid, termId, categoryId, subjectMetaDataId);
  }

  @GetMapping("/lmr")
  public LmrDto.LmrResponse getLearningMileStoneAttributes(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam(value = "grade") String gradeSlug,
      LmrCategoryType type,
      Long subjectMetadataId,
      @RequestParam(value = "term_id") Long termId) {
    return lmrStudentService.getLearningMileStoneAttributes(
        gradeSlug, type, subjectMetadataId, orgSlug, termId);
  }

  @PostMapping("/lmr-term-migration")
  public void createLmrCategoryGradeTermMigration(
      @RequestBody LmrStudentDto.TermMigrationRequest request) {
    lmrStudentService.createLmrCategoryGradeTermMigration(request);
  }

  @PostMapping("/student-attribute-migration")
  public void migrateStudentAttribute(
      @PathVariable String orgSlug, @RequestBody LmrStudentDto.AttributeMigrationRequest request) {
    lmrStudentService.studentAttributeMigration(request, orgSlug);
  }

  @PostMapping("/show-student")
  public void saveShowStudentsStatus(@RequestBody LmrStudentDto.LmrSportsSubjectRequest request) {
    lmrStudentService.saveShowStudentsStatus(request);
  }

  @GetMapping("/show-student")
  public LmrDto.ShowStudentsStatus showStudentsStatus(
      @RequestParam(value = "subject_metadata_id") Long subjectMetadataId,
      @RequestParam(value = "term_id") Long termId) {
    return lmrStudentService.showStudentsStatus(subjectMetadataId, termId);
  }
}
