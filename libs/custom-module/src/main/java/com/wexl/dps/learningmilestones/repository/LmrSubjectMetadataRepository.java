package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrSubjectMetadata;
import com.wexl.dps.learningmilestones.service.StudentSportsSubjectMetadataDetails;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LmrSubjectMetadataRepository extends JpaRepository<LmrSubjectMetadata, Long> {
  List<LmrSubjectMetadata> findAllBySubjectMetadataId(Long subjectMetadataId);

  List<LmrSubjectMetadata> findByLmrCategoryGradeId(Long lmrCategoryGradeId);

  @Query(
      value =
          """
                  select lsm.* from lmr_subject_metadata lsm
                  join lmr_category_grades lcg on lsm.lmr_category_grade_id = lcg.id
                  where lsm.subject_metadata_id =:subjectMetaDataId and lcg.term_id =:termId
                  """,
      nativeQuery = true)
  List<LmrSubjectMetadata> findAllBySubjectMetadataIdAndTermId(Long subjectMetaDataId, Long termId);

  @Query(
      value =
          """
          select distinct  sm.id as subjectMetadataId, sm."name"  as subjectMetadataName, sm.type as subjectMetadataType, t.id as termId, t.slug as termSlug, t."name" as termName from lmr_subject_metadata lsm
          join subject_metadata sm  on lsm.subject_metadata_id = sm.id
          join subject_metadata_students sms on sm.id = sms.subject_metadata_id
          join lmr_category_grades lcg on lcg.id = lsm.lmr_category_grade_id
          join terms t on t.id = lcg.term_id
          where sms.student_id = :studentId and lsm.show_student is true
          """,
      nativeQuery = true)
  List<StudentSportsSubjectMetadataDetails> getStudentSportsSubjectMetadata(Long studentId);

  @Query(
      value =
          """
                                     SELECT DISTINCT lsm.show_student
                                     FROM lmr_category_grades lcg
                                     JOIN lmr_subject_metadata lsm
                                     ON lcg.id = lsm.lmr_category_grade_id
                                     WHERE lcg.term_id = :termId
                                     AND lsm.subject_metadata_id = :subjectMetadataId""",
      nativeQuery = true)
  Boolean showStudentsStatus(Long termId, Long subjectMetadataId);
}
