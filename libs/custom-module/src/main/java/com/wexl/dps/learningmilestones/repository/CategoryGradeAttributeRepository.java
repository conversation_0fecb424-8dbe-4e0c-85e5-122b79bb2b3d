package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrCategoryGradeAttribute;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CategoryGradeAttributeRepository
    extends JpaRepository<LmrCategoryGradeAttribute, Long> {

  @Query(
      value =
          """
                    select distinct  lcga.*  from lmr_category_grade_attributes lcga
                        join lmr_category_grades lcg on lcg.id  = lcga .lmr_category_grade_id
                        join lmr_categories lc  on lc.id  = lcg.lmr_category_id
                        where  lc.id = :categoryId and (cast((:gradeSlug) as varChar) is null or lcg.grade_slug in (:gradeSlug)) and lcg.term_id = :termId""",
      nativeQuery = true)
  List<LmrCategoryGradeAttribute> getCategoryAttributes(
      Long categoryId, String gradeSlug, Long termId);

  List<LmrCategoryGradeAttribute> findByLmrCategoryGradeIdIn(List<Long> categoryGradeIds);

  Optional<LmrCategoryGradeAttribute> findByLmrCategoryGradeIdAndAttributeName(
      Long lmrCategoryGradeId, String attributeName);

  @Query(
      value =
          """
                                select distinct  lcga.*  from lmr_category_grade_attributes lcga
                                join lmr_category_grades lcg on lcg.id  = lcga .lmr_category_grade_id
                                join lmr_categories lc  on lc.id  = lcg.lmr_category_id
                                where (:lmrCategoryGradeId is null or lcg.id = :lmrCategoryGradeId) and lc.id = :categoryId and (cast((:gradeSlug) as varChar) is null or lcg.grade_slug in (:gradeSlug)) and lcg.term_id = :termId """,
      nativeQuery = true)
  List<LmrCategoryGradeAttribute> getCategoryAttribute(
      Long categoryId, String gradeSlug, Long termId, Long lmrCategoryGradeId);
}
