package com.wexl.dps.assesmentobjectives.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.term.model.TermAssessment;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "ao_students")
public class AoStudent extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long studentId;
  private String areaOfStrength;
  private String areaOfFocus;
  private String remarks;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "aoStudent", fetch = FetchType.LAZY)
  private List<AoStudentDetail> aoStudentDetails;

  private Long aoId;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "term_assessment_id")
  private TermAssessment termAssessment;
}
