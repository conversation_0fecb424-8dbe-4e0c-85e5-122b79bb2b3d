package com.wexl.dps.reportcard;

import com.wexl.dps.dto.HallticketReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CambridgeAdmitCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request.offlineTestDefinitionId(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("cambridge-admit-card.xml");
  }

  public HallticketReportDto.Body buildBody(
      User user, Long offlineTestDefinitionId, Organization organization) {
    var student = user.getStudentInfo();
    var studentName =
        student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();
    var guardians = student.getGuardians();
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var tableMarks = buildTableMarks(testDefinition, student);
    String formattedStartDate = formatWithOrdinalSuffix(testDefinition.getExamStartDate());
    String formattedEndDate = formatWithOrdinalSuffix(testDefinition.getExamEndDate());
    String startAndEndDate = formattedStartDate + " to " + formattedEndDate;
    if (tableMarks.marks().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.HallTicket",
          new String[] {studentName});
    }
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    return HallticketReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNumber(student.getRollNumber())
        .className(student.getSection().getName())
        .sectionName(student.getSection().getName().substring(0, 4).toUpperCase())
        .rollNumber(student.getClassRollNumber())
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .examinationName(testDefinition.getTitle())
        .orgSlug(organization.getSlug())
        .date(startAndEndDate)
        .firstTable(buildFirstTable(testDefinition, tableMarks.marks(), student))
        .build();
  }

  private String formatWithOrdinalSuffix(LocalDateTime dateTime) {
    int day = dateTime.getDayOfMonth();
    String dayWithSuffix = getDayWithSuffix(day);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM");
    return dayWithSuffix + " " + dateTime.format(formatter);
  }

  private String getDayWithSuffix(int day) {
    if (day >= 11 && day <= 13) {
      return day + "th";
    }
    switch (day % 10) {
      case 1:
        return day + "st";
      case 2:
        return day + "nd";
      case 3:
        return day + "rd";
      default:
        return day + "th";
    }
  }

  public String dateFormat(LocalDateTime dateTime) {
    int day = dateTime.getDayOfMonth();
    String dayWithSuffix = getDayWithSuffix(day);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM yyyy");
    return dayWithSuffix + " OF " + dateTime.format(formatter).toUpperCase();
  }

  private HallticketReportDto.FirstTable buildFirstTable(
      OfflineTestDefinition testDefinition,
      List<HallticketReportDto.Marks> marks,
      Student student) {
    return HallticketReportDto.FirstTable.builder().marks(marks).build();
  }

  private HallticketReportDto.TableMarks buildTableMarks(
      OfflineTestDefinition testDefinition, Student student) {

    var testSchedules =
        testDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(offlineTestSchedule -> offlineTestSchedule.getDeletedAt() == null)
            .toList();

    List<HallticketReportDto.Marks> firstTableMarks = new ArrayList<>();

    for (var testSchedule : testSchedules) {

      var studentDataOptional =
          testSchedule.getOfflineTestScheduleStudents().stream()
              .filter(x -> x.getStudentId().equals(student.getId()))
              .findFirst();

      var studentsSubjectMetaData =
          subjectsMetadataStudentsRepository.findByStudentId(student.getId());

      if (studentDataOptional.isPresent()) {
        var studentData = studentDataOptional.get();

        var subjectsMetaDataOptional =
            getSubjectMetaData(
                studentsSubjectMetaData, testSchedule.getSubjectsMetaData().getName());

        if (subjectsMetaDataOptional.isPresent()) {
          var subjectsMetaData = subjectsMetaDataOptional.get();
          if (subjectsMetaData.getCategoryEnum().equals(SubjectsCategoryEnum.SCHOLASTIC)) {
            var marksBuilder =
                HallticketReportDto.Marks.builder()
                    .subjects(buildSubjects(subjectsMetaData))
                    .build();

            firstTableMarks.add(marksBuilder);
          }
        }
      }
    }

    return HallticketReportDto.TableMarks.builder().marks(firstTableMarks).build();
  }

  private List<HallticketReportDto.Subjects> buildSubjects(SubjectsMetaData subjectsMetaData) {
    HallticketReportDto.Subjects subjects =
        HallticketReportDto.Subjects.builder().subjectNames(subjectsMetaData.getName()).build();

    return List.of(subjects);
  }

  private Optional<SubjectsMetaData> getSubjectMetaData(
      List<SubjectsMetadataStudents> studentsSubjectMetaData, String subjectName) {
    return studentsSubjectMetaData.stream()
        .map(SubjectsMetadataStudents::getSubjectsMetaData)
        .filter(subjectMetaData -> subjectMetaData.getName().equals(subjectName))
        .findFirst();
  }
}
