package com.wexl.dps.mlp.service;

import com.wexl.dps.mlp.dto.MlpDto;
import com.wexl.dps.mlp.repository.DpsMlpData;
import com.wexl.dps.mlp.repository.DpsMlpRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DpsMlpService {

  private final DpsMlpRepository dpsMlpRepository;
  private final CurriculumService curriculumService;
  private final SectionService sectionService;
  private final AuthService authService;
  private final TeacherOrgsService teacherOrgsService;
  private final ValidationUtils validationUtils;
  private final UserRoleHelper userRoleHelper;

  private final String[] subjectSlugs = {"mathematics", "science", "social", "english"};

  public List<MlpDto.Subject> getMlps(
      String orgSlug, String boardSlug, String gradeSlug, String childOrg) {
    User user = authService.getUserDetails();
    if (Boolean.TRUE.equals(userRoleHelper.isManager(user))) {
      if (childOrg != null) {
        return getAdminMlps(childOrg, boardSlug, gradeSlug);
      }
      return getManagerMlps(boardSlug, gradeSlug);
    }

    return getAdminMlps(orgSlug, boardSlug, gradeSlug);
  }

  public List<MlpDto.Subject> getAdminMlps(String orgSlug, String boardSlug, String gradeSlug) {
    if (gradeSlug == null) {
      return buildMlpResponse(boardSlug, orgSlug);
    }

    return getMlpByGrade(orgSlug, gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildMlpResponse(String boardSlug, String orgSlug) {
    List<MlpDto.Subject> response = new ArrayList<>();
    List<DpsMlpData> mlpData = dpsMlpRepository.getDpsMlpData(orgSlug);
    if (mlpData.isEmpty()) {
      return response;
    }

    var grades = getGradesByBoard(orgSlug, boardSlug);
    Map<String, List<DpsMlpData>> groupedBySubject =
        mlpData.stream().collect(Collectors.groupingBy(DpsMlpData::getSubjectSlug));

    Map<String, String> subjectMap =
        Arrays.stream(subjectSlugs).collect(Collectors.toMap(slug -> slug, this::getSubjectName));

    Map<String, String> gradeMap =
        grades.stream().collect(Collectors.toMap(Grade::getSlug, Grade::getName));

    for (String subjectSlug : subjectSlugs) {
      String subjectName = subjectMap.get(subjectSlug);

      List<MlpDto.Grades> gradeList =
          grades.stream()
              .map(
                  grade -> {
                    double knowledgePercentage =
                        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
                            .filter(data -> data.getGradeSlug().equals(grade.getSlug()))
                            .mapToDouble(DpsMlpData::getKnowledgePercentage)
                            .findFirst()
                            .orElse(0.0);

                    return MlpDto.Grades.builder()
                        .gradeName(gradeMap.getOrDefault(grade.getSlug(), "Unknown"))
                        .gradeSlug(grade.getSlug())
                        .orderId(grade.getOrderId())
                        .knowledgePercentage(
                            knowledgePercentage == 0.0
                                ? 0.0
                                : Math.round(knowledgePercentage * 100.0) / 100.0)
                        .build();
                  })
              .sorted(Comparator.comparing(MlpDto.Grades::orderId))
              .toList();

      response.add(
          MlpDto.Subject.builder().name(subjectName).slug(subjectSlug).grades(gradeList).build());
    }

    return response;
  }

  private List<Grade> getGradesByBoard(String orgSlug, String boardSlug) {
    List<EduBoard> boardsHierarchy = curriculumService.getBoardsHierarchy(orgSlug);
    Optional<EduBoard> boardData =
        boardsHierarchy.stream().filter(x -> x.getSlug().equals(boardSlug)).findFirst();
    if (boardData.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.EduboardFind.Organization",
          new String[] {boardSlug});
    }

    List<Grade> grades = boardData.get().getGrades();
    grades.sort(Comparator.comparing(Grade::getSlug));
    return grades;
  }

  private String getSubjectName(String slug) {
    return switch (slug) {
      case "science" -> "Science";
      case "mathematics" -> "Mathematics";
      case "english" -> "English";
      case "social" -> "Social";
      default -> slug;
    };
  }

  public List<MlpDto.Subject> getMlpByGrade(String orgSlug, String gradeSlug, String boardSlug) {
    return buildMlpByGradeResponse(
        dpsMlpRepository.getDpsMlpSubjectsData(orgSlug, gradeSlug), orgSlug, boardSlug, gradeSlug);
  }

  private List<MlpDto.Subject> buildMlpByGradeResponse(
      List<DpsMlpData> mlpData, String orgSlug, String boardSlug, String gradeSlug) {
    List<MlpDto.Subject> response = new ArrayList<>();

    if (mlpData.isEmpty()) {
      return response;
    }
    List<SectionEntityDto.Response> allSections =
        sectionService.getSectionsByGrade(orgSlug, gradeSlug);
    var sections = allSections.stream().filter(x -> x.boardSlug().equals(boardSlug)).toList();
    Map<String, List<DpsMlpData>> groupedBySubject =
        mlpData.stream().collect(Collectors.groupingBy(DpsMlpData::getSubjectSlug));

    Map<String, String> subjectMap =
        Arrays.stream(subjectSlugs).collect(Collectors.toMap(slug -> slug, this::getSubjectName));

    Map<UUID, String> sectionMap =
        sections.stream()
            .filter(x -> x.boardSlug().equals(boardSlug))
            .collect(
                Collectors.toMap(SectionEntityDto.Response::uuid, SectionEntityDto.Response::name));

    for (String subjectSlug : subjectSlugs) {
      String subjectName = subjectMap.get(subjectSlug);

      List<MlpDto.Sections> sectionsList =
          sections.stream()
              .map(
                  section -> {
                    double knowledgePercentage =
                        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
                            .filter(data -> data.getSectionUuid().equals(section.uuid().toString()))
                            .mapToDouble(DpsMlpData::getKnowledgePercentage)
                            .findFirst()
                            .orElse(0.0);

                    return MlpDto.Sections.builder()
                        .sectionName(sectionMap.getOrDefault(section.uuid(), "Unknown"))
                        .sectionUuid(section.uuid().toString())
                        .knowledgePercentage(
                            knowledgePercentage == 0.0
                                ? 0.0
                                : Math.round(knowledgePercentage * 100.0) / 100.0)
                        .build();
                  })
              .toList();

      response.add(
          MlpDto.Subject.builder()
              .name(subjectName)
              .slug(subjectSlug)
              .sections(sectionsList)
              .build());
    }
    return response;
  }

  public List<MlpDto.Subject> getManagerMlps(String boardSlug, String gradeSlug) {
    return (gradeSlug == null)
        ? buildManagerMlpResponse(boardSlug)
        : buildManagerMlpByGradeResponse(gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildManagerMlpByGradeResponse(String gradeSlug, String boardSlug) {
    List<MlpDto.Subject> respone = new ArrayList<>();
    User user = authService.getUserDetails();
    List<String> teacherOrgSlugs = getManagerOrgs(boardSlug);
    List<DpsMlpData> mlpData =
        dpsMlpRepository.getDpsManagerSectionMlpData(user.getAuthUserId(), gradeSlug);
    respone.add(
        MlpDto.Subject.builder()
            .orgResponses(buildOrgResponse(mlpData, teacherOrgSlugs, null))
            .build());
    return (respone);
  }

  private List<MlpDto.Subject> buildManagerMlpResponse(String boardSlug) {
    User user = authService.getUserDetails();
    List<MlpDto.Subject> responeList = new ArrayList<>();
    List<String> teacherOrgSlugs = getManagerOrgs(boardSlug);

    List<DpsMlpData> mlpData = dpsMlpRepository.getDpsManagerMlpData(user.getAuthUserId());
    if (mlpData.isEmpty()) {
      return Collections.emptyList();
    }

    for (String subjectSlug : subjectSlugs) {
      responeList.add(
          MlpDto.Subject.builder()
              .name(getSubjectName(subjectSlug))
              .slug(subjectSlug)
              .orgResponses(buildOrgResponse(mlpData, teacherOrgSlugs, subjectSlug))
              .build());
    }
    return responeList;
  }

  private List<MlpDto.OrgResponse> buildOrgResponse(
      List<DpsMlpData> mlpData, List<String> teacherOrgSlugs, String subjectSlug) {
    List<MlpDto.OrgResponse> orgResponseList = new ArrayList<>();

    teacherOrgSlugs.forEach(
        orgSlug -> {
          var org = validationUtils.isOrgValid(orgSlug);

          Optional<DpsMlpData> orgMlp =
              subjectSlug != null
                  ? mlpData.stream()
                      .filter(
                          x ->
                              x.getSubjectSlug().equals(subjectSlug)
                                  && x.getOrgSlug().equals(orgSlug))
                      .findAny()
                  : mlpData.stream().filter(x -> x.getOrgSlug().equals(orgSlug)).findAny();

          orgResponseList.add(
              MlpDto.OrgResponse.builder()
                  .orgSlug(org.getSlug())
                  .orgName(org.getName())
                  .knowledgePercentage(
                      orgMlp
                          .map(
                              dpsMlpData -> {
                                Double knowledgePercentage = dpsMlpData.getKnowledgePercentage();
                                return knowledgePercentage != null
                                    ? roundToTwoDecimalPlaces(knowledgePercentage)
                                    : 0.0;
                              })
                          .orElse(0.0))
                  .build());
        });

    return orgResponseList;
  }

  private double roundToTwoDecimalPlaces(double value) {
    return Math.round(value * 100.0) / 100.0;
  }

  private List<String> getManagerOrgs(String boardSlug) {
    User user = authService.getUserDetails();
    return teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
        .filter(
            org ->
                org.getCurriculum().getBoards().stream()
                    .anyMatch(board -> board.getSlug().equals(boardSlug)))
        .map(Organization::getSlug)
        .distinct()
        .toList();
  }
}
