package com.wexl.dps;

import com.wexl.dps.dto.ComprehensiveReportDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class DpsService {

  private final String url;
  private final RestTemplate restTemplate;
  private final String token;

  public DpsService(
      @Value("${urls.dpsIntegrationService:http://dps-integration-service:8080}") String url,
      @Value("${app.contentToken}") String token,
      RestTemplate restTemplate) {
    this.token = token;
    this.url = url;
    this.restTemplate = restTemplate;
  }

  public ComprehensiveReportDto.AttendanceTable getDpsStudentAttendance(String studentCode) {
    try {
      String endPoint = String.format("%s/student/%s/dps-student-attendance", url, studentCode);
      HttpHeaders headers = new HttpHeaders();
      headers.setBearerAuth(token);
      HttpEntity<Void> httpEntity = new HttpEntity<>(headers);

      ResponseEntity<ComprehensiveReportDto.AttendanceTable> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              httpEntity,
              new ParameterizedTypeReference<ComprehensiveReportDto.AttendanceTable>() {});

      return responseEntity.getBody();
    } catch (Exception ex) {
      return ComprehensiveReportDto.AttendanceTable.builder().build();
    }
  }
}
