package com.wexl.dps.preprimary.controller;

import com.wexl.dps.preprimary.model.DpsPrePrimaryAprDto;
import com.wexl.dps.preprimary.service.PrePrimaryAprService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/orgs/{orgSlug}")
@RestController
@RequiredArgsConstructor
public class DpsPrePrimaryController {
  private final PrePrimaryAprService prePrimaryAprService;

  @PostMapping("/students/{studentAuthId}/preprimary-apr")
  public void dpsPrePrimary(
      @PathVariable String studentAuthId, @RequestBody DpsPrePrimaryAprDto.SubjectRequest request) {
    prePrimaryAprService.createStudentApr(studentAuthId, request);
  }

  @PostMapping("/students/{studentAuthId}/apr-attendance")
  public void dpsPrePrimaryAttendance(
      @PathVariable String studentAuthId, @RequestBody DpsPrePrimaryAprDto.Attendance request) {
    prePrimaryAprService.createAttendance(studentAuthId, request);
  }

  @PostMapping("/students/{studentAuthId}/apr-remarks")
  public void dpsPrePrimaryRemarks(
      @PathVariable String studentAuthId, @RequestBody DpsPrePrimaryAprDto.Remarks request) {
    prePrimaryAprService.postRemarks(studentAuthId, request);
  }

  @GetMapping("/preprimary-apr/{aprId}")
  public DpsPrePrimaryAprDto.Summary getDpsReportCard(@PathVariable Long aprId) {
    return prePrimaryAprService.getStudentReport(aprId);
  }

  @GetMapping("/sections/{sectionUuid}/terms/{termId}/preprimary-apr")
  public DpsPrePrimaryAprDto.Summary getTermStudents(
      @PathVariable Long termId, @PathVariable String sectionUuid) {
    return prePrimaryAprService.getTermStudents(termId, sectionUuid);
  }

  @PutMapping("/preprimary-apr/{aprId}")
  public void updateAprRecord(
      @PathVariable Long aprId, @RequestBody DpsPrePrimaryAprDto.SubjectRequest request) {
    prePrimaryAprService.updateSkillEntry(aprId, request);
  }

  @PutMapping("/preprimary-apr/{aprId}/remarks")
  public void updateAprRecord(
      @PathVariable Long aprId, @RequestBody DpsPrePrimaryAprDto.Remarks request) {
    prePrimaryAprService.updateRemarks(aprId, request);
  }

  @PutMapping("/preprimary-apr/{aprId}/attendance")
  public void updateAprRecord(
      @PathVariable Long aprId, @RequestBody DpsPrePrimaryAprDto.Attendance request) {
    prePrimaryAprService.updateAttendance(aprId, request);
  }
}
