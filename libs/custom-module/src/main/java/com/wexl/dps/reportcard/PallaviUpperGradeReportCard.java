package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.dto.NinthTenthGradeReportCardDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PallaviUpperGradeReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final StudentAttributeService studentAttributeService;
  private static final String FALSE = "false";

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("pallavi-upper-grade-report-card.xml");
  }

  public NinthTenthGradeReportCardDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();

    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    return NinthTenthGradeReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(NinthTenthGradeReportCardDto.GradeTable.builder().title("Grade Scale").build())
        .attendance(buildAttendance(student.getId()))
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .build();
  }

  private NinthTenthGradeReportCardDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList),
            buildTableMarks(optionalData),
            buildTableMarks(coScholasticData));
    return NinthTenthGradeReportCardDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .build();
  }

  public List<NinthTenthGradeReportCardDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<NinthTenthGradeReportCardDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var port = getMarks(List.of("portfoil"), scholasticData);
          var se = getMarks(List.of("se"), scholasticData);
          var ae = getMarks(List.of("ae"), scholasticData);
          var pt3 = getMarks(List.of("pa3"), scholasticData);
          var pt1 = getMarks(List.of("pa1"), scholasticData);
          var pt2 = getMarks(List.of("pa2"), scholasticData);
          var ma = getMarks(List.of("ma"), scholasticData);
          var ptsMarks = getTopTwoMarks(pt1, pt2, pt3);

          var overAllMarks = 100d;
          double totalMarks =
              parseMark(ptsMarks) + parseMark(se) + parseMark(ma) + parseMark(port) + parseMark(ae);

          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(totalMarks, overAllMarks, offlineTestDefinition.getGradeScaleSlug());

          marksList.add(
              NinthTenthGradeReportCardDto.Marks.builder()
                  .ptsMarks(ptsMarks)
                  .ma(ma)
                  .se(se)
                  .port(port)
                  .annualMarks(ae)
                  .subject(subject)
                  .grade(grade)
                  .totalMarksScored(String.valueOf(totalMarks))
                  .total(String.valueOf(overAllMarks))
                  .seqNo(scholasticData.getFirst().getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  private String getTopTwoMarks(String pt1, String pt2, String pt3) {
    List<Double> marks = Arrays.asList(parseMark(pt1), parseMark(pt2), parseMark(pt3));

    double average =
        marks.stream()
            .sorted(Comparator.reverseOrder())
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private double parseMark(String mark) {
    if (mark == null || mark.isEmpty()) {
      return 0.0;
    }
    try {
      return Double.parseDouble(mark);
    } catch (NumberFormatException e) {
      return 0.0;
    }
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;
    if (assessmentSlug.getFirst().equals("pa2")) {
      data = subjectData.stream().filter(d -> "PT2(B) ".contains(d.getTestName())).toList();
    } else if (assessmentSlug.getFirst().equals("pa1")) {
      data =
          subjectData.stream()
              .filter(d -> "PERIODIC TEST -1(B) ".contains(d.getTestName()))
              .toList();
    } else {
      data =
          subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();
    }

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private NinthTenthGradeReportCardDto.FirstTable buildFirstTable(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks,
      List<NinthTenthGradeReportCardDto.Marks> externalMarks,
      Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    NinthTenthGradeReportCardDto.FirstTable s =
        NinthTenthGradeReportCardDto.FirstTable.builder()
            .title("PART-I: SCHOLASTIC AREAS [ON a 8-POINT(A1 to E)GRADING SCALE]")
            .marks(firstTableMarks)
            .external(externalMarks)
            .totals(buildTotals(firstTableMarks))
            .build();
    return s;
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private NinthTenthGradeReportCardDto.Totals buildTotals(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks) {
    double totalMarksScored =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::totalMarksScored)
            .filter(Objects::nonNull)
            .mapToDouble(Double::parseDouble)
            .sum();

    double totalMarks =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::total)
            .filter(Objects::nonNull)
            .mapToDouble(Double::parseDouble)
            .sum();

    double percentage =
        firstTableMarks.stream()
            .map(NinthTenthGradeReportCardDto.Marks::totalMarksScored)
            .filter(Objects::nonNull)
            .mapToDouble(Double::parseDouble)
            .average()
            .orElse(0.0);
    percentage = Double.parseDouble(String.format("%.2f", percentage));

    String totalMarksScoredFormat = String.format("%.2f", totalMarksScored);

    String grade =
        totalMarks == 0.0
            ? "N/A"
            : pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(percentage));

    String overallPercentageGrade = "N/A".equals(grade) ? "0.0" : grade;
    return NinthTenthGradeReportCardDto.Totals.builder()
        .marksTotal(totalMarks)
        .percentage(Double.valueOf(totalMarksScoredFormat))
        .overallPercentage(percentage)
        .grade(overallPercentageGrade)
        .build();
  }

  private NinthTenthGradeReportCardDto.SecondTable buildSecondTable(
      List<NinthTenthGradeReportCardDto.SecondTableMarks> marks) {
    return NinthTenthGradeReportCardDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 5-point(A to E)grading scale]")
        .marks(marks)
        .build();
  }

  public NinthTenthGradeReportCardDto.Attendance buildAttendance(Long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("ae");
    if (termAssessment.isEmpty()) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return NinthTenthGradeReportCardDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return NinthTenthGradeReportCardDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }

  private NinthTenthGradeReportCardDto.TableMarks sortTable(
      List<NinthTenthGradeReportCardDto.Marks> firstTableMarks,
      List<NinthTenthGradeReportCardDto.Marks> externalMarks,
      List<NinthTenthGradeReportCardDto.Marks> secondTableMarks) {
    List<NinthTenthGradeReportCardDto.Marks> firstTable = new ArrayList<>();
    List<NinthTenthGradeReportCardDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<NinthTenthGradeReportCardDto.Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();

    for (int i = 0; i < sortedFirstTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          NinthTenthGradeReportCardDto.Marks.builder()
              .sno(i + 1L)
              .grade(mark.grade())
              .port(mark.port())
              .se(mark.se())
              .ma(mark.ma())
              .ptsMarks(mark.ptsMarks())
              .annualMarks(mark.annualMarks())
              .totalMarksScored(mark.totalMarksScored())
              .total(mark.total())
              .subject(mark.subject())
              .otdId(mark.otdId())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          NinthTenthGradeReportCardDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .grade(mark.grade())
              .annualMarks(mark.annualMarks())
              .totalMarksScored(mark.totalMarksScored())
              .total(mark.total())
              .subject(mark.subject())
              .otdId(mark.otdId())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(NinthTenthGradeReportCardDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      NinthTenthGradeReportCardDto.Marks mark = sortedSecondTable.get(i);
      var hye = mark.total();
      String grade = null;

      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = offlineTestScheduleService.getGrade(BigDecimal.valueOf(Double.parseDouble(hye)));
      }

      secondTable.add(
          NinthTenthGradeReportCardDto.SecondTableMarks.builder()
              .sno(i + 1L)
              .termGrade(Objects.isNull(grade) ? hye : grade)
              .subjectName(mark.subject())
              .build());
    }
    return NinthTenthGradeReportCardDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .build();
  }
}
