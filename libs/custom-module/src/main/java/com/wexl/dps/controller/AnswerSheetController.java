package com.wexl.dps.controller;

import com.wexl.dps.reportcard.GenericAnswerSheet;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.v2.dto.GenericAsnwerSheetDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class AnswerSheetController {

  private final GenericAnswerSheet genericAnswerSheet;

  @IsOrgAdminOrTeacher
  @PostMapping(
      value = "/test-schedules/{testScheduleId}/generic-sc-answer-sheet-templates",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getGenericAnswerSheetTemplates(
      @PathVariable String orgSlug,
      @PathVariable("testScheduleId") Long testScheduleId,
      @RequestBody GenericAsnwerSheetDto.Request request) {
    return genericAnswerSheet.getAnswerSheet(orgSlug, testScheduleId, request);
  }
}
