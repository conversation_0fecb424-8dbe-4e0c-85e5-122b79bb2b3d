package com.wexl.dps.learningmilestones.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "lmr_category_attribute_definitions")
public class LmrCategoryAttributeDefinition extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  String definition;
  String standard;
  Long lmrCategoryAttributeId;
  Long seqNo;
}
