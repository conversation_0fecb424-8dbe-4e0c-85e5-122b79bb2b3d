package com.wexl.dps.assesmentobjectives.repository;

import com.wexl.dps.assesmentobjectives.model.AoStudent;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AoStudentRepository extends JpaRepository<AoStudent, Long> {
  Optional<AoStudent> findByAoIdAndStudentId(Long aoId, Long studentId);

  List<AoStudent> findByStudentId(Long studentId);

  @Query(
      value =
          """
                                         select aos.* from ao_students aos
                                         join term_assessments ta on ta.id=aos.term_assessment_id
                                         where aos.student_id=:studentId and ta.term_id=:termId
                                         """,
      nativeQuery = true)
  List<AoStudent> getDataByStudentAndTerm(Long studentId, Long termId);
}
