package com.wexl.dps.learningmilestones.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "lmr_student_detail_achievements")
public class LmrStudentDetailAchievement extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long studentId;

  @Column(columnDefinition = "TEXT")
  private String achievements;

  @Column(columnDefinition = "TEXT")
  private String comments;

  private Long attendancePresent;
}
