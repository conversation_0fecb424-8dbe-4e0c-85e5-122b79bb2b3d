package com.wexl.dps.learningmilestones.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record LmrDto() {

  @Builder
  public record LmrResponse(
      @JsonProperty("category_responses") List<LmrCategoryResponse> categoryResponses) {}

  @Builder
  public record LmrCategoryResponse(
      Long id,
      @JsonProperty("category_name") String categoryName,
      List<String> grades,
      List<LmrAttributes> attributes) {}

  @Builder
  public record LmrAttributes(
      @JsonProperty("attribute_id") Long attributeId,
      @JsonProperty("attribute_name") String attributeName,
      String uuid) {}

  @Builder
  public record ShowStudentsStatus(
      @JsonProperty("show_students_status") Boolean showStudentsStatus) {}
}
