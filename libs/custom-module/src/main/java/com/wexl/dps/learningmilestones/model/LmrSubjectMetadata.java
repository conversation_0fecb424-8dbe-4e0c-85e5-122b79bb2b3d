package com.wexl.dps.learningmilestones.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "lmr_subject_metadata")
public class LmrSubjectMetadata extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long subjectMetadataId;
  private Long lmrCategoryGradeId;
  private Boolean showStudent;
}
