package com.wexl.dps.preprimary.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.dps.dto.LearningLevel;
import lombok.Builder;

@Builder
public class DpsPrimaryReportCardDto {
  @JsonProperty("apr_id")
  private Long aprId;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("grade_facilitator")
  private String teacherName;

  @JsonProperty("teacher_id")
  private Long teacherId;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("math")
  private LearningLevel mathematics;

  @JsonProperty("cll_listening")
  private LearningLevel listening;

  @JsonProperty("cll_reading")
  private LearningLevel reading;

  @JsonProperty("cll_speaking")
  private LearningLevel speaking;

  @JsonProperty("cll_writing")
  private LearningLevel writing;

  @JsonProperty("understanding_of_the_world")
  private LearningLevel untw;

  @JsonProperty("personal_social_phys_dev")
  private LearningLevel psew;

  @JsonProperty("phy_dev")
  private LearningLevel phyDev;

  @JsonProperty("assessment_id")
  private Long assessmentId;

  @JsonProperty("assessment_name")
  private String assessmentName;

  @JsonProperty("candidate_name")
  private String studentName;

  @JsonProperty("total_attendance")
  private Long totalAttendance;

  @JsonProperty("present_attendance")
  private Long attendancePresent;

  @JsonProperty("comments")
  private String remarks;

  @JsonProperty("term_id")
  private Long termId;
}
