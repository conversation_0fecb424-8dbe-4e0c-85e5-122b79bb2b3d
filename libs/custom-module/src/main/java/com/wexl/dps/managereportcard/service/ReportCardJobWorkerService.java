package com.wexl.dps.managereportcard.service;

import static com.wexl.retail.reportcards.model.StudentReportCardStatus.*;
import static com.wexl.retail.subjects.model.SubjectsCategoryEnum.CO_SCHOLASTIC;

import com.wexl.dps.managereportcard.model.*;
import com.wexl.dps.managereportcard.repository.*;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.reportcards.model.ReportCardJob;
import com.wexl.retail.reportcards.model.StudentReportCard;
import com.wexl.retail.reportcards.repository.StudentReportCardRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.util.StrapiService;
import jakarta.transaction.Transactional;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ReportCardJobWorkerService {
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final CustomOfflineTestDefinitionRepository customOfflineTestScheduleRepository;
  private final ReportCardConfigDetailRepository reportCardConfigDetailRepository;
  private final ReportCardJobRepository jobRepository;
  private final StudentRepository studentRepository;
  private final StrapiService strapiService;
  private final ContentService contentService;
  private final StudentReportCardRepository studentReportCardRepository;
  private final ManageReportCardService manageReportCardService;
  private final OfflineTestReportService offlineTestReportService;
  private final StorageService storageService;
  private final TermAssessmentRepository termAssessmentRepository;

  @Async
  @Transactional
  public void triggerReportCardJob(Long reportCardJobId) {
    var possibleCurrentJob = jobRepository.findById(reportCardJobId);
    if (possibleCurrentJob.isEmpty()) {
      return;
    }
    var currentJob = possibleCurrentJob.get();
    var reportCardConfig = currentJob.getReportCardConfig();
    List<ReportCardConfigData> reportCardData = new ArrayList<>();
    try {
      var reportCardConfigDetails =
          reportCardConfigDetailRepository.findAllByReportCard(currentJob.getReportCardConfig());
      var studentReportCards = assembleStudentReportCard(currentJob);

      Map<Long, StudentReportCard> studentReportMap =
          studentReportCards.stream()
              .collect(Collectors.toMap(StudentReportCard::getStudentId, Function.identity()));

      for (ReportCardConfigDetail reportCardConfigDetail : reportCardConfigDetails) {
        Long assessmentId = reportCardConfigDetail.getTermAssessment().getId();

        var categories =
            Objects.nonNull(reportCardConfigDetail.getAssessmentEvaluationConfig())
                    && !reportCardConfigDetail
                        .getAssessmentEvaluationConfig()
                        .getAssessmentCategories()
                        .isEmpty()
                ? reportCardConfigDetail.getAssessmentEvaluationConfig().getAssessmentCategories()
                : null;

        List<OfflineTestScheduleStudentDetails> offlineTestScheduleStudentDetails =
            customOfflineTestScheduleRepository.getOfflineTestDefinitionsByAssessment(
                currentJob.getOrgSlug(),
                reportCardConfig.getBoardSlug(),
                reportCardConfig.getGradeSlug(),
                assessmentId,
                categories);

        var testScheduleStudentDetailMap =
            offlineTestScheduleStudentDetails.stream()
                .collect(Collectors.groupingBy(OfflineTestScheduleStudentDetails::getStudentId));

        testScheduleStudentDetailMap.forEach(
            (student, details) -> {
              var studentReportCard = studentReportMap.get(student);
              if (Objects.isNull(studentReportCard)) {
                return;
              }
              try {
                var reportCardConfigData =
                    details.stream()
                        .map(
                            detail ->
                                buildReportCardConfigData(
                                    detail, reportCardConfigDetail, currentJob))
                        .toList();
                reportCardData.addAll(reportCardConfigData);
                studentReportCard.setFailureReason(
                    concatFailureReason(
                        reportCardConfigData, studentReportCard.getFailureReason()));
                if (!StringUtils.isEmpty(studentReportCard.getFailureReason()))
                  studentReportCard.setStatus(FAILED);
                else studentReportCard.setStatus(SUCCESS);
              } catch (Exception e) {
                studentReportCard.setStatus(FAILED);
                studentReportCard.setStackTrace(getStackTrace(e));
                log.error("Error while generating report card:  {}", e.getMessage(), e);
              } finally {
                studentReportCardRepository.save(studentReportCard);
              }
            });
      }
      reportCardConfigDataRepository.saveAll(reportCardData);
      log.info("Report Card Generated Successfully");
      manageReportCardService.updateReportCardJob(
          currentJob.getId(), ReportCardConfigDto.ReportCardJobStatus.COMPLETED, null);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      manageReportCardService.updateReportCardJob(
          reportCardJobId, ReportCardConfigDto.ReportCardJobStatus.FAILED, getStackTrace(e));
    }
  }

  private List<StudentReportCard> assembleStudentReportCard(ReportCardJob currentJob) {
    String boardSlug = currentJob.getReportCardConfig().getBoardSlug();
    String gradeSlug = currentJob.getReportCardConfig().getGradeSlug();
    var board = strapiService.getEduBoardBySlug(boardSlug);
    var grade = contentService.getGradeBySlug(gradeSlug);
    var students =
        studentRepository
            .getStudentsByOrgSlugAndBoardAndGrade(
                currentJob.getOrgSlug(),
                (long) board.getId(),
                Collections.singletonList((long) grade.getId()))
            .stream()
            .filter(s -> Objects.isNull(s.getDeletedAt()))
            .toList();
    var previousJob =
        jobRepository.findTop1ByReportCardConfigOrderByCreatedAtDesc(
            currentJob.getReportCardConfig());
    if (previousJob.isEmpty()) {
      return students.stream()
          .map(student -> buildStudentReportCard(student, currentJob, new StudentReportCard()))
          .toList();
    }
    var studentReportCards = studentReportCardRepository.findByReportCardJob(previousJob.get());
    var studentReportCardMap =
        studentReportCards.stream()
            .collect(Collectors.toMap(StudentReportCard::getStudentId, Function.identity()));

    return students.stream()
        .map(
            student -> {
              var studentReportCard = studentReportCardMap.get(student.getId());
              if (Objects.nonNull(studentReportCard)) {
                return buildStudentReportCard(student, currentJob, studentReportCard);
              }
              return buildStudentReportCard(student, currentJob, new StudentReportCard());
            })
        .toList();
  }

  public String getStackTrace(Exception exception) {
    try {
      var stackTrace = ExceptionUtils.getStackTrace(exception);
      if (stackTrace.length() > 5000) {
        return stackTrace.substring(0, 5000);
      }
      return stackTrace;
    } catch (Exception ex) {
      log.error("Failed to save stack trace {}", ex.getMessage(), ex);
      return String.format("Failed to get stack trace [ %s ]", ex.getMessage());
    }
  }

  @Async
  @Transactional
  public void generateStudentReportCard(long reportCardJobId) {
    var currentJob = validateReportCardJob(reportCardJobId);
    var studentReportCards =
        studentReportCardRepository.findByReportCardJob(currentJob).stream()
            .filter(src -> SUCCESS.equals(src.getStatus()))
            .toList();
    if (studentReportCards.isEmpty()) {
      log.info("Student report cards are empty with job id :{}", currentJob.getId());
      return;
    }
    try {
      var studentIds = studentReportCards.stream().map(StudentReportCard::getStudentId).toList();
      var students = studentRepository.findAllById(studentIds);
      var studentMap =
          students.stream().collect(Collectors.toMap(Student::getId, Function.identity()));
      var termAssessments =
          termAssessmentRepository.getAssessmentsByReportJob(
              studentReportCards.getFirst().getReportCardJob().getId());
      var terms = termAssessments.stream().map(TermAssessment::getTerm).distinct().toList();
      var localDateTime = LocalDateTime.now();

      studentReportCards.forEach(
          studentReportCard -> {
            var tempDir =
                String.format("/temp/%s/%s.pdf", localDateTime, studentReportCard.getStudentId());
            storeStudentReport(
                studentReportCard,
                studentMap.get(studentReportCard.getStudentId()),
                terms,
                tempDir,
                currentJob.getReportCardConfig().getTemplateId());
          });
      var fileDirectory = new File(String.format("/temp/%s/", localDateTime));
      storageService.uploadDirectoryInBulk(
          fileDirectory, constructDr(studentReportCards.getFirst()));
      manageReportCardService.updateReportCardJob(
          currentJob.getId(), ReportCardConfigDto.ReportCardJobStatus.SAVED_REPORTS_SUCCESS, null);
    } catch (Exception e) {
      manageReportCardService.updateReportCardJob(
          currentJob.getId(),
          ReportCardConfigDto.ReportCardJobStatus.SAVED_REPORTS_FAILED,
          e.getMessage());
      log.error("Failed to generate and store the bulk report cards : {}", e.getMessage(), e);
    } finally {
      studentReportCardRepository.saveAll(studentReportCards);
      jobRepository.save(currentJob);
    }
  }

  private void storeStudentReport(
      StudentReportCard studentReportCard,
      Student student,
      List<Term> terms,
      String path,
      Long templateId) {
    try {
      var termSlugs = terms.stream().map(Term::getSlug).toList();
      var request =
          ReportCardDto.Request.builder()
              .termId(terms.getFirst().getId())
              .studentAuthId(student.getUserInfo().getAuthUserId())
              .build();
      Long reportTemplate;
      if (templateId != null) {
        reportTemplate = templateId;
      } else {
        reportTemplate =
            manageReportCardService.getReportTemplate(
                student,
                student.getUserInfo().getOrganization(),
                termSlugs,
                ReportCardTemplateType.CUSTOM);
      }
      var report =
          offlineTestReportService.getStudentReportByOfflineTestDefinition(
              student.getUserInfo().getOrganization(), reportTemplate, request);
      var file = convertByteArrayToFile(report, new File(path));
      if (Objects.nonNull(file)) {
        studentReportCard.setReportCardPath(constructPath(studentReportCard));
        studentReportCard.setReportCardTemplate(reportTemplate);
        studentReportCard.setStatus(REPORT_CARD_GENERATED);
      } else {
        studentReportCard.setStatus(FAILED_TO_GENERATE_REPORT);
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      studentReportCard.setStackTrace(e.getMessage() + " " + e);
      studentReportCard.setStatus(FAILED_TO_GENERATE_REPORT);
      studentReportCard.setFailureReason(
          String.format("Could not download the report card: %s", e.getMessage()));
      studentReportCard.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    }
  }

  private static File convertByteArrayToFile(byte[] byteArray, File file) {
    try {
      FileUtils.writeByteArrayToFile(file, byteArray);
      return file;
    } catch (Exception e) {
      log.error("Could not convert  bytes into file :{}", e.getMessage(), e);
      return null;
    }
  }

  private String constructPath(StudentReportCard studentReportCard) {
    return String.format(
        "%s/report-cards/%s/%s.pdf",
        studentReportCard.getOrgSlug(),
        studentReportCard.getReportCardJob().getId(),
        studentReportCard.getStudentId());
  }

  private String constructDr(StudentReportCard studentReportCard) {
    return String.format(
        "%s/report-cards/%s/",
        studentReportCard.getOrgSlug(), studentReportCard.getReportCardJob().getId());
  }

  private StudentReportCard buildStudentReportCard(
      Student student, ReportCardJob reportCardJob, StudentReportCard studentReportCard) {
    studentReportCard.setStudentId(student.getId());
    studentReportCard.setReportCardJob(reportCardJob);
    studentReportCard.setStatus(STARTED);
    studentReportCard.setFailureReason(null);
    studentReportCard.setStackTrace(null);
    studentReportCard.setReportCardPath(null);
    studentReportCard.setOrgSlug(student.getUserInfo().getOrganization());
    studentReportCard.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    return studentReportCard;
  }

  private String concatFailureReason(
      List<ReportCardConfigData> reportCardConfigData, String failureReason) {
    try {
      StringBuilder result;
      if (!StringUtils.isEmpty(failureReason)) {
        result = new StringBuilder(failureReason);
      } else {
        result = new StringBuilder();
      }
      var failureReasons =
          reportCardConfigData.stream()
              .map(ReportCardConfigData::getFailureReason)
              .filter(Objects::nonNull)
              .toList();
      failureReasons.forEach(reason -> result.append(", ").append(reason));
      return result.toString();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }

  private ReportCardConfigData buildReportCardConfigData(
      OfflineTestScheduleStudentDetails response,
      ReportCardConfigDetail reportCardConfigDetail,
      ReportCardJob reportCardJob) {
    var existingRecord =
        reportCardConfigDataRepository.findByOfflineTestScheduleStudentId(response.getOtssId());
    if (existingRecord.isPresent()) {
      existingRecord.get().setFailureReason(updateFailureReasonIfNotPublished(response));
      existingRecord.get().setTermAssessmentName(response.getAssessmentName());
      existingRecord.get().setActualMarks(response.getStudentMarks());
      existingRecord.get().setCalculatedMarks(calculateMarks(response, reportCardConfigDetail));
      existingRecord.get().setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
      existingRecord.get().setReportCardConfigDetail(reportCardConfigDetail);
      existingRecord.get().setReportCardJob(reportCardJob);
      return existingRecord.get();
    }
    return ReportCardConfigData.builder()
        .termName(reportCardConfigDetail.getTermAssessment().getTerm().getName())
        .termAssessmentName(response.getAssessmentName())
        .reportCardConfigDetail(reportCardConfigDetail)
        .actualMarks(response.getStudentMarks())
        .studentId(response.getStudentId())
        .offlineTestScheduleStudentId(response.getOtssId())
        .orgSlug(reportCardConfigDetail.getReportCard().getOrgSlug())
        .calculatedMarks(calculateMarks(response, reportCardConfigDetail))
        .reportCardJob(reportCardJob)
        .failureReason(updateFailureReasonIfNotPublished(response))
        .build();
  }

  private String updateFailureReasonIfNotPublished(OfflineTestScheduleStudentDetails response) {
    if (Objects.isNull(response.getPublishedAt())) {
      return "Please publish " + response.getSubjectName() + " subject to download the report card";
    }
    return null;
  }

  private BigDecimal calculateMarks(
      OfflineTestScheduleStudentDetails response, ReportCardConfigDetail reportCardConfigDetail) {
    try {
      if (CO_SCHOLASTIC.name().equals(response.getCategory())
          || Objects.isNull(reportCardConfigDetail.getWeightage())) {
        return response.getStudentMarks();
      }
      return Objects.isNull(response.getStudentMarks())
          ? null
          : response
              .getStudentMarks()
              .divide(BigDecimal.valueOf(response.getTotalMarks()), 4, RoundingMode.HALF_UP)
              .multiply(BigDecimal.valueOf(reportCardConfigDetail.getWeightage()))
              .setScale(2, RoundingMode.HALF_UP);
    } catch (Exception e) {
      log.error("Error while calculating marks in generate report card:{}", e.getMessage(), e);
      return null;
    }
  }

  public ReportCardJob validateReportCardJob(long jobId) {
    return jobRepository
        .findById(jobId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Report card job"));
  }
}
