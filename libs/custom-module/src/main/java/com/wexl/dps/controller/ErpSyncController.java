package com.wexl.dps.controller;

import com.wexl.dps.dto.DpsAttendanceSyncDto;
import com.wexl.dps.dto.ErpIntegrationDto.DpsEntityChangeResponse;
import com.wexl.dps.service.AttendanceProcessor;
import com.wexl.dps.service.ErpIntegrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ErpSyncController {

  private final ErpIntegrationService erpIntegrationService;
  private final AttendanceProcessor attendanceProcessor;

  @PostMapping("/public/erp-sync")
  public void syncTeachers(@RequestBody DpsEntityChangeResponse response) {
    log.info("Received to ERP update notifications");
    erpIntegrationService.process(response);
    log.info("Done with ERP Updates");
  }

  @PostMapping("/public/erp-attendance-sync")
  public void updateAttendance(
      @RequestBody DpsAttendanceSyncDto.AttendanceAndRemarksRequest response) {
    attendanceProcessor.erpSyncAttendance(response);
  }
}
