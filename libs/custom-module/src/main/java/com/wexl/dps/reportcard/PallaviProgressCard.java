package com.wexl.dps.reportcard;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.PallaviProgressCardDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PallaviProgressCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final StudentAttributeService studentAttributeService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final UserService userService;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildPallaviHeader(user.getStudentInfo());
    var body = buildBody(user, request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("3rd-4th-grades-pallavi-progress-card.xml");
  }

  private PallaviProgressCardDto.Header buildPallaviHeader(Student student) {
    return PallaviProgressCardDto.Header.builder()
        .schoolName(student.getSchoolName().toUpperCase())
        .academicSection(student.getAcademicYearSlug())
        .studentId(getRollNumber(student.getRollNumber()))
        .build();
  }

  private PallaviProgressCardDto.Body buildBody(User user, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    LowerGradeReportDto.Attendance attendance =
        lowerGradeOverallReportCard.buildAttendance(student.getId());
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(request.offlineTestDefinitionId());
    var gradeScale =
        testDefinition.getGradeScaleSlug() != null ? testDefinition.getGradeScaleSlug() : "4point";
    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var scholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var optionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var classTeacher = student.getSection().getClassTeacher();
    String classTeacherName =
        (classTeacher != null && classTeacher.getUserInfo() != null)
            ? userService.getNameByUserInfo(classTeacher.getUserInfo())
            : "";
    return PallaviProgressCardDto.Body.builder()
        .rollNumber(student.getClassRollNumber() == null ? null : student.getClassRollNumber())
        .name(user.getFirstName() + " " + user.getLastName())
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .orgSlug(user.getOrganization())
        .classTeacherName(classTeacherName)
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .gradeSlug(student.getSection().getGradeSlug())
        .classAndSection(student.getSection().getName())
        .firstTable(buildFirstTable(scholasticDataList, gradeScale))
        .secondTable(buildSecondTable(coScholasticData, gradeScale))
        .thirdTable(buildFirstTable(optionalData, gradeScale))
        .fourthTable(
            buildFourthTable(
                scholasticOptionalData,
                gradeScale,
                buildFirstTable(scholasticDataList, gradeScale)))
        .attendance(buildAttendance(attendance))
        .promotedClass("")
        .date("")
        .place("")
        .gradingScale(gradeScale)
        .build();
  }

  public PallaviProgressCardDto.Attendance buildAttendance(
      LowerGradeReportDto.Attendance attendance) {
    return PallaviProgressCardDto.Attendance.builder()
        .attendancePercentage(attendance.attendancePercentage())
        .workingDays(attendance.workingDays())
        .daysPresent(attendance.daysPresent())
        .remarks(attendance.remarks())
        .build();
  }

  public List<PallaviProgressCardDto.FirstTable> buildSecondTable(
      List<LowerGradeReportCardData> scholasticDataList, String gradeScale) {
    List<PallaviProgressCardDto.FirstTable> firstTables = new ArrayList<>();
    if (scholasticDataList.size() == 0) {
      return Collections.emptyList();
    }
    var scholasticDataMap =
        scholasticDataList.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));
    AtomicLong snoCounter = new AtomicLong(1);
    List<String> absentReasons = List.of("AB", "PA", "PL", "ML");
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var hye = getMarks("hye", scholasticData);
          var fe = getMarks("ye", scholasticData);
          Double termsTotalMarks = 100.0;
          String term1Grade =
              (hye == null)
                  ? null
                  : absentReasons.contains(hye)
                      ? hye
                      : calculateGrades(Double.parseDouble(hye), termsTotalMarks, gradeScale);
          String term2Grade =
              (fe == null)
                  ? null
                  : absentReasons.contains(fe)
                      ? fe
                      : calculateGrades(Double.parseDouble(fe), termsTotalMarks, gradeScale);

          firstTables.add(
              PallaviProgressCardDto.FirstTable.builder()
                  .sno(snoCounter.getAndIncrement())
                  .subjectName(subject)
                  .term1Marks(hye)
                  .term1Grade(term1Grade)
                  .term2Marks(fe)
                  .term2Grade(term2Grade)
                  .build());
        });
    return firstTables;
  }

  private List<PallaviProgressCardDto.FirstTable> buildFourthTable(
      List<LowerGradeReportCardData> scholasticOptionalData,
      String gradeScale,
      List<PallaviProgressCardDto.FirstTable> firstTableData) {
    List<PallaviProgressCardDto.FirstTable> fourthTables = new ArrayList<>();
    if (scholasticOptionalData.isEmpty() && scholasticOptionalData.size() == 0) {
      return Collections.emptyList();
    }
    List<String> absentReasons = List.of("AB", "PA", "PL", "ML");

    var scholasticOptionalDataMap =
        scholasticOptionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    AtomicLong snoCounter = new AtomicLong(firstTableData.size() + 1);
    scholasticOptionalDataMap.forEach(
        (subject, optionalData) -> {
          var hye = getMarks("hye", optionalData);
          var ye = getMarks("ye", optionalData);
          var hyeTotalMarks = getTotalMarks("hye", optionalData);
          var yeTotalMarks = getTotalMarks("ye", optionalData);
          Double termsTotalMarks = 100.0;
          Double overallTotalMarks = 200.0;
          String term1Total = "0";
          String term2Total = "0";
          String term1Grade = null;
          String term2Grade = null;
          if (!absentReasons.contains(hye) && hyeTotalMarks > 0) {
            Double term1TotalDouble = (Double.parseDouble(hye) / hyeTotalMarks) * 100;
            term1Total = String.format("%.2f", term1TotalDouble);
            term1Grade =
                calculateGrades(Double.parseDouble(term1Total), termsTotalMarks, gradeScale);
          }
          if (!absentReasons.contains(ye) && yeTotalMarks > 0) {
            Double term2TotalDouble = (Double.parseDouble(ye) / yeTotalMarks) * 100;
            term2Total = String.format("%.2f", term2TotalDouble);
            term2Grade =
                calculateGrades(Double.parseDouble(term2Total), termsTotalMarks, gradeScale);
          }

          var totalTermMarks = sumMarks(term1Total, term2Total);
          var overallMarks =
              calculateOverallMarks(Double.parseDouble(totalTermMarks), overallTotalMarks);
          String overallGrade =
              calculateGrades(Double.parseDouble(totalTermMarks), overallTotalMarks, gradeScale);
          fourthTables.add(
              PallaviProgressCardDto.FirstTable.builder()
                  .sno(snoCounter.getAndIncrement())
                  .subjectName(subject)
                  .half(hye)
                  .term1Marks(term1Total)
                  .term1Grade(term1Grade)
                  .finalMarks(ye)
                  .term2Marks(term2Total)
                  .term2Grade(term2Grade)
                  .totalMarks(totalTermMarks)
                  .overallMarks(overallMarks)
                  .overallGrade(overallGrade)
                  .build());
        });
    return fourthTables;
  }

  private List<PallaviProgressCardDto.FirstTable> buildFirstTable(
      List<LowerGradeReportCardData> scholasticDataList, String gradeScale) {
    List<PallaviProgressCardDto.FirstTable> firstTables = new ArrayList<>();
    if (scholasticDataList.size() == 0) {
      return Collections.emptyList();
    }
    var scholasticDataMap =
        scholasticDataList.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    AtomicLong snoCounter = new AtomicLong(1);
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getPaMarks("pa1", scholasticData);
          var pa2 = getPaMarks("pa2", scholasticData);
          var hye = getMarks("hye", scholasticData);

          var pa3 = getPaMarks("pa3", scholasticData);
          var pa4 = getPaMarks("pa4", scholasticData);
          var fe = getMarks("ye", scholasticData);

          var term1Total = sumMarks(pa1, pa2, String.valueOf(hye));
          var term2Total = sumMarks(pa3, pa4, String.valueOf(fe));
          Double termsTotalMarks = 100.0;
          Double overallTotalMarks = 200.0;

          var totalTermMarks = sumMarks(term1Total, term2Total);
          var overallMarks =
              calculateOverallMarks(Double.parseDouble(totalTermMarks), overallTotalMarks);
          String term1Grade =
              calculateGrades(Double.parseDouble(term1Total), termsTotalMarks, gradeScale);
          String term2Grade =
              calculateGrades(Double.parseDouble(term2Total), termsTotalMarks, gradeScale);
          String overallGrade =
              calculateGrades(Double.parseDouble(totalTermMarks), overallTotalMarks, gradeScale);
          firstTables.add(
              PallaviProgressCardDto.FirstTable.builder()
                  .sno(snoCounter.getAndIncrement())
                  .subjectName(subject)
                  .pa1(pa1)
                  .pa2(pa2)
                  .half(hye)
                  .term1Marks(term1Total)
                  .term1Grade(term1Grade)
                  .pa3(pa3)
                  .pa4(pa4)
                  .finalMarks(fe)
                  .term2Marks(term2Total)
                  .term2Grade(term2Grade)
                  .totalMarks(totalTermMarks)
                  .overallMarks(overallMarks)
                  .considerPercentage(considerPercentage("ye", scholasticData))
                  .overallGrade(overallGrade)
                  .build());
        });
    return firstTables;
  }

  private String calculateOverallMarks(Double totalTermMarks, Double overallTotalMarks) {
    Double overallMarks = (totalTermMarks / overallTotalMarks) * 100;
    var roundedMarks = Math.round(overallMarks * 100.0) / 100.0;
    return String.format("%.2f", roundedMarks);
  }

  public String sumMarks(String... marks) {
    double sum = 0.0;
    for (String mark : marks) {
      if (mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?")) {
        sum += Double.parseDouble(mark);
      }
    }
    return String.format("%.2f", sum);
  }

  public String getPaMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data =
        subjectData.stream().filter(d -> assessmentSlug.equals(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var attendedData =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (attendedData.isEmpty()) {
      return data.get(0).getRemarks() == null
          ? "AB"
          : data.get(0).getRemarks().substring(0, 2).toUpperCase();
    }

    double average =
        attendedData.stream()
            .filter(
                d ->
                    d.getMarks() != null
                        && d.getTotalMarks() != null
                        && d.getSubjectMarks() != null)
            .mapToDouble(d -> (d.getMarks() / d.getTotalMarks()) * d.getSubjectMarks())
            .sum();

    return String.format("%.2f", average);
  }

  public String getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    String remarksOrMarks =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .findFirst()
            .map(
                data -> {
                  if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                    return Objects.isNull(data.getRemarks())
                        ? "AB"
                        : data.getRemarks().substring(0, 2).toUpperCase().toString();
                  }
                  return null;
                })
            .orElse(null);
    if (remarksOrMarks != null) {
      return remarksOrMarks;
    }

    double sum =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
    return String.format("%.2f", sum);
  }

  public boolean considerPercentage(
      String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    var remarksOrMarks =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .findFirst()
            .orElse(null);

    if (remarksOrMarks == null) {
      return false;
    }

    return remarksOrMarks.getConsiderPercentage();
  }

  private Double getTotalMarks(
      String assessmentSlug, List<LowerGradeReportCardData> scholasticOptionalData) {
    double sum =
        scholasticOptionalData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .map(LowerGradeReportCardData::getTotalMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
    return sum;
  }

  public String calculateGrades(Double marks, Double totalMarks, String gradeScale) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScale, BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private PallaviProgressCardDto.OverallGradeAndPercentage buildOverAllGradeAndPercentage(
      List<PallaviProgressCardDto.FirstTable> firstTable) {

    double totalMarks = 0.0;
    int count = 0;

    for (PallaviProgressCardDto.FirstTable row : firstTable) {
      if (Boolean.TRUE.equals(row.considerPercentage())) {
        totalMarks += Double.parseDouble(row.overallMarks());
        count++;
      }
    }

    double finalPercentage = (count > 0) ? (totalMarks / count) : 0.0;

    String grade = pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(finalPercentage));

    return PallaviProgressCardDto.OverallGradeAndPercentage.builder()
        .overallPercentage(finalPercentage)
        .overAllGrade(grade)
        .build();
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeResponse(
      List<Student> students, Boolean aFalse, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var termAssessments =
                termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
                    List.of("t1", "t2"), student.getSection().getGradeSlug());
            var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
            var data =
                reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
                    student.getId(), termAssessmentIds);
            var scholasticDataList =
                data.stream()
                    .filter(
                        x ->
                            x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                                && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
                    .toList();
            var firstTable = buildFirstTable(scholasticDataList, "5point");
            var overAllGrade = buildOverAllGradeAndPercentage(firstTable);

            if (Objects.equals("A+", overAllGrade.overAllGrade())) {
              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(overAllGrade.overAllGrade())
                      .percentage(String.valueOf(overAllGrade.overallPercentage()))
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeAllSubjectsSummary(
      List<Student> students) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var termAssessments =
                termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
                    List.of("t1", "t2"), student.getSection().getGradeSlug());
            var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
            var data =
                reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
                    student.getId(), termAssessmentIds);
            var scholasticDataList =
                data.stream()
                    .filter(
                        x ->
                            x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                                && !x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
                    .toList();
            var firstTable = buildFirstTable(scholasticDataList, "5point");
            boolean allSubjectsA1 =
                firstTable.stream().allMatch(subject -> "A+".equals(subject.overallGrade()));

            if (allSubjectsA1) {
              var userInfo = student.getUserInfo();
              var name = userInfo.getFirstName() + " " + userInfo.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(firstTable.getFirst().overallGrade())
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }

          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<Map<String, Integer>> getGradeCounts(
      List<Student> students, String subjectSlug, String orgSlug) {
    Map<String, Integer> gradeCounts = new LinkedHashMap<>();
    int t1TotalStudents = 0;
    int t2TotalStudents = 0;

    for (Student student : students) {
      var termAssessments =
          termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
              Arrays.asList("t1", "t2"), student.getSection().getGradeSlug());

      if (termAssessments.isEmpty()) continue;

      var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
      var data =
          reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
              student.getId(), termAssessmentIds);

      if (Objects.isNull(data) || data.isEmpty()) continue;

      var scholasticDataList =
          data.stream()
              .filter(
                  x ->
                      SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                          && SubjectsTypeEnum.MANDATORY.name().equals(x.getType())
                          && x.getSubjectSlug().equals(subjectSlug))
              .toList();

      var marksList = buildFirstTable(scholasticDataList, "5point");
      if (marksList.isEmpty()) continue;

      var marks = marksList.get(0);
      gradeCounts.merge(marks.term1Grade() + "(T1)", 1, Integer::sum);
      gradeCounts.merge(marks.term2Grade() + "(T2)", 1, Integer::sum);

      t1TotalStudents++;
      t2TotalStudents++;
    }

    gradeCounts.put("T1TOTALSTUDENTS", t1TotalStudents);
    gradeCounts.put("T2TOTALSTUDENTS", t2TotalStudents);

    Map<String, Integer> sortedMap = new TreeMap<>(gradeCounts);
    return List.of(sortedMap);
  }
}
