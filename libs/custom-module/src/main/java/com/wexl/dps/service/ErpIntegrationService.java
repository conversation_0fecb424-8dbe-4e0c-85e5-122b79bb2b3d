package com.wexl.dps.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.ErpIntegrationDto;
import com.wexl.dps.dto.ErpIntegrationDto.DpsEntityChangeResponse;
import com.wexl.dps.model.ErpIntegration;
import com.wexl.dps.repository.ErpIntegrationRepository;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ErpIntegrationService {

  private final TeacherProcessor teacherProcessor;
  private final StudentProcessor studentProcessor;
  private final ParentProcessor parentProcessor;
  private final ErpIntegrationRepository erpIntegrationRepository;

  public void process(DpsEntityChangeResponse response) {
    log.info("Processing response: {}", response);

    response
        .dpsEntityChangeList()
        .forEach(
            request -> {
              if ("teacher".equals(request.type())) {
                teacherProcessor.process(request);
              } else if ("student".equals(request.type())) {
                studentProcessor.process(request);
              } else if ("parent".equals(request.type())) {
                parentProcessor.process(request);
              } else {
                log.info("Do not understand type.");
              }
            });

    erpIntegrationRepository.save(
        ErpIntegration.builder()
            .type(guessType(response))
            .lastSyncedAt(LocalDateTime.now())
            .json(responseToString(response))
            .build());
  }

  private String guessType(DpsEntityChangeResponse response) {
    return response.dpsEntityChangeList().stream()
        .map(ErpIntegrationDto.DpsEntityChange::type)
        .findFirst()
        .orElse("unknown");
  }

  private String responseToString(DpsEntityChangeResponse response) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      final String s = objectMapper.writeValueAsString(response);
      return s.substring(0, Math.min(s.length(), 4999));
    } catch (Exception e) {
      String message = e.getMessage() + ExceptionUtils.getStackTrace(e);
      return message.substring(0, Math.min(message.length(), 4999));
    }
  }
}
