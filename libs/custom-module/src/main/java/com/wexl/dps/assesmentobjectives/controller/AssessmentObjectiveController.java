package com.wexl.dps.assesmentobjectives.controller;

import com.wexl.dps.assesmentobjectives.dto.AssessmentObjectiveDto;
import com.wexl.dps.assesmentobjectives.service.AssessmentObjectiveService;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class AssessmentObjectiveController {

  private final AssessmentObjectiveService assessmentObjectiveService;

  @IsOrgAdminOrTeacher
  @PostMapping("/assessment-objectives")
  public AssessmentObjectiveDto.AssessmentObjectiveResponse createAssessmentObjective(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody AssessmentObjectiveDto.AssessmentObjectiveRequest assessmentObjectiveRequest) {
    return assessmentObjectiveService.createAssessmentObjective(
        orgSlug, assessmentObjectiveRequest);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/assessment-objectives")
  public List<AssessmentObjectiveDto.AssessmentObjectiveResponse> getAssessmentObjective(
      @PathVariable("orgSlug") String orgSlug) {
    return assessmentObjectiveService.getAssessmentObjective(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/assessment-objectives/{aOId}")
  public AssessmentObjectiveDto.AssessmentObjectiveResponse getAssessmentObjectiveById(
      @PathVariable("orgSlug") String orgSlug, @PathVariable("aOId") long aOId) {
    return assessmentObjectiveService.getAssessmentObjectiveById(orgSlug, aOId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/assessment-objectives/{aOId}")
  public void updateAssessmentObjectiveById(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("aOId") long aOId,
      @RequestBody AssessmentObjectiveDto.AssessmentObjectiveRequest assessmentObjectiveRequest) {
    assessmentObjectiveService.updateAssessmentObjectiveById(
        orgSlug, aOId, assessmentObjectiveRequest);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/assessment-objectives/{aOId}")
  public void deleteAssessmentObjective(
      @PathVariable("orgSlug") String orgSlug, @PathVariable("aOId") long aOId) {
    assessmentObjectiveService.deleteAssessmentObjective(orgSlug, aOId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/assessment-objectives/{aoId}/ao-detail")
  public void createAssessmentObjectiveDetail(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("aoId") Long aoId,
      @RequestBody AssessmentObjectiveDto.AoDetailRequest detailRequest) {
    assessmentObjectiveService.createAssessmentObjectiveDetail(orgSlug, aoId, detailRequest);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/assessment-objectives/{aOId}/ao-detail/{detailId}")
  public void deleteAssessmentObjectiveDetail(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("aOId") Long aOId,
      @PathVariable("detailId") long detailId) {
    assessmentObjectiveService.deleteAssessmentObjectiveDetail(orgSlug, aOId, detailId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/assessment-objectives/{aOId}/ao-detail")
  public List<AssessmentObjectiveDto.AssessmentObjectiveDetailResponse>
      getAssessmentObjectiveDetails(
          @PathVariable("orgSlug") String orgSlug, @PathVariable("aOId") Long aOId) {
    return assessmentObjectiveService.getAssessmentObjectiveDetails(orgSlug, aOId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/ao-details/{aoDetailId}/publish")
  public void changeAoDetailPublishStatus(@PathVariable("aoDetailId") Long aoDetailId) {
    assessmentObjectiveService.updateAoDetailPublish(aoDetailId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/ao-details/{aoDetailId}")
  public void updateAoDetail(
      @PathVariable("aoDetailId") Long aoDetailId,
      @RequestBody AssessmentObjectiveDto.AoDetailRequest request) {
    assessmentObjectiveService.updateAoDetail(aoDetailId, request);
  }

  @IsTeacher
  @PostMapping("/assessment-objectives/subjects")
  public AssessmentObjectiveDto.ValidAOSubjectResponse getValidateAoSubjects(
      @PathVariable String orgSlug,
      @RequestBody AssessmentObjectiveDto.AssessmentObjectiveRequest request) {
    return assessmentObjectiveService.getValidateAoSubjects(orgSlug, request);
  }
}
