package com.wexl.dps.assesmentobjectives.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Entity
@Table(name = "ao_remarks")
public class AoRemark extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String board;

  private String grade;

  private String subject;

  private String orgSlug;

  @Column(columnDefinition = "TEXT", nullable = false)
  private String remark;

  @Enumerated(EnumType.STRING)
  private AreaRemark areaRemark;

  private Integer seqNumber;

  private Long termId;
}
