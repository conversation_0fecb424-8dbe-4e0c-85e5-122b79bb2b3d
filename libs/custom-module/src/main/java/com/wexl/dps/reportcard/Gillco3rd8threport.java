package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Gillco3rd8thReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.InterOverAllReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class Gillco3rd8threport extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;

  @Value("classpath:gillco-coscholastic.json")
  private Resource resource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildReportCardHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  private Gillco3rd8thReportDto.Header buildReportCardHeader(User user) {
    return Gillco3rd8thReportDto.Header.builder()
        .schoolName("THE SCHOLARS' HOME")
        .address("JAMNIWALA ROAD, BADRIPUR, PAONTA SAHIB DIST.SIRMOUR(H.P.)-173025")
        .build();
  }

  private Gillco3rd8thReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");

    var data = getData(student);
    var scholasticMandatory = buildScholosticMandatory(data);
    var scholasticOptional = buildScholosticOptional(data);
    var getMarksResponse = getMarksResponse(scholasticMandatory, scholasticOptional);

    assert getMarksResponse != null;
    return Gillco3rd8thReportDto.Body.builder()
        .name(user.getFirstName() + " " + user.getLastName())
        .admissionNumber(student.getRollNumber())
        .rollNo(student.getClassRollNumber())
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .sectionName(student.getSection().getName())
        // .height("5'6")
        // .weight("50")
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .scholosticMandatory(scholasticMandatory)
        .scholosticTotalPercentage(buildScholosticTotalPercentage(getMarksResponse))
        .scholosticTotalGrade(buildScholosticTotalGrade(getMarksResponse))
        .scholosticOptional(scholasticOptional)
        // .attendance("95%")
        .coScholostic(buildCoScholosticMandatory(data))
        // .coScholosticOptional(buildCoScholosticOptional(data))
        // .remarks("Good")
        .build();
  }

  private List<Gillco3rd8thReportDto.ScholasticMandatory> buildScholosticMandatory(
      List<LowerGradeReportCardData> data) {
    var scholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var scholasticDataMap =
        scholasticMandatoryData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    List<Gillco3rd8thReportDto.ScholasticMandatory> scholasticMandatory = new ArrayList<>();
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var port = getMarks(List.of("portfolio"), scholasticData);
          var se = getMarks(List.of("se"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);
          var pt = getPtMarks(List.of("pa1", "pa2", "pa3"), scholasticData);
          var ma = getMarks(List.of("ma"), scholasticData);
          var totalScoredMarks = sumMarks(port, se, hye, pt, ma);
          var totalMarks = 100.0;
          var grade = calculateGrade(totalScoredMarks, totalMarks, null);

          scholasticMandatory.add(
              Gillco3rd8thReportDto.ScholasticMandatory.builder()
                  .subject(subject)
                  .portfolio(Double.valueOf(port))
                  .se(Double.valueOf(se))
                  .hye(Double.valueOf(hye))
                  .pt(Double.valueOf(pt))
                  .ma(Double.valueOf(ma))
                  .total(totalMarks)
                  .grade(grade)
                  .build());
        });
    return scholasticMandatory;
  }

  private List<Gillco3rd8thReportDto.ScholasticOptional> buildScholosticOptional(
      List<LowerGradeReportCardData> data) {
    var scholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var scholasticDataMap =
        scholasticOptionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    List<Gillco3rd8thReportDto.ScholasticOptional> scholasticOptional = new ArrayList<>();
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var theoryMarks = getMarks(List.of("theory"), scholasticData);
          var practicalMarks = getMarks(List.of("practical"), scholasticData);
          var totalScoredMarks = sumMarks(theoryMarks, practicalMarks);
          var grade = calculateGrade(totalScoredMarks, 50d, null);

          scholasticOptional.add(
              Gillco3rd8thReportDto.ScholasticOptional.builder()
                  .subject(subject)
                  .theory(Double.valueOf(theoryMarks))
                  .practical(Double.valueOf(practicalMarks))
                  .total(totalScoredMarks)
                  .grade(grade)
                  .build());
        });
    return scholasticOptional;
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .filter(datas -> datas.getMarks() != null)
            .mapToDouble(datas -> datas.getMarks())
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private String getPtMarks(
      List<String> assessmentSlugs, List<LowerGradeReportCardData> subjectData) {

    List<LowerGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(d -> assessmentSlugs.contains(d.getAssessmentSlug()))
            .filter(d -> d.getMarks() != null && d.getSubjectMarks() != null)
            .toList();

    if (filteredData.isEmpty()) {
      return null;
    }

    List<LowerGradeReportCardData> sortedData =
        filteredData.stream()
            .sorted(
                (d1, d2) -> {
                  double percentage1 = (d1.getMarks() / d1.getSubjectMarks()) * 100;
                  double percentage2 = (d2.getMarks() / d2.getSubjectMarks()) * 100;
                  return Double.compare(percentage2, percentage1);
                })
            .limit(2)
            .toList();

    double totalMarks = sortedData.stream().mapToDouble(LowerGradeReportCardData::getMarks).sum();
    double totalSubjectMarks =
        sortedData.stream().mapToDouble(LowerGradeReportCardData::getSubjectMarks).sum();

    double average = (totalMarks / totalSubjectMarks) * 5;

    return String.format("%.2f", average);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private List<Gillco3rd8thReportDto.CoScholastic> buildCoScholosticMandatory(
      List<LowerGradeReportCardData> data) {
    var coScholasticMandatoryData =
        data.stream()
            .filter(x -> SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory()))
            .toList();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    var reportDataMap =
        coScholasticMandatoryData.stream()
            .filter(s -> Objects.nonNull(s.getSubjectSlug()))
            .collect(Collectors.groupingBy(s -> s.getSubjectSlug().toLowerCase()));

    List<Gillco3rd8thReportDto.CoScholastic> response = new ArrayList<>();

    reportDataMap.forEach(
        (subject, reportCardDataList) -> {
          var coScholastic =
              coScholasticAreaResources.stream()
                  .filter(csa -> csa.subjectTitle().contains(subject))
                  .toList();
          List<Gillco3rd8thReportDto.Skill> skills = new ArrayList<>();

          if (!coScholastic.isEmpty()) {
            reportCardDataList.forEach(
                rcd -> {
                  var coScholasticOptional =
                      coScholastic.stream()
                          .filter(csa -> csa.subjectName().contains(rcd.getSubjectName()))
                          .findAny();
                  if (coScholasticOptional.isEmpty()) {
                    return;
                  }
                  String grade;
                  if (rcd.getMarks() != null && rcd.getMarks() != 0) {
                    grade =
                        pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(rcd.getMarks()));
                  } else {
                    grade =
                        Objects.nonNull(rcd.getRemarks()) ? rcd.getRemarks().substring(0, 2) : "-";
                  }
                  skills.add(
                      Gillco3rd8thReportDto.Skill.builder()
                          .subject(rcd.getSubjectName())
                          .grade(grade)
                          .build());
                });
          }
          if (subject.toLowerCase().contains("art-education")) {
            response.add(
                Gillco3rd8thReportDto.CoScholastic.builder()
                    .skillName(subject)
                    .skill(skills)
                    .build());
          } else if (subject.toLowerCase().contains("health-and-physical-education")) {
            response.add(
                Gillco3rd8thReportDto.CoScholastic.builder()
                    .skillName(subject)
                    .skill(skills)
                    .build());
          } else if (subject.toLowerCase().contains("disciplinary-traits")) {
            response.add(
                Gillco3rd8thReportDto.CoScholastic.builder()
                    .skillName(subject)
                    .skill(skills)
                    .build());
          } else if (subject.toLowerCase().contains("general-knowledge")) {
            response.add(
                Gillco3rd8thReportDto.CoScholastic.builder()
                    .skillName(subject)
                    .skill(skills)
                    .build());
          }
        });
    return response;
  }

  private List<InterOverAllReportDto.InterTerm1CoScholastic> getCoScholasticAreaResources() {

    List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreas = new ArrayList<>();
    try {
      var objectMapper = new ObjectMapper();
      coScholasticAreas =
          objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
      if (Objects.isNull(coScholasticAreas) || coScholasticAreas.isEmpty()) {
        return coScholasticAreas;
      }
    } catch (Exception ex) {
      log.error(
          "Unable to process the resource [inter-term1-co-scholastic.json] from the classpath", ex);
      return coScholasticAreas;
    }
    return coScholasticAreas;
  }

  private String buildScholosticTotalPercentage(Gillco3rd8thReportDto.Marks marks) {
    var percentage = (marks.marksScored() / marks.totalMarks()) * 100;
    return String.format("%.2f%%", percentage);
  }

  private String buildScholosticTotalGrade(Gillco3rd8thReportDto.Marks marks) {
    if (Objects.isNull(marks)
        || Objects.isNull(marks.marksScored())
        || Objects.isNull(marks.totalMarks())) {
      return null;
    }
    if (marks.marksScored() == 0 || marks.totalMarks() == 0) {
      return "AB";
    }
    return calculateGrade(marks.marksScored(), marks.totalMarks(), "8point");
  }

  private Gillco3rd8thReportDto.Marks getMarksResponse(
      List<Gillco3rd8thReportDto.ScholasticMandatory> scholasticMandatory,
      List<Gillco3rd8thReportDto.ScholasticOptional> scholasticOptional) {
    if (Objects.isNull(scholasticMandatory) || scholasticMandatory.isEmpty()) {
      return null;
    }
    var mandatoryTotal =
        scholasticMandatory.stream()
            .mapToDouble(Gillco3rd8thReportDto.ScholasticMandatory::total)
            .sum();
    var optionalTotal =
        scholasticOptional.stream()
            .mapToDouble(Gillco3rd8thReportDto.ScholasticOptional::total)
            .sum();
    var scholasticTotal = mandatoryTotal + optionalTotal;
    var totalMarks = (scholasticMandatory.size() * 100) + (scholasticOptional.size() * 50);
    Gillco3rd8thReportDto.Marks marks =
        Gillco3rd8thReportDto.Marks.builder()
            .marksScored(scholasticTotal)
            .totalMarks((double) totalMarks)
            .build();

    return marks;
  }

  private List<LowerGradeReportCardData> getData(Student student) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    return data;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("Gilco-holistic-report-card.xml");
  }
}
