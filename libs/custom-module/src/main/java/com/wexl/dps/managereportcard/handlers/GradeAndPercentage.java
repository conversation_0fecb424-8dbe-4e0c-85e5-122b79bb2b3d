package com.wexl.dps.managereportcard.handlers;

import com.wexl.dps.managereportcard.service.ManageReportCardService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GradeAndPercentage extends AbstractMetricHandler {

  private final ManageReportCardService reportCardService;

  @Override
  public String name() {
    return "grade-percentage-summary";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String orgSlug, GenericMetricRequest genericMetricRequest) {
    String board = (String) genericMetricRequest.getInput().get("board");
    String grade = (String) genericMetricRequest.getInput().get("grade");
    String section = (String) genericMetricRequest.getInput().get("section");

    return reportCardService.buildGradeAndPercentageSummary(
        orgSlug, board, grade, section, Boolean.FALSE);
  }
}
