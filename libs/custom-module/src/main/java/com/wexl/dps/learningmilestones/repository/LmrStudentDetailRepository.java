package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrStudentDetail;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LmrStudentDetailRepository extends JpaRepository<LmrStudentDetail, Long> {

  List<LmrStudentDetail> findAllByOrgSlugAndStudentIdInAndTermIdAndLmrCategoryAttributeIdIn(
      String orgSlug, List<Long> studentIds, Long termId, List<Long> gradeAttributesIds);

  List<LmrStudentDetail> findAllByOrgSlugAndStudentIdAndTermId(
      String orgSlug, Long studentId, Long termId);

  List<LmrStudentDetail> findAllByStudentIdAndTermIdAndLmrCategoryAttributeIdIn(
      Long studentIds, Long termId, List<Long> gradeAttributesIds);

  List<LmrStudentDetail> findAllByOrgSlugAndTermId(String orgSlug, Long termId);
}
