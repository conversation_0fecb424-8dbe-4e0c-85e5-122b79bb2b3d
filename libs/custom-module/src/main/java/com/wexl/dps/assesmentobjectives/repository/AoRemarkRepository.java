package com.wexl.dps.assesmentobjectives.repository;

import com.wexl.dps.assesmentobjectives.model.AoRemark;
import com.wexl.dps.assesmentobjectives.model.AreaRemark;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AoRemarkRepository extends JpaRepository<AoRemark, Long> {

  List<AoRemark> findAllByOrgSlugAndBoardAndGradeAndSubjectAndAreaRemarkAndTermIdOrderBySeqNumber(
      String orgSlug,
      String board,
      String grade,
      String subject,
      AreaRemark areaRemark,
      Long termId);
}
