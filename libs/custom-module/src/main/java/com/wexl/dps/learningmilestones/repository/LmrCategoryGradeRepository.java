package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrCategoryGrade;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LmrCategoryGradeRepository extends JpaRepository<LmrCategoryGrade, Long> {

  List<LmrCategoryGrade> findAllByGradeSlug(String gradeSlug);

  List<LmrCategoryGrade> findByIdInAndTermId(List<Long> lmrGradeId, Long termId);

  Optional<LmrCategoryGrade> findByLmrCategoryIdAndGradeSlugAndTermId(
      Long categoryId, String gradeSlug, Long termId);

  @Query(
      value =
          """
          select lcg.* from lmr_category_grades lcg
          join lmr_subject_metadata lsm on lsm.lmr_category_grade_id = lcg.id
          where lsm.subject_metadata_id = :subjectMetadataId and lcg.grade_slug = :gradeSlug and term_id=:termId
          """,
      nativeQuery = true)
  List<LmrCategoryGrade> getAllByGradeSlugAndSubjectMetadataIdAndTerm(
      String gradeSlug, Long subjectMetadataId, Long termId);

  @Query(
      value =
          """
          select lcg.* from lmr_category_grades lcg
          join lmr_subject_metadata lsm on lsm.lmr_category_grade_id = lcg.id
          where lsm.subject_metadata_id = :subjectMetadataId and lcg.grade_slug = :gradeSlug and term_id=:termId and lcg.lmr_category_id = :lmrCategoryId
          """,
      nativeQuery = true)
  Optional<LmrCategoryGrade> getLmrGradeByGradeSlugAndSubjectMetadataIdAndTermAndCategory(
      String gradeSlug, Long subjectMetadataId, Long termId, Long lmrCategoryId);

  List<LmrCategoryGrade> findAllByGradeSlugAndTermId(String gradeSlug, Long termId);

  @Query(
      value =
          """
                                  select lcg.* from lmr_category_grades lcg
                                  join lmr_subject_metadata lsm on lsm.lmr_category_grade_id = lcg.id
                                  join subject_metadata sm on lsm.subject_metadata_id = sm.id
                                  where sm.org_slug = :orgSlug and lcg.grade_slug = :gradeSlug and lcg.lmr_category_id = :lmrCategoryId and lcg.term_id = :termId
                          """,
      nativeQuery = true)
  Optional<LmrCategoryGrade> getLmrGradeByOrgSlugAndGradeAndTermAndCategory(
      String orgSlug, String gradeSlug, Long lmrCategoryId, Long termId);
}
