package com.wexl.dps.reportcard;

import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto.Header;
import com.wexl.retail.offlinetest.service.reportcard.framework.ReportCardDefinition;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.util.Optional;

public abstract class BaseReportCardDefinition implements ReportCardDefinition {

  public abstract Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key);

  public Header buildHeader(Student student, Organization org) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, "admission_no");
    var cannedReportOrg = "sai681502";
    if (cannedReportOrg.contains(org.getSlug())) {
      return getHeaderForSaiSecondary(student, org, admissionNo);
    } else {
      return getDpsHeader(student, org, admissionNo);
    }
  }

  private Header getDpsHeader(
      Student student, Organization org, Optional<StudentAttributeValueModel> admissionNo) {
    return Header.builder()
        .schoolLogo("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")
        .schoolWaterMark("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg")
        .academicYear("2025 - 26")
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .schoolName(org.getName())
        .address(
            "PLOT NO:44, 42A,BEHIND NACHARAM TELEPHONE EXCHANGE,NACHARAM,UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076")
        .affiliationData("Affiliated to CBSE,New Delhi,Affiliation No :3630057")
        .isoDetails("ISO 9001:2005, ISO 45001:2018, ISO 21001:2018")
        .isoData(
            "Affiliated to CBSE,New Delhi,Affiliation No :3630057 ISO 9001:2005, ISO 45001:2018, ISO 21001:2018")
        .studentId(getRollNumber(student.getRollNumber()))
        .build();
  }

  private Header getHeaderForSaiSecondary(
      Student student, Organization org, Optional<StudentAttributeValueModel> admissionNo) {
    return Header.builder()
        .schoolLogo("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/saisenior_logo.jpeg")
        .academicYear("Session: 2024-25")
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .schoolName(org.getName())
        .address("Village Ladora Narayanpur,Uttar Pradesh PIN: 244901")
        .isoData(" ")
        .studentId(getRollNumber(student.getRollNumber()))
        .build();
  }

  public Long getRollNumber(String rollNumber) {
    try {
      return Long.valueOf(rollNumber);
    } catch (Exception ex) {
      return null;
    }
  }

  private Long getClassRollNumber(String classRollNumber) {
    try {
      return Long.valueOf(classRollNumber);
    } catch (Exception ex) {
      return null;
    }
  }
}
