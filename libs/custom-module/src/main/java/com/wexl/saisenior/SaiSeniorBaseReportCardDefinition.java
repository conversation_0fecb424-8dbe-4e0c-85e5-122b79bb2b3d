package com.wexl.saisenior;

import static java.lang.String.format;

import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudentAttendance;
import com.wexl.retail.offlinetest.service.reportcard.framework.ReportCardDefinition;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import java.text.DecimalFormat;
import java.util.Objects;
import java.util.Optional;

public abstract class SaiSeniorBaseReportCardDefinition implements ReportCardDefinition {

  private ReportCardService reportCardService;

  public ReportCardDto.Header buildHeader(Student student, Organization org) {
    return getHeaderForSaiSecondary(student, org);
  }

  private ReportCardDto.Header getHeaderForSaiSecondary(Student student, Organization org) {
    return ReportCardDto.Header.builder()
        .schoolLogo("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/saisenior_logo.jpeg")
        .academicYear("Academic Year: 2024-25")
        .schoolName(org.getName())
        .address("Village Ladora Narayanpur,Uttar Pradesh PIN: 244901")
        .isoData(" ")
        .studentId(getClassRollNumber(student))
        .reportName("Final Report Card")
        .build();
  }

  private Long getClassRollNumber(Student student) {
    try {
      return Long.valueOf(student.getClassRollNumber());
    } catch (Exception ex) {
      return 0L;
    }
  }

  private Long getRollNumber(String rollNumber) {
    try {
      return Long.valueOf(rollNumber);
    } catch (Exception ex) {
      return null;
    }
  }

  public String getStudentAttributeValue(Student student) {
    var admissionNo = reportCardService.getStudentAttributeValue(student, "admission_no");
    return admissionNo.map(StudentAttributeValueModel::getValue).orElse(null);
  }

  public String constructColumn(ReportCardConfigDetail configDetail, int marks) {
    return Objects.isNull(configDetail)
        ? null
        : format("%s (%s)", configDetail.getTermAssessment().getName(), marks);
  }

  public String determinePassOrFail(double marks) {
    if (marks < 33) {
      return "Fail";
    }
    return "Pass";
  }

  public double formatMarks(double percentage) {
    DecimalFormat decimalFormat = new DecimalFormat("0.00");
    return Double.parseDouble(decimalFormat.format(percentage));
  }

  public double parseOrZero(String str) {
    if (str == null || str.trim().isEmpty()) {
      return 0;
    }

    try {
      return Double.parseDouble(str);
    } catch (NumberFormatException e) {
      return 0;
    }
  }

  public ReportCardDto.Attendance buildAttendance(
      long studentId, Optional<OfflineTestScheduleStudentAttendance> studentAttendance) {

    if (studentAttendance.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return ReportCardDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return ReportCardDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
