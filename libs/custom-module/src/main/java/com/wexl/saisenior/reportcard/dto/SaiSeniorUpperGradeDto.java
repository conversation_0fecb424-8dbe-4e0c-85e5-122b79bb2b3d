package com.wexl.saisenior.reportcard.dto;

import java.util.List;
import lombok.Builder;

public record SaiSeniorUpperGradeDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String reportName,
      String schoolLogo,
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String admissionNo,
      String dateOfBirth,
      String fathersName,
      String mothersName,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      String daysPresent,
      String workingDays,
      String rank,
      String date,
      String generalRemark) {}

  @Builder
  public record FirstTable(
      String column1,
      String column2,
      String column3,
      List<Marks> subject1,
      List<Marks> subject2,
      List<Marks> subject3,
      List<Marks> subject4,
      List<Marks> subject5) {}

  @Builder
  public record Marks(
      String subjectName,
      String term1FA,
      String term1SA,
      String term1IA,
      Double grade,
      Double percentage) {}

  @Builder
  public record SecondTable(List<SecondTableMarks> marks) {}

  @Builder
  public record SecondTableMarks(String name, String grade) {}

  @Builder
  public record ThirdTable(
      String result,
      Double academic,
      String rank,
      String date,
      Double attendance,
      Double overall) {}
}
