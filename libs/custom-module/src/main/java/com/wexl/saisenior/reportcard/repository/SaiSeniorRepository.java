package com.wexl.saisenior.reportcard.repository;

import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;

public interface SaiSeniorRepository extends OfflineTestScheduleStudentRepository {

  @Query(
      value =
          """
                  select DISTINCT sm."name" AS subjectName ,COALESCE(rcc.calculated_marks,0) AS marks, ots.marks as totalMarks,
                         sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug , otss.is_attended as isAttended, otss.remarks as remarks
                          from report_card_config_data rcc
                          join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                          join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                          join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                          join term_assessments ta on ta.id = rccd.term_assessment_id
                          join subject_metadata_students sms on sms.student_id = rcc.student_id
                          join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                            where rcc.student_id = :studentId and rccd.term_assessment_id in (:assessmentIds)""",
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentReportByStudentAndAssessments(
      Long studentId, List<Long> assessmentIds);

  @Query(
      value =
          """
                          select DISTINCT sm."name" AS subjectName ,COALESCE(rcc.calculated_marks,0) AS marks, ots.marks as totalMarks,t.slug as termSlug ,
                            sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug , otss.is_attended as isAttended, otss.remarks as remarks
                            from report_card_config_data rcc join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                            join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                            join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                            join offline_test_definition otd on otd.id = ots.offline_test_definition_id
                            join term_assessments ta on ta.id = otd.assessment_id
                            join term_assessment_grades tag  on tag.term_assessments_id = ta.id
                            join terms t on t.id = ta.term_id
                            join subject_metadata_students sms on sms.student_id = rcc.student_id
                            join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                            where rcc.student_id =:studentId and (cast((:termSlugs) as varChar) is null or t.slug in (:termSlugs))
                            and tag.grade_slug =:gradeSlug and ots.deleted_at is null order by sm.seq_no
                          """,
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentT2ReportByStudentAndAssessments(
      Long studentId, String gradeSlug, List<String> termSlugs);
}
