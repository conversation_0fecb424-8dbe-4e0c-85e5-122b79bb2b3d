package com.wexl.saisenior.reportcard.service;

import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiLowerGradeTerm1Report extends SaiSeniorBaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermRepository termRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senior-1st-5th-term1-report.xml");
  }

  public ReportCardDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();

    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(dateString -> LocalDate.parse(dateString))
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());
    var studentAttendance = getAttendance(student.getId());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var marks =
        saiSeniorRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    var firstTable = buildFirstTable(marks, student);
    return ReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNo(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .thirdTable(buildThirdTable(firstTable, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private ReportCardDto.ThirdTable buildThirdTable(
      ReportCardDto.FirstTable firstTable, ReportCardDto.Attendance studentAttendance) {
    return ReportCardDto.ThirdTable.builder()
        .result(calculateOverAllGrade(firstTable))
        .academic(buildPercentage(firstTable))
        .overall(calculateOverAllPercentage(firstTable))
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private double buildPercentage(ReportCardDto.FirstTable firstTable) {
    double totalMarks =
        firstTable.marks().stream()
            .map(ReportCardDto.Marks::grade)
            .mapToDouble(Double::doubleValue)
            .sum();

    long totalCount = firstTable.marks().size();

    return totalCount > 0 ? formatMarks(totalMarks / totalCount) : 0.0;
  }

  private double calculateOverAllPercentage(ReportCardDto.FirstTable firstTable) {
    double totalMarks =
        firstTable.marks().stream()
            .map(ReportCardDto.Marks::percentage)
            .mapToDouble(Double::doubleValue)
            .sum();

    long totalCount = firstTable.marks().size();

    return totalCount > 0 ? formatMarks(totalMarks / totalCount) : 0.0;
  }

  private ReportCardDto.FirstTable buildFirstTable(
      List<LowerGradeReportCardData> marks, Student student) {

    var section = student.getSection();
    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();
    return ReportCardDto.FirstTable.builder()
        .column1(constructColumn(configDetails.getFirst(), 20))
        .column2(constructColumn(configDetails.get(1), 80))
        .column3(constructColumn(configDetails.get(2), 50))
        .marks(buildFirstTableMarks(marks))
        .build();
  }

  private List<ReportCardDto.Marks> buildFirstTableMarks(List<LowerGradeReportCardData> marks) {
    List<ReportCardDto.Marks> marksList = new ArrayList<>();
    var data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var fa = buildFa(subjectData, subject);
          var sa = buildMarks(subjectData, "s.a", subject);
          var ia = buildMarks(subjectData, "i.a", subject);

          double faValue = parseOrZero(fa);
          double saValue = parseOrZero(sa);
          double iaValue = parseOrZero(ia);
          double academics = faValue + saValue;
          double percentage = faValue + saValue + iaValue;
          double overAllPercentage = percentage > 0 ? formatMarks((percentage) / 150 * 100) : 0.0;

          marksList.add(
              ReportCardDto.Marks.builder()
                  .subjectName(subject)
                  .term1SA(sa)
                  .term1IA(ia)
                  .term1FA(fa)
                  .grade(academics)
                  .percentage(overAllPercentage)
                  .build());
        });
    return marksList;
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData, String assessmentSlug, String subjectName) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getAssessmentSlug().equals(assessmentSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PA")) {
                    return "PA";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private String buildFa(List<LowerGradeReportCardData> subjectData, String subject) {
    List<LowerGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("f.a"))
            .collect(Collectors.toList());

    Optional<Double> attendedMarks =
        filteredData.stream()
            .filter(x -> "true".equals(x.getIsAttended()) && x.getMarks() != null)
            .map(x -> x.getMarks())
            .max(Double::compareTo);

    if (attendedMarks.isPresent()) {
      return String.valueOf(attendedMarks.get());
    } else {
      return filteredData.stream()
          .map(
              x -> {
                if (x.getIsAttended() == null || "false".equals(x.getIsAttended())) {
                  if (x.getRemarks() != null) {
                    if (x.getRemarks().contains("ML")) {
                      return "ML";
                    } else if (x.getRemarks().contains("PA")) {
                      return "PA";
                    } else if (x.getRemarks().contains("Absent")) {
                      return "AB";
                    }
                  }
                  return "AB";
                } else {
                  return x.getMarks() != null ? String.valueOf(x.getMarks()) : "0";
                }
              })
          .max(String::compareTo)
          .orElse("0");
    }
  }

  private ReportCardDto.SecondTable buildSecondTable(List<LowerGradeReportCardData> marks) {
    var data =
        marks.stream()
            .filter(x -> x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name()))
            .toList();
    if (data.isEmpty()) {
      return ReportCardDto.SecondTable.builder().build();
    }
    return ReportCardDto.SecondTable.builder().marks(buildSecondTableMarks(data)).build();
  }

  private List<ReportCardDto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<ReportCardDto.SecondTableMarks> marksList = new ArrayList<>();
    data.forEach(
        d ->
            marksList.add(
                ReportCardDto.SecondTableMarks.builder()
                    .name(d.getSubjectName())
                    .grade(calculateGrade(d.getMarks(), d.getTotalMarks(), d.getRemarks()))
                    .build()));
    return marksList;
  }

  private String calculateOverAllGrade(ReportCardDto.FirstTable firstTable) {
    boolean hasFailingMark =
        firstTable.marks().stream()
            .anyMatch(marks -> determinePassOrFail(marks.grade()).equals("Fail"));
    return hasFailingMark ? "Not Clear" : "Pass";
  }

  private String calculateGrade(Double marks, Double totalMarks, String remarks) {
    return marks == null || marks == 0
        ? (remarks == null ? null : remarks.substring(0, 2).toUpperCase())
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t1").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }
}
