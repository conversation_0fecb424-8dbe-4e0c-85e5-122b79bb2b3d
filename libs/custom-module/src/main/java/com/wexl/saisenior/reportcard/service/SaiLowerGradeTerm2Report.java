package com.wexl.saisenior.reportcard.service;

import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.LowerGradeTerm2Dto;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiLowerGradeTerm2Report extends SaiSeniorBaseReportCardDefinition {
  private final List<String> terms = List.of("t1", "t2");
  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final SaiSeniorRepository saiSeniorRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final TermRepository termRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senor-1st-5th-grade-final-progress-report.xml");
  }

  public LowerGradeTerm2Dto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();

    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(LocalDate::parse)
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var studentAttendance = getAttendance(student.getId());
    var marks =
        saiSeniorRepository.getStudentT2ReportByStudentAndAssessments(
            student.getId(), student.getSection().getGradeSlug(), terms);
    var firstTable = buildFirstTable(marks);
    return LowerGradeTerm2Dto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .thirdTable(buildThirdTable(marks))
        .fourthTable(buildFourthTable(firstTable, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t2").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }

  private LowerGradeTerm2Dto.FirstTable buildFirstTable(List<LowerGradeReportCardData> marks) {
    return LowerGradeTerm2Dto.FirstTable.builder().marks(buildFirstTableMarks(marks)).build();
  }

  private List<LowerGradeTerm2Dto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> marks) {
    List<LowerGradeTerm2Dto.Marks> marksList = new ArrayList<>();
    var data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var fa = buildFa(subjectData, "f.a", subject, "t1");
          var fa2 = buildFa(subjectData, "f.a", subject, "t2");
          var sa = buildMarks(subjectData, "s.a", subject, "t1");
          var sa2 = buildMarks(subjectData, "s.a", subject, "t2");
          var ia = buildMarks(subjectData, "i.a", subject, "t1");
          var ia2 = buildMarks(subjectData, "i.a", subject, "t2");

          double faValue = parseOrZero(fa);
          double saValue = parseOrZero(sa);
          double iaValue = parseOrZero(ia);
          double fa2Value = parseOrZero(fa2);
          double sa2Value = parseOrZero(sa2);
          double ia2Value = parseOrZero(ia2);
          double academics = faValue + saValue + fa2Value + sa2Value;
          String academicsPercentage =
              String.format("%.2f", academics > 0 ? formatMarks((academics) / 200 * 100) : 0.00);
          double percentage = faValue + saValue + iaValue + fa2Value + sa2Value + ia2Value;
          String overAllPercentage =
              String.format("%.2f", percentage > 0 ? formatMarks((percentage) / 300 * 100) : 0.00);

          marksList.add(
              LowerGradeTerm2Dto.Marks.builder()
                  .subjectName(subject)
                  .term1SA(sa)
                  .term2SA(sa2)
                  .term1IA(ia)
                  .term2IA(ia2)
                  .term1FA(fa)
                  .term2FA(fa2)
                  .academics(academicsPercentage)
                  .overAll(overAllPercentage)
                  .build());
        });
    return marksList;
  }

  private String buildFa(
      List<LowerGradeReportCardData> subjectData,
      String assessmentSlug,
      String subject,
      String termSlug) {
    List<LowerGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(
                x ->
                    x.getSubjectName().equals(subject)
                        && x.getAssessmentSlug().equals(assessmentSlug)
                        && x.getTermSlug().equals(termSlug))
            .toList();

    Optional<Double> attendedMarks =
        filteredData.stream()
            .filter(x -> "true".equals(x.getIsAttended()) && x.getMarks() != null)
            .map(LowerGradeReportCardData::getMarks)
            .max(Double::compareTo);

    if (attendedMarks.isPresent()) {
      return String.valueOf(attendedMarks.get());
    } else {
      return filteredData.stream()
          .map(
              x -> {
                if (x.getIsAttended() == null || "false".equals(x.getIsAttended())) {
                  if (x.getRemarks() != null) {
                    if (x.getRemarks().contains("ML")) {
                      return "ML";
                    } else if (x.getRemarks().contains("PL")) {
                      return "PL";
                    } else if (x.getRemarks().contains("Absent")) {
                      return "AB";
                    }
                  }
                  return "AB";
                } else {
                  return x.getMarks() != null ? String.valueOf(x.getMarks()) : "0";
                }
              })
          .max(String::compareTo)
          .orElse("0");
    }
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData,
      String assessmentSlug,
      String subjectName,
      String termSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getTermSlug().equals(termSlug)
                    && x.getAssessmentSlug().equals(assessmentSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PL")) {
                    return "PL";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private LowerGradeTerm2Dto.SecondTable buildSecondTable(List<LowerGradeReportCardData> marks) {
    List<LowerGradeReportCardData> data;
    data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    if (data.isEmpty()) {
      return LowerGradeTerm2Dto.SecondTable.builder().build();
    }
    return LowerGradeTerm2Dto.SecondTable.builder().marks(buildSecondTableMarks(data)).build();
  }

  private List<LowerGradeTerm2Dto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<LowerGradeTerm2Dto.SecondTableMarks> marksList = new ArrayList<>();
    var subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          var optionalTerm1 =
              subjectData.stream()
                  .filter(x -> x.getAssessmentSlug().equals("s.a") && x.getTermSlug().equals("t1"))
                  .findAny();
          var optionalTerm2 =
              subjectData.stream()
                  .filter(x -> x.getAssessmentSlug().equals("s.a") && x.getTermSlug().equals("t2"))
                  .findAny();
          String term1Grade =
              optionalTerm1
                  .map(
                      term1 -> {
                        if ("false".equalsIgnoreCase(term1.getIsAttended())
                            && term1.getRemarks() == null) {
                          return "AB";
                        }
                        return calculateGrade(
                            term1.getMarks(), term1.getTotalMarks(), term1.getRemarks());
                      })
                  .orElse("");

          String term2Grade =
              optionalTerm2
                  .map(
                      term2 -> {
                        if ("false".equalsIgnoreCase(term2.getIsAttended())
                            && term2.getRemarks() == null) {
                          return "AB";
                        }
                        return calculateGrade(
                            term2.getMarks(), term2.getTotalMarks(), term2.getRemarks());
                      })
                  .orElse("");
          marksList.add(
              LowerGradeTerm2Dto.SecondTableMarks.builder()
                  .subject(subject)
                  .term1Grade(term1Grade)
                  .term2Grade(term2Grade)
                  .build());
        });
    return marksList;
  }

  private String calculateGrade(Double marks, Double totalMarks, String remarks) {
    return marks == null || marks == 0
        ? (remarks == null ? null : remarks.substring(0, 2).toUpperCase())
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private LowerGradeTerm2Dto.FourthTable buildFourthTable(
      LowerGradeTerm2Dto.FirstTable firstTable, ReportCardDto.Attendance studentAttendance) {
    return LowerGradeTerm2Dto.FourthTable.builder()
        .result(calculateOverAllGrade(firstTable))
        .academic(calculateOverAllPercentage(firstTable))
        .overall(buildPercentage(firstTable))
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private String calculateOverAllGrade(LowerGradeTerm2Dto.FirstTable firstTable) {
    boolean hasFailingMark;
    hasFailingMark =
        firstTable.marks().stream()
            .anyMatch(
                marks -> determinePassOrFail(Double.parseDouble(marks.academics())).equals("Fail"));
    return hasFailingMark ? "Not Clear" : "Pass";
  }

  private String buildPercentage(LowerGradeTerm2Dto.FirstTable firstTable) {
    double totalMarks;
    long totalCount;

    totalMarks =
        firstTable.marks().stream()
            .map(x -> Double.parseDouble(x.overAll()))
            .mapToDouble(Double::doubleValue)
            .sum();
    totalCount = firstTable.marks().size();
    return totalCount > 0 ? String.format("%.2f", formatMarks(totalMarks / totalCount)) : "0.00";
  }

  private String calculateOverAllPercentage(LowerGradeTerm2Dto.FirstTable firstTable) {
    double totalMarks;
    long totalCount;

    totalMarks =
        firstTable.marks().stream()
            .map(x -> Double.parseDouble(x.academics()))
            .mapToDouble(Double::doubleValue)
            .sum();
    totalCount = firstTable.marks().size();
    return totalCount > 0 ? String.format("%.2f", formatMarks(totalMarks / totalCount)) : "0.00";
  }

  private LowerGradeTerm2Dto.ThirdTable buildThirdTable(List<LowerGradeReportCardData> marks) {
    List<LowerGradeReportCardData> data;
    data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.CURRICULAR_ACTIVITIES.name()))
            .sorted(
                Comparator.comparing(LowerGradeReportCardData::getSeqNo, Comparator.naturalOrder()))
            .toList();

    if (data.isEmpty()) {
      return LowerGradeTerm2Dto.ThirdTable.builder().build();
    }
    return LowerGradeTerm2Dto.ThirdTable.builder().marks(buildThirdTableMarks(data)).build();
  }

  private List<LowerGradeTerm2Dto.ThirdTableMarks> buildThirdTableMarks(
      List<LowerGradeReportCardData> data) {
    List<LowerGradeTerm2Dto.ThirdTableMarks> marksList = new ArrayList<>();
    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var optionalTerm2 =
              subjectData.stream().filter(x -> x.getAssessmentSlug().equals("s.a")).findAny();

          String term2Grade =
              optionalTerm2
                  .map(
                      term2 -> {
                        if ("false".equalsIgnoreCase(term2.getIsAttended())
                            && term2.getRemarks() == null) {
                          return "AB";
                        }
                        return calculateGrade(
                            term2.getMarks(), term2.getTotalMarks(), term2.getRemarks());
                      })
                  .orElse("");
          marksList.add(
              LowerGradeTerm2Dto.ThirdTableMarks.builder().name(subject).grade(term2Grade).build());
        });
    return marksList;
  }
}
