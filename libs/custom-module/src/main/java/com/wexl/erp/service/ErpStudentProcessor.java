package com.wexl.erp.service;

import static com.wexl.retail.section.domain.SectionStatus.ACTIVE;

import com.wexl.erp.dto.ErpDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Gender;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.request.SectionCreateRequest;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.auth.StudentAuthService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ErpStudentProcessor {
  private final StudentAuthService studentAuthService;
  private final UserRepository userRepository;
  private final SectionRepository sectionRepository;
  private final StudentRepository studentRepository;
  private final RoleTemplateRepository roleTemplateRepository;
  private final OrganizationRepository organizationRepository;
  private final SectionService sectionService;
  private final GuardianService guardianService;

  public void process(ErpDto.ErpEntityChange request) {
    switch (request.changeType()) {
      case "ADD" -> createStudent(request);
      case "UPDATE" -> updateStudent(request);
      case "DELETE" -> deleteStudent(request);
      default -> log.error("change Type not found");
    }
  }

  private void updateStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        createStudent(request);
        return;
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      var student = isStudentExists.get();
      var guardians = guardianService.fetchGuardiansDetails(student.getUserInfo().getAuthUserId());
      studentAuthService.editStudent(
          student.getUserInfo().getOrganization(),
          student.getUserInfo().getAuthUserId(),
          buildStudentRequest(request));
      var guardianRequest = buildGuardians(request);
      if (guardians.isEmpty() && !guardianRequest.isEmpty()) {
        guardianService.createGuardian(student.getUserInfo().getAuthUserId(), guardianRequest);
      } else {
        guardians.forEach(
            guardian -> {
              var req =
                  guardianRequest.stream()
                      .filter(
                          guardianRequest1 ->
                              guardianRequest1.getRelationType().equals(guardian.relationType()))
                      .findFirst();
              guardianService.editGuardian(
                  req.get(), guardian.id(), student.getUserInfo().getAuthUserId());
            });
      }
    } catch (Exception e) {
      log.error("Error in updating a student [{}]", e.getMessage());
    }
  }

  public void createStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    try {
      var user = userRepository.findByExternalRef(externalRef);
      if (user.isEmpty()) {
        var newStudentRequest = buildStudentRequest(request);
        var student =
            studentAuthService.createStudent(
                newStudentRequest, request.studentResponse().orgSlug());
        if (!request.studentResponse().fatherName().isEmpty()
            || !request.studentResponse().motherName().isEmpty()) {
          guardianService.createGuardian(
              student.getUserInfo().getAuthUserId(), buildGuardians(request));
        }
      } else {
        var userDetails = user.get();
        userDetails.setDeletedAt(null);
        userDetails.setIsDeleted(null);
        userRepository.save(userDetails);
        updateStudent(request);
      }
    } catch (Exception e) {
      log.error("Error in creating a student [{}]", e.getMessage(), e);
    }
  }

  private void deleteStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.employeeCode();
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return;
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      isStudentExists.ifPresent(
          value ->
              studentAuthService.deleteStudent(
                  value.getUserInfo().getOrganization(), value.getUserInfo().getAuthUserId()));
    } catch (Exception e) {
      log.error("Error in deleting a student [{}]", e.getMessage());
    }
  }

  private StudentRequest buildStudentRequest(ErpDto.ErpEntityChange request) {
    var studentRequest = request.studentResponse();
    var roleTemplate = roleTemplateRepository.findBySlug("student-bet admission profile");
    var schoolName = organizationRepository.findBySlug(studentRequest.orgSlug()).getName();
    var possibleSection =
        sectionRepository.findByNameAndOrganization(
            Objects.isNull(studentRequest.sectionName()) ? "Z" : studentRequest.sectionName(),
            request.studentResponse().orgSlug());
    Section section;
    if (possibleSection.isEmpty()) {
      log.error("Section is not present: {}", studentRequest.sectionUuid());
      log.info("creating Section");
      section =
          sectionService.createSection(
              request.studentResponse().orgSlug(), buildSectionRequest(request));
      log.info("Section created");
    } else {
      section = possibleSection.get();
    }

    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.roleTemplate not found");
    }
    return StudentRequest.builder()
        .section(section.getName())
        .schoolName(schoolName)
        .boardSlug(section.getBoardSlug())
        .email(studentRequest.email())
        .rollNumber(studentRequest.rollNumber())
        .classRollNumber(String.valueOf(studentRequest.classRollNo()))
        .roleTemplate(roleTemplate.getFirst())
        .externalRef(studentRequest.studentCode())
        .lastName(studentRequest.lastName())
        .userName(studentRequest.rollNumber())
        .firstName(studentRequest.firstName())
        .password("password@123")
        .gender(Gender.valueOf(studentRequest.gender()))
        .mobileNumber(studentRequest.phone())
        .academicYearSlug("25-26")
        .gradeSlug(section.getGradeSlug())
        .build();
  }

  private SectionCreateRequest buildSectionRequest(ErpDto.ErpEntityChange request) {
    var gradeSlug = request.studentResponse().grade();
    return SectionCreateRequest.builder()
        .name(request.studentResponse().sectionName())
        .gradeSlug(gradeSlug)
        .status(ACTIVE)
        .boardSlug("cbse")
        .build();
  }

  private List<GuardianRequest> buildGuardians(ErpDto.ErpEntityChange request) {
    var studentResponse = request.studentResponse();
    List<GuardianRequest> guardians = new ArrayList<>();
    guardians.add(
        GuardianRequest.builder()
            .firstName(studentResponse.fatherName())
            .lastName(studentResponse.lastName())
            .relationType(GuardianRole.FATHER)
            .emailId(studentResponse.fatherEmail())
            .isPrimary(true)
            .countryCodeId(1L)
            .mobileNumber(
                studentResponse.fatherPhone() != null
                    ? studentResponse.fatherPhone().replace("+91", "")
                    : "")
            .build());
    guardians.add(
        GuardianRequest.builder()
            .firstName(studentResponse.motherName())
            .lastName(studentResponse.lastName())
            .relationType(GuardianRole.MOTHER)
            .emailId(studentResponse.motherEmail())
            .isPrimary(false)
            .countryCodeId(1L)
            .mobileNumber(
                studentResponse.motherPhone() != null
                    ? studentResponse.motherPhone().replace("+91", "")
                    : "")
            .build());

    return guardians;
  }
}
