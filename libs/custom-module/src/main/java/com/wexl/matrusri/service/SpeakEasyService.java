package com.wexl.matrusri.service;

import com.wexl.matrusri.dto.SpeakEasyDto;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.courses.step.dto.AssetResponse;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SpeakEasyService {
  private final ContentService contentService;
  private final AuthService authService;
  private final String[] subtopicNames =
      new String[] {
        "Learning Outcomes",
        "Grammar",
        "Vocabulary",
        "Conversations",
        "Listening & Comprehension",
        "Activities"
      };

  public SpeakEasyDto.Response getChapterResponse(
      String orgSlug, String board, String gradeSlug, String subjectSlug) {

    var userAuthId = authService.getUserDetails().getAuthUserId();

    List<SubTopicResponse> subtopics =
        contentService.getSubtopicsByBoardAndGradeAndSubject(
            orgSlug,
            board,
            Collections.singletonList(gradeSlug),
            Collections.singletonList(subjectSlug));
    if (subtopics.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubTopicNotFound");
    }

    Set<String> chapterSlugs =
        subtopics.stream().map(SubTopicResponse::getChapterSlug).collect(Collectors.toSet());
    List<SpeakEasyDto.ChapterResponse> chapterResponseList = new ArrayList<>();
    for (String chapter : chapterSlugs) {
      List<AssetResponse> assets = contentService.getAllAssets(chapter, userAuthId, orgSlug);
      List<SubTopicResponse> chapterSubtopics =
          subtopics.stream().filter(subtopic -> chapter.equals(subtopic.getChapterSlug())).toList();
      chapterResponseList.add(
          SpeakEasyDto.ChapterResponse.builder()
              .chapterName(chapterSubtopics.getFirst().getChapterName())
              .chapterSlug(chapterSubtopics.getFirst().getChapterSlug())
              .subtopicResponse(buildSubtopicResponse(chapterSubtopics, assets))
              .build());
    }
    var sortedChapterResponseList =
        chapterResponseList.stream()
            .sorted(Comparator.comparing(SpeakEasyDto.ChapterResponse::chapterName))
            .toList();
    return SpeakEasyDto.Response.builder().chapterResponse(sortedChapterResponseList).build();
  }

  private List<SpeakEasyDto.SubtopicResponse> buildSubtopicResponse(
      List<SubTopicResponse> subtopics, List<AssetResponse> assets) {
    List<SpeakEasyDto.SubtopicResponse> subtopicResponses = new ArrayList<>();
    if (Objects.isNull(subtopics)) {
      return Collections.emptyList();
    }
    for (String subtopicName : subtopicNames) {
      Optional<SubTopicResponse> matchingSubtopic =
          subtopics.stream()
              .filter(subtopic -> subtopicName.equalsIgnoreCase(subtopic.getName()))
              .findFirst();
      boolean videoAvailable = false;
      if (matchingSubtopic.isPresent() && Objects.nonNull(assets)) {
        var asset =
            assets.stream()
                .filter(
                    assetResponse ->
                        matchingSubtopic.get().getSlug().equals(assetResponse.subtopicSlug))
                .toList();
        videoAvailable = !asset.isEmpty();
      }
      SpeakEasyDto.SubtopicResponse.SubtopicResponseBuilder responseBuilder =
          SpeakEasyDto.SubtopicResponse.builder()
              .subtopicName(subtopicName)
              .slug(matchingSubtopic.map(SubTopicResponse::getSlug).orElse(null))
              .active(matchingSubtopic.map(SubTopicResponse::getStatus).orElse(false))
              .isVideosAvailable(videoAvailable);

      subtopicResponses.add(responseBuilder.build());
    }
    return subtopicResponses;
  }
}
