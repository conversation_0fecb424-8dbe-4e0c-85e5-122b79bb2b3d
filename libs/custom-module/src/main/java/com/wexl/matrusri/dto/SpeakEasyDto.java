package com.wexl.matrusri.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record SpeakEasyDto() {
  @Builder
  public record Response(@JsonProperty("data") List<ChapterResponse> chapterResponse) {}

  @Builder
  public record ChapterResponse(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("sub_topics") List<SubtopicResponse> subtopicResponse) {}

  @Builder
  public record SubtopicResponse(
      @JsonProperty("name") String subtopicName,
      @JsonProperty("slug") String slug,
      @JsonProperty("active") Boolean active,
      @JsonProperty("is_video_available") Boolean isVideosAvailable) {}
}
