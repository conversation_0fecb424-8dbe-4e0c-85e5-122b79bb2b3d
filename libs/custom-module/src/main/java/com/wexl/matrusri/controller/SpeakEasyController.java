package com.wexl.matrusri.controller;

import com.wexl.matrusri.dto.SpeakEasyDto;
import com.wexl.matrusri.service.SpeakEasyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class SpeakEasyController {
  private final SpeakEasyService service;

  @GetMapping("/speakeasy")
  public SpeakEasyDto.Response getChapterResponses(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("board") String board,
      @RequestParam("grade") String gradeSlug,
      @RequestParam("subject") String subjectSlug) {
    return service.getChapterResponse(orgSlug, board, gradeSlug, subjectSlug);
  }
}
