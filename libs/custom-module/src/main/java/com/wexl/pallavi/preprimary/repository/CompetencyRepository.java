package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.preprimary.model.Competencies;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CompetencyRepository extends JpaRepository<Competencies, Long> {

  List<Competencies> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
