package com.wexl.pallavi.reports;

import com.wexl.pallavi.dto.FirstTerm6To8ReportDto;
import com.wexl.pallavi.dto.Grade9Dto;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto.Header;
import com.wexl.retail.offlinetest.service.reportcard.framework.ReportCardDefinition;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.text.DecimalFormat;
import java.util.Optional;

public abstract class PallaviBaseReportCardDefinition implements ReportCardDefinition {

  public abstract Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key);

  public Header buildHeader(Student student, Organization org) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, "admission_no");
    return getPallaviHeader(student, org, admissionNo);
  }

  private Header getPallaviHeader(
      Student student, Organization org, Optional<StudentAttributeValueModel> admissionNo) {
    return Header.builder()
        .schoolLogo(
            "https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")
        .schoolWaterMark(
            "https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")
        .academicYear("Session: 2024-25")
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .schoolName(org.getName())
        .address("PLOT NO:-1-5-563/1/414D/NR. NEAR PAKALAKUNTA,ALWAL,SEC-500010")
        .isoData("Affiliated to CBSE,New Delhi,Affiliation No :3630095")
        .studentId(getClassRollNumber(student))
        .sectionName(student.getSection().getName())
        .build();
  }

  private Long getClassRollNumber(Student student) {
    try {
      return Long.valueOf(student.getClassRollNumber());
    } catch (Exception ex) {
      return 0L;
    }
  }

  public FirstTerm6To8ReportDto.Header getPallavi6To8Header(Student student, Organization org) {
    return FirstTerm6To8ReportDto.Header.builder()
        .schoolName(org.getName())
        .address("PLOT NO:-1-5-563/1/414D/NR. NEAR PAKALAKUNTA,ALWAL,SEC-500010")
        .isoData("Affiliated to CBSE,New Delhi,Affiliation No :3630095")
        .academicYear("Session: 2023-24")
        .studentId(student.getRollNumber() == null ? null : getRollNumber(student).toString())
        .build();
  }

  public Grade9Dto.Header getPallaviGrade9Header(Student student, Organization org) {
    return Grade9Dto.Header.builder()
        .schoolName(org.getName())
        .address("PLOT NO:-1-5-563/1/414D/NR. NEAR PAKALAKUNTA,ALWAL,SEC-500010")
        .isoData("Affiliated to CBSE,New Delhi,Affiliation No :3630095")
        .academicYear("Session: 2024-25")
        .studentId(
            student.getRollNumber() == null
                ? null
                : Long.valueOf(getRollNumber(student).toString()))
        .build();
  }

  private Long getRollNumber(Student student) {
    try {
      return Long.valueOf(student.getRollNumber());
    } catch (Exception ex) {
      return 0L;
    }
  }

  public double formatMarks(double percentage) {
    DecimalFormat decimalFormat = new DecimalFormat("0.00");
    return Double.parseDouble(decimalFormat.format(percentage));
  }
}
