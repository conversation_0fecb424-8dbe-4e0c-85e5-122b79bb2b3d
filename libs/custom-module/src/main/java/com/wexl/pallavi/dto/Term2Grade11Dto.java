package com.wexl.pallavi.dto;

import java.util.List;
import lombok.Builder;

public class Term2Grade11Dto {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(String academicSection, String schoolName) {}

  @Builder
  public record Body(
      List<FirstTable> firstTable,
      List<SecondTable> secondTable,
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String teacherName,
      String classAndSection,
      String remarks,
      Long totalWorkingDays,
      Long totalPresentDays,
      double attendancePercentage,
      String dateOfBirth) {}

  @Builder
  public record FirstTable(
      long sno,
      String subjectName,
      String ct1,
      String ct2,
      String term1,
      double totalTerm1,
      String ct3,
      String ct4,
      String finalExam,
      String finalExamPracticals,
      double feappa,
      double overAll) {}

  @Builder
  public record SecondTable(long sno, String subjectName, String term1Grade, String term2Grade) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, double attendancePer, String remarks) {}
}
