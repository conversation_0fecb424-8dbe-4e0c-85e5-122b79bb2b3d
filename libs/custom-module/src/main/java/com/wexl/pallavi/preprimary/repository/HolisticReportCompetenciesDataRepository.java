package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.preprimary.model.HolisticReportCompetenciesData;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface HolisticReportCompetenciesDataRepository
    extends JpaRepository<HolisticReportCompetenciesData, Long> {

  List<HolisticReportCompetenciesData> findAllByStudentId(Long studentId);
}
