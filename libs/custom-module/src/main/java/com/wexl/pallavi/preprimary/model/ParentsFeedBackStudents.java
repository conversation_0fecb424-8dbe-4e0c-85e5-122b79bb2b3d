package com.wexl.pallavi.preprimary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_parents_feedback_students")
public class ParentsFeedBackStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  private String term1;
  private String term2;

  @ManyToOne
  @JoinColumn(name = "pallavi_pre_primary_parents_feedback_id")
  private ParentsFeedBack parentsFeedBack;
}
