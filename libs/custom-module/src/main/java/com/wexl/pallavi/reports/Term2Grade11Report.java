package com.wexl.pallavi.reports;

import com.wexl.pallavi.dto.Term2Grade11Dto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Term2Grade11Report extends PallaviBaseReportCardDefinition {

  private final SaiSeniorRepository saiSeniorRepository;
  private final ReportCardService reportCardService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final List<String> terms = List.of("t1", "t2");
  private final TermAssessmentRepository termAssessmentRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public Term2Grade11Dto.Body buildBody(User user) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();

    var marks =
        saiSeniorRepository.getStudentT2ReportByStudentAndAssessments(
            student.getId(), student.getSection().getGradeSlug(), terms);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var firstTable = buildFirstTable(marks);
    var studentAttendance = getAttendance(student.getId());
    return Term2Grade11Dto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(getDateOfBirth(student))
        .classAndSection(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .remarks(studentAttendance.remarks())
        .totalPresentDays(studentAttendance.daysPresent())
        .totalWorkingDays(studentAttendance.workingDays())
        .attendancePercentage(studentAttendance.attendancePer())
        .build();
  }

  private String getDateOfBirth(Student student) {
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");

    try {

      DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
      DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
      return dateOfBirth
          .map(StudentAttributeValueModel::getValue)
          .map(date -> LocalDate.parse(date, inputFormatter))
          .map(date -> date.format(outputFormatter))
          .orElse(null);
    } catch (Exception ex) {
      DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
      DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
      return dateOfBirth
          .map(StudentAttributeValueModel::getValue)
          .map(date -> LocalDate.parse(date, inputFormatter))
          .map(date -> date.format(outputFormatter))
          .orElse(null);
    }
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("pallavi-11th-grade-term2-report-card.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return Optional.empty();
  }

  private List<Term2Grade11Dto.SecondTable> buildSecondTable(List<LowerGradeReportCardData> marks) {
    List<LowerGradeReportCardData> data;
    data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .sorted(
                Comparator.comparing(LowerGradeReportCardData::getSeqNo, Comparator.naturalOrder()))
            .toList();
    if (data.isEmpty()) {
      return null;
    }
    return buildSecondTableMarks(data);
  }

  private List<Term2Grade11Dto.SecondTable> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<Term2Grade11Dto.SecondTable> marksList = new ArrayList<>();
    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var optionalTerm1 =
              subjectData.stream()
                  .filter(x -> x.getAssessmentSlug().equals("hye") && x.getTermSlug().equals("t1"))
                  .findAny();
          var optionalTerm2 =
              subjectData.stream()
                  .filter(x -> x.getAssessmentSlug().equals("hye") && x.getTermSlug().equals("t2"))
                  .findAny();

          String term1Grade =
              optionalTerm1
                  .map(
                      term1 ->
                          calculateGrade(
                              term1.getMarks(), term1.getTotalMarks(), term1.getRemarks()))
                  .orElse("");
          String term2Grade =
              optionalTerm2
                  .map(
                      term2 ->
                          calculateGrade(
                              term2.getMarks(), term2.getTotalMarks(), term2.getRemarks()))
                  .orElse("");
          marksList.add(
              Term2Grade11Dto.SecondTable.builder()
                  .subjectName(subject)
                  .term1Grade(term1Grade)
                  .term2Grade(term2Grade)
                  .build());
        });
    return marksList;
  }

  private String calculateGrade(Double marks, Double totalMarks, String remarks) {
    return marks == null || marks == 0
        ? (remarks == null ? null : remarks.substring(0, 2).toUpperCase())
        : pointScaleEvaluator.evaluate("3point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private List<Term2Grade11Dto.FirstTable> buildFirstTable(List<LowerGradeReportCardData> marks) {

    List<Term2Grade11Dto.FirstTable> firstTableList = new ArrayList<>();

    var data =
        marks.stream()
            .filter(x -> x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name()))
            .toList();

    var subjectsList =
        data.stream()
            .sorted(Comparator.comparing(LowerGradeReportCardData::getSeqNo))
            .map(LowerGradeReportCardData::getSubjectName)
            .distinct()
            .toList();

    AtomicInteger snoCounter = new AtomicInteger(1);

    subjectsList.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var ct1 = buildMarks(subjectData, subject, "t1", "ct1");
          var ct2 = buildMarks(subjectData, subject, "t1", "ct2");
          var ct3 = buildMarks(subjectData, subject, "t2", "ct3");
          var ct4 = buildMarks(subjectData, subject, "t2", "ct4");
          var term1 = buildMarks(subjectData, subject, "t1", "hye");
          var theory = buildMarks(subjectData, subject, "t2", "theory");
          var practical = buildMarks(subjectData, subject, "t2", "practical");

          double ct1Value = parseOrZero(ct1);
          double ct2Value = parseOrZero(ct2);
          double term1Value = parseOrZero(term1);
          double penPaperValue = parseOrZero(theory);
          double practicalValue = parseOrZero(practical);
          double feppa = penPaperValue + practicalValue;
          double overall = term1Value + feppa;
          double totalTerm1 = ct1Value + ct2Value + term1Value;

          firstTableList.add(
              Term2Grade11Dto.FirstTable.builder()
                  .sno(snoCounter.getAndIncrement())
                  .subjectName(subjectData.get(0).getSubjectName())
                  .ct1(ct1)
                  .ct2(ct2)
                  .term1(term1)
                  .totalTerm1(formatMarks(totalTerm1))
                  .ct3(ct3)
                  .ct4(ct4)
                  .finalExam(theory)
                  .finalExamPracticals(practical)
                  .feappa(formatMarks(feppa))
                  .overAll(formatMarks(overall))
                  .build());
        });
    return firstTableList;
  }

  public double parseOrZero(String str) {
    if (str == null || str.trim().isEmpty()) {
      return 0;
    }

    try {
      return Double.parseDouble(str);
    } catch (NumberFormatException e) {
      return 0;
    }
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData,
      String subjectName,
      String termSlug,
      String assessmentSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getTermSlug().equals(termSlug)
                    && x.getAssessmentSlug().equals(assessmentSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PL")) {
                    return "PL";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private Term2Grade11Dto.Attendance getAttendance(long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return Term2Grade11Dto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return Term2Grade11Dto.Attendance.builder().build();
    }
    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return Term2Grade11Dto.Attendance.builder().build();
    }
    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return Term2Grade11Dto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePer(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
