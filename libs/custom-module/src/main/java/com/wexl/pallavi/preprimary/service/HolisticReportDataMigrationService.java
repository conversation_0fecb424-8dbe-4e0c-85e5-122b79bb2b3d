package com.wexl.pallavi.preprimary.service;

import com.wexl.pallavi.preprimary.dto.ProgressCardDto;
import com.wexl.pallavi.preprimary.model.*;
import com.wexl.pallavi.preprimary.repository.HolisticReportCompetenciesDataRepository;
import com.wexl.pallavi.preprimary.repository.HolisticReportDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.omr.OmrService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.repository.SectionRepository;
import java.util.*;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class HolisticReportDataMigrationService {
  private final HolisticReportDataRepository holisticReportDataRepository;
  private final ProgressCardService pallaviPrimaryProgressCardService;
  private final OmrService omrService;
  private final StudentRepository student;
  private final SectionRepository sectionRepository;
  private final HolisticReportCompetenciesDataRepository holisticReportCompetenciesDataRepository;

  public void migrateHolisticData(
      String orgSlug, ProgressCardDto.HolisticReportRequest requestBody) {

    var section = sectionRepository.findByUuid(UUID.fromString(requestBody.sectionUuid()));
    if (Objects.isNull(section)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound");
    }

    List<Student> students =
        student.findByGradeAndSection(requestBody.gradeSlug(), orgSlug, requestBody.sectionUuid());
    if (students.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.StudentsNotFound");
    }
    List<String> studentsRollNUmber = students.stream().map(Student::getRollNumber).toList();
    List<HolisticReportData> aboutMeData =
        holisticReportDataRepository.findAllByStudentIdIn(studentsRollNUmber);

    if (aboutMeData.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "No Data Found");
    }

    aboutMeData.forEach(
        data -> {
          Student student =
              omrService.getStudentByRollNumber(String.valueOf(data.getStudentId()), orgSlug, true);
          ProgressCardDto.Request request =
              ProgressCardDto.Request.builder()
                  .address(data.getAddress())
                  .iLiveIn(data.getLive())
                  .thingsILike(data.getLike())
                  .term1Height(String.valueOf(data.getHeight()))
                  .term1Weight(String.valueOf(data.getWeight()))
                  .studentId(student.getId())
                  .motherPhoneNo(data.getMotherName())
                  .myFriendsAre(data.getMyFriend())
                  .myFavouriteColoursAre(data.getMyFevColour())
                  .myFavouriteFoods(data.getMyFevFoods())
                  .myFavouriteGames(data.getMyFevGames())
                  .myFavouriteAnimals(data.getMyFevAnimals())
                  .selfAssessments(buildSelfAssessmentRequest(student, orgSlug, data))
                  .peerAssessments(buildPeerAssessmentRequest(student, orgSlug, data))
                  .competenciesList(buildCompetencyDetailsRequest(student, orgSlug))
                  .parentsFeedbacks(buildParentFeedbackRequest(student, orgSlug, data))
                  .build();
          pallaviPrimaryProgressCardService.saveProgressCard(orgSlug, request);
        });
  }

  private List<ProgressCardDto.ParentsFeedback> buildParentFeedbackRequest(
      Student student, String orgSlug, HolisticReportData data) {

    var section = student.getSection();

    List<ProgressCardDto.ParentsFeedback> parentsFeedbacks =
        pallaviPrimaryProgressCardService.getParentsFeedBacks(
            section.getGradeSlug(), orgSlug, student);

    return parentsFeedbacks.stream()
        .filter(
            oldData ->
                "My child enjoys participating in …".equals(oldData.name())
                    || "My child can be supported for … ".equals(oldData.name())
                    || "Have I completed age appropriate vaccination  schedule for my child?"
                        .equals(oldData.name())
                    || "I would also like to share… ".equals(oldData.name()))
        .map(
            oldData ->
                new ProgressCardDto.ParentsFeedback(
                    oldData.id(),
                    oldData.name(),
                    "My child enjoys participating in …".equals(oldData.name())
                        ? data.getParentFeedbackParticipating()
                        : "My child can be supported for … ".equals(oldData.name())
                            ? data.getParentFeedbackSupported()
                            : "I would also like to share… ".equals(oldData.name())
                                ? data.getParentFeedbackLike()
                                : data.getParentFeedbackAppropriate(),
                    oldData.term2()))
        .toList();
  }

  private List<ProgressCardDto.SelfAssessment> buildSelfAssessmentRequest(
      Student student, String orgSlug, HolisticReportData data) {

    var section = student.getSection();

    List<ProgressCardDto.SelfAssessment> selfAssessmentData =
        pallaviPrimaryProgressCardService.getSelfAssessmentDetails(
            section.getGradeSlug(), orgSlug, student);

    return selfAssessmentData.stream()
        .filter(
            oldData ->
                "1. Activities that I find difficult to do".equals(oldData.name())
                    || "2. Activities that I enjoy doing with my friend".equals(oldData.name())
                    || "3. Activities that I enjoy the most ".equals(oldData.name()))
        .map(
            oldData ->
                new ProgressCardDto.SelfAssessment(
                    oldData.id(),
                    oldData.name(),
                    "1. Activities that I find difficult to do".equals(oldData.name())
                        ? data.getSelfAssessmentFindDifficult()
                        : "2. Activities that I enjoy doing with my friend".equals(oldData.name())
                            ? data.getSelfAssessmentEnjoyDoingWithMyFriends()
                            : data.getSelfAssessmentIEnjoyTheMost(),
                    oldData.term2()))
        .toList();
  }

  private List<ProgressCardDto.PeerAssessment> buildPeerAssessmentRequest(
      Student student, String orgSlug, HolisticReportData data) {

    var section = student.getSection();

    List<ProgressCardDto.PeerAssessment> peerAssessmentData =
        pallaviPrimaryProgressCardService.getPeerAssessmentDetails(
            section.getGradeSlug(), orgSlug, student);

    return peerAssessmentData.stream()
        .filter(
            oldData ->
                "1. Helps in completing tasks/activity".equals(oldData.name())
                    || "2. Likes to play with others".equals(oldData.name())
                    || "3. Shares statoinary(crayons/glue/chalk) with classmates"
                        .equals(oldData.name()))
        .map(
            oldData ->
                new ProgressCardDto.PeerAssessment(
                    oldData.id(),
                    oldData.name(),
                    "1. Helps in completing tasks/activity".equals(oldData.name())
                        ? PeerAssessmentTypes.fromString(data.getPeerAssessmentHelpInCompleting())
                        : "2. Likes to play with others".equals(oldData.name())
                            ? PeerAssessmentTypes.fromString(data.getPeerAssessmentLikeToPlay())
                            : PeerAssessmentTypes.fromString(
                                data.getPeerAssessmentSharesStationery()),
                    oldData.term2()))
        .toList();
  }

  private List<ProgressCardDto.Competencies> buildCompetencyDetailsRequest(
      Student student, String orgSlug) {

    var section = student.getSection();

    List<ProgressCardDto.Competencies> competencies =
        pallaviPrimaryProgressCardService.getCompetencyDetails(
            section.getGradeSlug(), orgSlug, student);

    List<HolisticReportCompetenciesData> competencyData =
        holisticReportCompetenciesDataRepository.findAllByStudentId(
            Long.valueOf(student.getRollNumber()));

    return competencies.stream()
        .map(
            competency -> {
              List<ProgressCardDto.Skill> updatedSkills =
                  competency.skills().stream()
                      .map(
                          skill -> {
                            List<ProgressCardDto.Details> updatedDetails =
                                skill.competencyDetails().stream()
                                    .map(
                                        detail -> {
                                          if (detail.term1() == null) {
                                            return competencyData.stream()
                                                .filter(
                                                    data ->
                                                        Stream.of(
                                                                    data.getSubjectName()
                                                                        .trim()
                                                                        .toLowerCase()
                                                                        .split("\s+"))
                                                                .anyMatch(
                                                                    word ->
                                                                        competency
                                                                            .subjectName()
                                                                            .trim()
                                                                            .toLowerCase()
                                                                            .contains(word))
                                                            && Stream.of(
                                                                    data.getSkill()
                                                                        .trim()
                                                                        .toLowerCase()
                                                                        .split("\s+"))
                                                                .anyMatch(
                                                                    word ->
                                                                        skill
                                                                            .skillName()
                                                                            .trim()
                                                                            .toLowerCase()
                                                                            .contains(word))
                                                            && Stream.of(
                                                                    data.getParameter()
                                                                        .trim()
                                                                        .toLowerCase()
                                                                        .split("\s+"))
                                                                .anyMatch(
                                                                    word ->
                                                                        detail
                                                                            .name()
                                                                            .trim()
                                                                            .toLowerCase()
                                                                            .contains(word)))
                                                .findFirst()
                                                .map(
                                                    data ->
                                                        new ProgressCardDto.Details(
                                                            detail.id(),
                                                            detail.name(),
                                                            CompetencyTypes.valueOf(
                                                                data.getGrade()),
                                                            detail.term2()))
                                                .orElse(detail);
                                          }
                                          return detail;
                                        })
                                    .toList();

                            return new ProgressCardDto.Skill(skill.skillName(), updatedDetails);
                          })
                      .toList();

              return new ProgressCardDto.Competencies(
                  competency.id(),
                  competency.subjectName(),
                  competency.subjectSlug(),
                  updatedSkills);
            })
        .toList();
  }
}
