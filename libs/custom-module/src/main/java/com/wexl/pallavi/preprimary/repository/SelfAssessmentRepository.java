package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.preprimary.model.SelfAssessment;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SelfAssessmentRepository extends JpaRepository<SelfAssessment, Long> {
  List<SelfAssessment> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
