package com.wexl.pallavi;

import com.wexl.pallavi.preprimary.dto.HolisticReportCardDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class PallaviService {
  private final String url;
  private final RestTemplate restTemplate;
  private final String token;

  public PallaviService(
      @Value("${urls.dpsIntegrationService:http://dps-integration-service:8080}") String url,
      @Value("${app.contentToken}") String token,
      RestTemplate restTemplate) {
    this.token = token;
    this.url = url;
    this.restTemplate = restTemplate;
  }

  public HolisticReportCardDto.Table3 getPallaviStudentAttendance(String studentCode) {
    try {
      String endPoint = String.format("%s/student/%s/pallavi-student-attendance", url, studentCode);
      HttpHeaders headers = new HttpHeaders();
      headers.setBearerAuth(token);
      HttpEntity<Void> httpEntity = new HttpEntity<>(headers);

      ResponseEntity<HolisticReportCardDto.Table3> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              httpEntity,
              new ParameterizedTypeReference<HolisticReportCardDto.Table3>() {});

      return responseEntity.getBody();
    } catch (Exception ex) {
      return HolisticReportCardDto.Table3.builder().build();
    }
  }
}
