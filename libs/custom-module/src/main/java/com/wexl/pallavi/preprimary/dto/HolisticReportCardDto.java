package com.wexl.pallavi.preprimary.dto;

import com.wexl.pallavi.preprimary.model.CompetencyTypes;
import java.util.List;
import lombok.Builder;

public record HolisticReportCardDto() {

  @Builder
  public record Header(String reportName, String subjectTtl) {}

  @Builder
  public record Body(
      String gradeSlug,
      String orgSlug,
      AllAboutMe allAboutMe,
      Images images,
      List<Competencies> competencies,
      Observations observations,
      List<ParentsFeedback> parentsFeedback,
      HeightAndWeight heightAndWeight,
      List<SelfAssessment> selfAssessment,
      List<PeerAssessment> peerAssessment,
      AcademicPerformance academicPerformance) {}

  @Builder
  public record AllAboutMe(
      String studentName,
      Long studentId,
      String admissionNo,
      String dateOfBirth,
      String classTeacher,
      String fatherName,
      String motherName,
      String fatherPhoneNo,
      String motherPhoneNo,
      String gradeSlug,
      String gradeName,
      String sectionName,
      String address,
      String thingsILike,
      String iLiveIn,
      String myFriendsAre,
      String myFavouriteColoursAre,
      String myFavouriteFoods,
      String myFavouriteGames,
      String myFavouriteAnimals) {}

  @Builder
  public record Images(
      String aGlimpseOfMyFamily, String aGlimpseOfMySelf, String learnersPortFolio) {}

  @Builder
  public record Competencies(String subjectName, String subjectSlug, List<Skill> skills) {}

  @Builder
  public record Skill(String skillName, List<Details> details) {}

  @Builder
  public record Details(
      Long id, String subjectValue, CompetencyTypes term1Value, CompetencyTypes term2Value) {}

  @Builder
  public record Observations(String term1, String term2) {}

  @Builder
  public record ParentsFeedback(Long id, String name, String term1, String term2) {}

  @Builder
  public record HeightAndWeight(
      String term1Height, String term2Height, String term1Weight, String term2Weight) {}

  @Builder
  public record SelfAssessment(Long id, String name, String term1, String term2) {}

  @Builder
  public record PeerAssessment(Long id, String name, Integer term1, Integer term2) {}

  @Builder
  public record AcademicPerformance(
      List<Table1> table1, List<Table1> table4, List<Table2> table2, Table3 table3) {}

  @Builder
  public record Table1(
      String orp,
      String rp,
      String term1,
      String orpTerm2,
      String rpTerm2,
      String term2,
      String rpandterm1,
      String rpandterm2,
      String term1AndTerm2,
      String subjectName,
      String subjectSlug,
      String penAndPaper1,
      String cwOrhw1,
      String seOrma1,
      String penAndPaper2,
      String cwOrhw2,
      String seOrma2,
      String hye,
      Double total,
      String grade,
      String gradeTerm2,
      String term1AndTerm2Grade,
      double overallTerm1AndTerm2Percentage,
      String overallGrading) {}

  @Builder
  public record Table2(String subjectName, String term, String term2) {}

  @Builder
  public record Table3(
      Long nwMarch,
      Long npMarch,
      Long npMarchAttendancePercentage,
      Long nwApril,
      Long npApril,
      Long npAprilAttendancePercentage,
      Long npMay,
      Long nwMay,
      Long npMayAttendancePercentage,
      Long nwJune,
      Long npJune,
      Long npJuneAttendancePercentage,
      Long nwJuly,
      Long npJuly,
      Long npJulyAttendancePercentage,
      Long nwAug,
      Long npAug,
      Long npAugAttendancePercentage,
      Long nwSep,
      Long npSep,
      Long npSepAttendancePercentage,
      Long nwOct,
      Long npOct,
      Long npOctAttendancePercentage,
      Long npNov,
      Long nwNov,
      Long npNovAttendancePercentage,
      Long nwDec,
      Long npDec,
      Long npDecAttendancePercentage,
      Long nwJan,
      Long npJan,
      Long npJanAttendancePercentage,
      Long nwFeb,
      Long npFeb,
      Long npFebAttendancePercentage,
      Double attendancePercentage1,
      Double attendancePercentage2,
      Long overallPercentage1,
      Long overallPercentage2) {}
}
