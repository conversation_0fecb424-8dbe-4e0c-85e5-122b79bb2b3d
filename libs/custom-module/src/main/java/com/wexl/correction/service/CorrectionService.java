package com.wexl.correction.service;

import com.wexl.correction.dto.SubjectiveCorrectionDto;
import com.wexl.correction.dto.SubjectiveCorrectionDto.SubjectiveCorrectionSubmitAnswerRequest;
import com.wexl.correction.dto.SubjectiveCorrectionDto.SubjectiveCorrectionSubmitAnswerResponse;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.omr.OmrService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.ExamQuestion;
import com.wexl.retail.student.answer.StudentAnswerRequest;
import com.wexl.retail.student.exam.ExamFactory;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.mock.MockExamService;
import com.wexl.retail.student.exam.school.SchoolExamService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.MlpStudentOmrProcessor;
import com.wexl.retail.util.UploadService;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorrectionService {

  private final StorageService storageService;

  private final ScheduleTestService scheduleTestService;

  private final OmrService omrService;

  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;

  private final TestDefinitionService testDefinitionService;

  private final ExamFactory examFactory;

  private final SchoolExamService schoolExamService;
  private final UploadService uploadService;
  private final MlpStudentOmrProcessor mlpStudentOmrProcessor;
  private final ExamRepository examRepository;
  private final MockExamService mockExamService;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final TestQuestionRepository testQuestionRepository;

  @Value("${app.storageBucket}")
  private String s3BucketName;

  public String getPdfFileUploadUrl(String orgSlug, String reference, Long testScheduleId) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("tsi", String.valueOf(testScheduleId));

    String path = "student-corrections/uploads/%s/%s.pdf".formatted(orgSlug, reference);
    return storageService.generatePreSignedUrlForUpload(path, metadata);
  }

  public SubjectiveCorrectionSubmitAnswerResponse processSubjectiveCorrection(
      Long testScheduleId,
      SubjectiveCorrectionSubmitAnswerRequest correctionRequest,
      String bearerToken) {
    var scheduleTest = scheduleTestService.findTestScheduleById(testScheduleId);
    var student =
        omrService.getStudentByRollNumber(
            correctionRequest.rollNumber(), scheduleTest.getOrgSlug(), true);
    if (Objects.isNull(student)) {
      return null;
    }
    var tss = omrService.getTss(scheduleTest, student.getUserInfo().getId());
    if (TestStudentStatus.SUBMITTED.name().equals(tss.getStatus())) {
      log.error("Already submitted");
      return null;
    }

    long testTakenCount =
        examRepository.getScheduleTestTakenCountByStudentId(student.getId(), scheduleTest.getId());
    if (correctionRequest.force() && testTakenCount > 0) {
      scheduleTestStudentService.deleteExam(
          scheduleTest.getId(), student.getUserInfo().getAuthUserId());
      testTakenCount = 0;
    }
    if (testTakenCount > 0) {
      log.error("Already submitted");
      return null;
    }

    if (TestType.SCHOOL_TEST.equals(scheduleTest.getTestDefinition().getType())) {
      tss.setStatus(Constants.STARTED);
      scheduleTestStudentRepository.save(tss);
      var studentAnswerRequest = buildStudentAnswerRequest(scheduleTest, correctionRequest);
      var submitResponse = schoolExamService.submitSchoolTest(studentAnswerRequest, bearerToken);
      return SubjectiveCorrectionSubmitAnswerResponse.builder()
          .examId(submitResponse.getExamDetails().getExamId())
          .build();
    }
    if (TestType.MOCK_TEST.equals(scheduleTest.getTestDefinition().getType())) {
      tss.setStatus(Constants.STARTED);
      scheduleTestStudentRepository.save(tss);
      var studentAnswerRequest =
          buildMockTestStudentAnswerRequest(scheduleTest, correctionRequest, tss);
      mockExamService.submitMockExam(studentAnswerRequest, scheduleTest.getOrgSlug());
      schoolExamService.updateScheduleTestStatus(scheduleTest);
      return SubjectiveCorrectionSubmitAnswerResponse.builder()
          .examId(studentAnswerRequest.examId())
          .build();
    }
    return null;
  }

  private ElpDto.StudentAnswerRequest buildMockTestStudentAnswerRequest(
      ScheduleTest scheduleTest,
      SubjectiveCorrectionSubmitAnswerRequest correctionRequest,
      ScheduleTestStudent tss) {
    var exam = schoolExamService.createExam(scheduleTest, examFactory.createMockTest());
    if (Objects.nonNull(correctionRequest.answerSheetPath())) {
      String filePath =
          uploadService.constructAnswerSheetFilePath(
              exam.getId(), "%s.pdf".formatted(exam.getId()));
      storageService.copyFile(
          s3BucketName, correctionRequest.answerSheetPath(), s3BucketName, filePath);
      log.info("answer sheet is copied in S3");
    }
    List<TestScheduleStudentAnswer> tssaList = new ArrayList<>();

    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuidAndUserName(
            tss.getUuid(), tss.getStudent().getUserName());

    correctionRequest
        .answers()
        .forEach(
            ans -> {
              var tssAns =
                  testScheduleStudentAnswers.stream()
                      .filter(
                          tssa ->
                              ans.uuid().equals(tssa.getQuestionUuid())
                                  && ans.type()
                                      .toLowerCase()
                                      .equalsIgnoreCase(tssa.getQuestionType().toString()))
                      .toList()
                      .getFirst();
              switch (ans.type().toUpperCase()) {
                case "MCQ" -> {
                  if (ans.answer() != null) {
                    tssAns.setMcqSelectedAnswer(
                        mlpStudentOmrProcessor.getSelectedAnswer(
                            ans.answer().toLowerCase().trim()));
                    tssAns.setAttemptStatus(StudentTestAttemptStatus.ANSWERED);
                  }
                }
                case "SUBJECTIVE" -> {
                  tssAns.setSubjectiveWrittenAnswer(ans.answer());
                  tssAns.setAttemptStatus(StudentTestAttemptStatus.ANSWERED);
                }
                default -> {}
              }
              tssaList.add(tssAns);
            });

    return ElpDto.StudentAnswerRequest.builder()
        .examId(exam.getId())
        .questions(buildQuestionResponse(tssaList, correctionRequest.answers()))
        .build();
  }

  public List<QuestionDto.StudentQuestionStatusResponse> buildQuestionResponse(
      List<TestScheduleStudentAnswer> testScheduleStudentAnswers,
      List<SubjectiveCorrectionDto.Answer> correctionAnswers) {
    return testScheduleStudentAnswers.stream()
        .map(
            tssa -> {
              var correctionAnswer =
                  correctionAnswers.stream()
                      .filter(
                          answer ->
                              tssa.getQuestionUuid().equals(answer.uuid())
                                  && tssa.getQuestionType()
                                      .toString()
                                      .equalsIgnoreCase(answer.type()))
                      .findFirst();
              var testQuestion =
                  testQuestionRepository.findByIdAndQuestionUuid(
                      tssa.getTestQuestionId(), tssa.getQuestionUuid());
              return QuestionDto.StudentQuestionStatusResponse.builder()
                  .questionUuid(tssa.getQuestionUuid())
                  .testQuestionMarks(testQuestion.getMarks())
                  .questionType(tssa.getQuestionType())
                  .tssaUuid(tssa.getUuid().toString())
                  .status(tssa.getAttemptStatus())
                  .mcqSelectedAnswer(tssa.getMcqSelectedAnswer())
                  .subjectiveWrittenAnswer(tssa.getSubjectiveWrittenAnswer())
                  .msqSelectedAnswer(tssa.getMsqSelectedAnswer())
                  .natSelectedAnswer(tssa.getNatSelectedAnswer())
                  .yesNoSelectedAnswer(tssa.getYesNoSelectedAnswer())
                  .fbqSelectedAnswer(tssa.getFbqSelectedAnswer())
                  .pbqSelectedAnswer(tssa.getPbqAnswers())
                  .amcqSelectedAnswer(tssa.getAmcqSelectedAnswer())
                  .spchSelectedAnswer(tssa.getSpchSelectedAnswer())
                  .ddFbqAttemptedAnswer(tssa.getDdfbqAttemptedAnswer())
                  .aiAnalysis(
                      correctionAnswer.map(SubjectiveCorrectionDto.Answer::aiAnalysis).orElse(null))
                  .aiMarks(
                      correctionAnswer.map(SubjectiveCorrectionDto.Answer::aiMarks).orElse(null))
                  .build();
            })
        .toList();
  }

  private StudentAnswerRequest buildStudentAnswerRequest(
      ScheduleTest scheduleTest,
      SubjectiveCorrectionDto.SubjectiveCorrectionSubmitAnswerRequest request) {

    var testDefinitionResponse =
        testDefinitionService.getQuestionsPreconfigured(scheduleTest.getTestDefinition(), 0);

    var exam = schoolExamService.createExam(scheduleTest, examFactory.createScheduledTest());
    if (Objects.nonNull(request.answerSheetPath())) {
      String filePath =
          uploadService.constructAnswerSheetFilePath(
              exam.getId(), "%s.pdf".formatted(exam.getId()));
      storageService.copyFile(s3BucketName, request.answerSheetPath(), s3BucketName, filePath);
      log.info("answer sheet is copied in S3");
    }
    Map<String, Question> questionMap =
        testDefinitionResponse.getQuestionsAndOptions().stream()
            .collect(Collectors.toMap(Question::getUuid, q -> q));

    List<ExamQuestion> examQuestions =
        request.answers().stream()
            .map(
                correctionRequest -> {
                  var question = questionMap.get(correctionRequest.uuid());
                  return ExamQuestion.builder()
                      .questionId(question.getId())
                      .questionUuid(question.getUuid())
                      .answer(constructAnswer(correctionRequest))
                      .type(question.getType())
                      .build();
                })
            .toList();

    return StudentAnswerRequest.builder()
        .examId((int) exam.getId())
        .examQuestions(examQuestions)
        .build();
  }

  private String constructAnswer(SubjectiveCorrectionDto.Answer correctionRequest) {
    if ("mcq".equals(correctionRequest.type()) && correctionRequest.answer() != null) {
      // this is for handling mcq type questions
      Integer selectedAnswer =
          mlpStudentOmrProcessor.getSelectedAnswer(correctionRequest.answer().toLowerCase());
      return selectedAnswer != null ? selectedAnswer.toString() : null;
    }
    return correctionRequest.answer();
  }
}
