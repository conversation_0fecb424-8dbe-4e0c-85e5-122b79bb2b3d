package com.wexl.mlp;

import com.wexl.retail.content.ContentService;
import com.wexl.retail.courses.step.dto.AssetResponse;
import com.wexl.retail.student.mlp.service.MlpAssetHandler;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1)
@Component
@RequiredArgsConstructor
public class MlpSynopsisAssetHandler implements MlpAssetHandler {
  private final ContentService contentService;

  @Override
  public List<AssetResponse> getAssets(String orgSlug, String teacherAuthId, String chapterSlug) {
    return contentService.getAssetsBySynopsis(chapterSlug, teacherAuthId, orgSlug);
  }
}
