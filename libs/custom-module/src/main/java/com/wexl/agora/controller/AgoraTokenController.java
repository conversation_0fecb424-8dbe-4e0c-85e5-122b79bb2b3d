package com.wexl.agora.controller;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.agora.dto.AgoraTokenDto;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.model.User;
import io.agora.media.RtcTokenBuilder2;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}/agora-token")
public class AgoraTokenController {
  @Value("${app.conferencing.agora.appId}")
  private String appId;

  @Value("${app.conferencing.agora.appCertificate}")
  private String appCertificate;

  private final AuthService authService;

  @PostMapping
  public AgoraTokenDto.Response generateToken(
      @PathVariable String orgSlug, @RequestBody AgoraTokenDto.Request request) {
    final User userDetails = authService.getUserDetails();
    RtcTokenBuilder2 token = new RtcTokenBuilder2();
    int tokenExpirationInSeconds = 3600;
    int privilegeExpirationInSeconds = 3600;
    String result =
        token.buildTokenWithUserAccount(
            appId,
            appCertificate,
            request.channelName(),
            userDetails.getFirstName() + " " + userDetails.getLastName(),
            WEXL_INTERNAL.equals(orgSlug)
                ? RtcTokenBuilder2.Role.ROLE_PUBLISHER
                : RtcTokenBuilder2.Role.ROLE_SUBSCRIBER,
            tokenExpirationInSeconds,
            privilegeExpirationInSeconds);
    return AgoraTokenDto.Response.builder().jwt(result).build();
  }
}
