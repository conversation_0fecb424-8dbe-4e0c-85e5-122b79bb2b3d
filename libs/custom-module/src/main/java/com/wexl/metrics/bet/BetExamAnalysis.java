package com.wexl.metrics.bet;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BetExamAnalysis extends AbstractMetricHandler implements <PERSON>ricHandler {
  private final BetExamService betExamService;

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    return betExamService.getBetExamAnalysis(
        (String) genericMetricRequest.getInput().get(AUTHUSERID),
        (String) genericMetricRequest.getInput().get(TEST_SCHEDULE_ID));
  }

  @Override
  public String name() {
    return "bet-exam-analysis";
  }
}
