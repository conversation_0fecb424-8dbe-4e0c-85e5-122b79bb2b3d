package com.wexl.registry.job;

import com.wexl.registry.service.HarborRegistryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class HarborRegistryCleanupJob {
  private final HarborRegistryService harborRegistryService;

  @Scheduled(cron = "${harbor.registry.cleanup.schedule:0 0 0 * * *}") // Run every day at midnight
  @SchedulerLock(
      name = "harborRegistryCleanupJob",
      lockAtMostFor = "${harbor.registry.cleanup.lock.at.most:2m}", // > worst-case runtime
      lockAtLeastFor = "${harbor.registry.cleanup.lock.at.least:2m}") // <- optional smoothing
  @Profile("dev")
  public void run() {
    log.info("Harbor registry cleanup job started");
    harborRegistryService.cleanupOldImages();
  }
}
