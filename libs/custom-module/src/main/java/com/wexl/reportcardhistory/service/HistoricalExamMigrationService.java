package com.wexl.reportcardhistory.service;

import com.wexl.reportcardhistory.Repository.AcademicYearRepository;
import com.wexl.reportcardhistory.Repository.StudentHistoricalReportTypeRepository;
import com.wexl.reportcardhistory.model.StudentHistoryResponseDto;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.metrics.reportcards.dto.*;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class HistoricalExamMigrationService {
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final StudentRepository studentRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final AcademicYearRepository academicYearRepository;
  private final StudentHistoricalReportTypeRepository studentHistoricalReportTypeRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final GuardianRepository guardianRepository;
  private final StudentHistoricalReportRepository studentHistoricalReportRepository;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final StorageService storageService;
  private final ReportCardService reportCardService;
  private final StudentAttributeService studentAttributeService;

  public void migrateHistoricalExams(String orgId, String academicYearSlug) {
    log.info(
        "Migrating historical exams for org: {} and academic year: {}", orgId, academicYearSlug);

    try {
      var testDefinitions =
          offlineTestDefinitionRepository.getOfflineTestScheduleForReportCard(
              orgId, null, null, Collections.singletonList(academicYearSlug));

      for (OfflineTestDefinition testDefinition : testDefinitions) {
        try {
          var testSchedules =
              offlineTestScheduleRepository.findByOfflineTestDefinitionIn(
                  Collections.singletonList(testDefinition));
          var testScheduleStudents =
              offlineTestScheduleStudentRepository.findByOfflineTestScheduleDetailsIn(
                  testSchedules);
          var distinctStudents =
              testScheduleStudents.stream()
                  .map(OfflineTestScheduleStudent::getStudentId)
                  .distinct()
                  .toList();

          var academicYear = academicYearRepository.findBySlug(academicYearSlug).orElseThrow();
          var reportType = getReportType(orgId, testDefinition);

          for (Long studentId : distinctStudents) {
            try {
              var student = studentRepository.findByPrevStudentId(studentId).orElse(null);

              if (student == null) {
                log.info("Student not found for ID: {}", studentId);
                continue;
              }
              var user = student.getUserInfo();
              previousStudentReportData(testDefinition, student, academicYear, reportType);
            } catch (Exception e) {
              log.info("Error processing student with ID {}: {}", studentId, e.getMessage(), e);
            }
          }
        } catch (Exception e) {
          log.info(
              "Error processing test definition {}: {}", testDefinition.getId(), e.getMessage(), e);
        }
      }
    } catch (Exception e) {
      log.info(
          "Error during migration of historical exams for org: {} and academic year: {}",
          orgId,
          academicYearSlug,
          e);
    }
  }

  private void previousStudentReportData(
      OfflineTestDefinition testDefinition,
      Student student,
      AcademicYear academicYear,
      StudentHistoricalReportType reportType) {
    var userInfo = student.getUserInfo();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var father = guardianRepository.findByRelationTypeAndStudent(GuardianRole.FATHER, student);
    StudentHistoricalReport studentHistoricalReport = new StudentHistoricalReport();
    studentHistoricalReport.setStudentId(student.getId());
    studentHistoricalReport.setUserId(userInfo.getId());
    studentHistoricalReport.setStudentFirstName(userInfo.getFirstName());
    studentHistoricalReport.setStudentLastName(userInfo.getLastName());
    studentHistoricalReport.setGradeName(testDefinition.getGradeName());
    studentHistoricalReport.setGradeSlug(testDefinition.getGradeSlug());
    studentHistoricalReport.setAdmissionNumber(student.getRollNumber());
    studentHistoricalReport.setAcademicYearId(academicYear);
    studentHistoricalReport.setReportTypeId(reportType);
    studentHistoricalReport.setFatherName(
        (father != null && !father.isEmpty())
            ? father.get(0).getFirstName() + " " + father.get(0).getLastName()
            : "-");
    studentHistoricalReport.setDob(studentAttributeService.getDateOfBirthFormat(dateOfBirth));
    studentHistoricalReport.setDescription("Historical Report for Mgcv Students 2023-2024");
    studentHistoricalReportRepository.save(studentHistoricalReport);

    var reportCardTemplate =
        reportCardTemplateRepository
            .findByOrgSlugAndConfig(testDefinition.getOrgSlug(), "mgcv-canned-report.xml")
            .orElseThrow();
    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(testDefinition.getId())
            .studentAuthId(userInfo.getAuthUserId())
            .build();
    byte[] data =
        offlineTestReportService.getStudentReportByOfflineTestDefinition(
            testDefinition.getOrgSlug(), reportCardTemplate.getId(), request);

    String filePath =
        String.format(
            "historical_reports/%s/%s/%s/%s/%s.pdf",
            testDefinition.getOrgSlug(),
            academicYear.getSlug(),
            testDefinition.getGradeSlug(),
            student.getUserInfo().getAuthUserId(),
            testDefinition.getTitle());
    studentHistoricalReport.setS3Path(filePath);
    storageService.uploadFile(data, filePath, MediaType.APPLICATION_PDF_VALUE);
    log.info("Uploaded report for student {} to S3: {}", student.getId(), filePath);
  }

  private StudentHistoricalReportType getReportType(
      String orgId, OfflineTestDefinition testDefinition) {
    var reportType =
        studentHistoricalReportTypeRepository.findByOrgSlugAndTestDefinitionId(
            orgId, testDefinition.getId());
    if (reportType.isPresent()) {
      return reportType.get();
    }
    var reportTypeEntity = new StudentHistoricalReportType();
    reportTypeEntity.setReportType(testDefinition.getTitle());
    reportTypeEntity.setTestDefinitionId(testDefinition.getId());
    reportTypeEntity.setOrgSlug(orgId);
    return studentHistoricalReportTypeRepository.save(reportTypeEntity);
  }

  public List<StudentHistoryResponseDto.StudentHistoryResponse> getHistoricalExamStudents(
      String academicYear, String gradeSlug) {

    List<StudentHistoryReportInterface> data =
        studentHistoricalReportRepository.getReportByAcademicYearIdAndTestDefinitionIdAndGradeSlug(
            academicYear, gradeSlug);

    return data.stream()
        .map(
            studentData ->
                StudentHistoryResponseDto.StudentHistoryResponse.builder()
                    .path(storageService.generatePreSignedUrlForUpload(studentData.getPath()))
                    .studentName(studentData.getStudentName())
                    .testName(studentData.getTestName())
                    .gradeName(studentData.getGradeName())
                    .offlineTestDefinitionId(studentData.getOfflineTestDefinition())
                    .build())
        .toList();
  }
}
