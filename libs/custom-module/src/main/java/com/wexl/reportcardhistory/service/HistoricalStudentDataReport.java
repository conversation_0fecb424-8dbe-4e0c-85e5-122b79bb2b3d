package com.wexl.reportcardhistory.service;

import com.wexl.dps.reportcard.BaseReportCardDefinition;
import com.wexl.reportcardhistory.model.HistoricalStudentDataReportDto;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.metrics.reportcards.ReportCardDto;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.metrics.reportcards.dto.StudentHistoricalReportRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class HistoricalStudentDataReport extends BaseReportCardDefinition {
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final StudentHistoricalReportRepository studentHistoricalReportRepository;
  private final ReportCardService reportCardService;
  private final GuardianRepository guardianRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var header = buildHeader(student, org, request.offlineTestDefinitionId());
    var body = buildBody(student, org.getSlug(), request.offlineTestDefinitionId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    return reportCardTemplate.getConfig().equals("mgcv-canned-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return Optional.empty();
  }

  private HistoricalStudentDataReportDto.Body buildBody(
      Student student, String orgSlug, Long offlineTestDefinitionId) {
    var studentReports =
        studentHistoricalReportRepository
            .getReportByOrgSlugAndReportTypeIdAndStudentId(
                orgSlug, student.getId(), offlineTestDefinitionId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "No historical report found for the given student and organization"));

    var offlineTestDefinition =
        offlineTestDefinitionRepository
            .findById(studentReports.getOfflineTestDefinition())
            .orElseThrow(() -> new IllegalArgumentException("Offline test definition not found"));

    offlineTestDefinition
        .getOfflineTestScheduleSchedule()
        .forEach(
            ts ->
                ReportCardDto.StudentReportCard.builder()
                    .subjectName(ts.getSubjectsMetaData().getName())
                    .totalMarks(ts.getMarks())
                    .subjectSlug(ts.getSubjectsMetaData().getWexlSubjectSlug())
                    .myMarks(reportCardService.getStudentMarks(ts, student.getId()))
                    .build());
    var offlineTestSchedules =
        offlineTestScheduleRepository.findByTestDefinitionId(offlineTestDefinition.getId());
    if (offlineTestSchedules.isEmpty()) {
      throw new IllegalStateException("No offline test schedules found for the test definition");
    }

    var offlineTestScheduleStudents =
        offlineTestScheduleStudentRepository.findByOfflineTestScheduleDetailsIn(
            offlineTestSchedules);

    List<HistoricalStudentDataReportDto.FirstTable> firstTableList = new ArrayList<>();
    offlineTestSchedules.forEach(
        schedule -> {
          offlineTestScheduleStudents.stream()
              .filter(studentEntry -> studentEntry.getOfflineTestScheduleDetails().equals(schedule))
              .toList()
              .stream()
              .filter(
                  studentEntry -> studentEntry.getStudentId().equals(student.getPrevStudentId()))
              .findFirst()
              .ifPresent(
                  scheduleStudent ->
                      firstTableList.add(
                          HistoricalStudentDataReportDto.FirstTable.builder()
                              .subject(schedule.getSubjectsMetaData().getName())
                              .marks(
                                  Objects.isNull(scheduleStudent.getMarks())
                                      ? 0
                                      : scheduleStudent.getMarks().longValue())
                              .totalMarks(schedule.getMarks())
                              .build()));
        });
    long totalMarks =
        firstTableList.stream().mapToLong(HistoricalStudentDataReportDto.FirstTable::marks).sum();
    long averageMarks = firstTableList.isEmpty() ? 0 : totalMarks / firstTableList.size();
    return HistoricalStudentDataReportDto.Body.builder()
        .total(totalMarks)
        .average(averageMarks)
        .firstTable(firstTableList)
        .build();
  }

  private Object buildHeader(Student studentInfo, Organization org, Long offlineTestDefinitionId) {
    var offlineTestDefinition =
        offlineTestDefinitionRepository.findById(offlineTestDefinitionId).orElseThrow();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(studentInfo, "date_of_birth");
    var father = guardianRepository.findByRelationTypeAndStudent(GuardianRole.FATHER, studentInfo);
    return HistoricalStudentDataReportDto.Header.builder()
        .testName(offlineTestDefinition.getTitle())
        .studentName(
            studentInfo.getUserInfo().getFirstName()
                + " "
                + studentInfo.getUserInfo().getLastName())
        .fatherName(
            (father != null && !father.isEmpty())
                ? father.get(0).getFirstName() + " " + father.get(0).getLastName()
                : "-")
        .dob(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNo(studentInfo.getRollNumber())
        .gradeName(offlineTestDefinition.getGradeName())
        .academicYear(offlineTestDefinition.getAcademicYearSlug())
        .studentId(studentInfo.getId())
        .build();
  }
}
