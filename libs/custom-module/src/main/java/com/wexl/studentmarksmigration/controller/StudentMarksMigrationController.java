package com.wexl.studentmarksmigration.controller;

import com.wexl.studentmarksmigration.dto.StudentMarksMigrationDto;
import com.wexl.studentmarksmigration.service.StudentMarksMigrationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/public/orgs/wexl-internal")
public class StudentMarksMigrationController {

  private final StudentMarksMigrationService studentMarksMigrationService;

  @PostMapping("/students-marks-migration")
  public void migrateStudentMarks(
      @RequestBody StudentMarksMigrationDto.StudentMarksMigrationRequest marksMigrationRequest) {
    studentMarksMigrationService.migrateStudentMarksByStudentRollNumber(marksMigrationRequest);
  }
}
