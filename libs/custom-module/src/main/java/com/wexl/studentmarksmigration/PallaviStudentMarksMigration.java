package com.wexl.studentmarksmigration;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "pallavi_student_marks_migration")
public class PallaviStudentMarksMigration extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String classRollNumber;
  private String studentId;
  private String termAssessment;
  private String subjectName;
  private String marks;
  private String isPresent;
  private String orgSlug;
  private String offlineTestScheduleId;
  private String offlineTestDefinitionId;
}
