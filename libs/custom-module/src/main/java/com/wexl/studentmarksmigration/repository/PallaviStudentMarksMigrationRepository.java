package com.wexl.studentmarksmigration.repository;

import com.wexl.studentmarksmigration.PallaviStudentMarksMigration;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PallaviStudentMarksMigrationRepository
    extends JpaRepository<PallaviStudentMarksMigration, Long> {
  List<PallaviStudentMarksMigration> findByClassRollNumber(String rollNumber);

  List<PallaviStudentMarksMigration> findBySubjectNameInAndClassRollNumberInAndTermAssessment(
      List<String> subjectNames, List<String> studentRollNumbers, String assessmentCategory);
}
