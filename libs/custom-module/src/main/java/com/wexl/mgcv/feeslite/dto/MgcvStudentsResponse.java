package com.wexl.mgcv.feeslite.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MgcvStudentsResponse {
  @JsonProperty("user_name")
  private String userName;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("class_roll_number")
  private String classRollNumber;

  @JsonProperty("term1_status")
  private String term1Status;

  @JsonProperty("term2_status")
  private String term2Status;

  @JsonProperty("term1_Date")
  private Long term1Date;

  @JsonProperty("term2_Date")
  private Long term2Date;

  @JsonProperty("admission_no")
  private String admissionNo;

  @JsonProperty("academic_year")
  private String academicYear;

  @JsonProperty("user_id")
  private Long userId;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("section_name")
  private String sectionName;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_name")
  private String gradeName;
}
