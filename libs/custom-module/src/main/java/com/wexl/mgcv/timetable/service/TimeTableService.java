package com.wexl.mgcv.timetable.service;

import static com.wexl.retail.util.Constants.BACK_SLASH;

import com.wexl.mgcv.timetable.dto.TimeTableResponse;
import com.wexl.mgcv.timetable.model.MgcvTimeTable;
import com.wexl.mgcv.timetable.repository.MgcvTimeTableRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.storage.StorageService;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class TimeTableService {

  private final MgcvTimeTableRepository mgcvTimeTableRepository;

  private final SectionService sectionService;

  private final StorageService storageService;

  private final UserRepository userRepository;

  private final StudentRepository studentRepository;

  private final SectionRepository sectionRepository;

  public void uploadSectionTimeTableService(String orgSlug, String sectionUuid, byte[] content) {

    try {
      Section section =
          sectionRepository
              .findByOrganizationAndUuid(orgSlug, UUID.fromString(sectionUuid))
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST,
                          "error.sectionNotFound",
                          new String[] {String.valueOf(sectionUuid)}));
      String filePath = constructTimeTableFilePath(section);
      storageService.uploadFile(content, filePath, MediaType.APPLICATION_PDF_VALUE);
      MgcvTimeTable mgcvTimeTable =
          MgcvTimeTable.builder()
              .orgSlug(orgSlug)
              .boardSlug(section.getBoardSlug())
              .gradeSlug(section.getGradeSlug())
              .sectionUuid(sectionUuid)
              .filePath(filePath)
              .build();
      mgcvTimeTableRepository.save(mgcvTimeTable);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private String constructTimeTableFilePath(Section section) {
    return BACK_SLASH
        .concat(section.getOrganization())
        .concat(BACK_SLASH + "mgcv-timetable")
        .concat(BACK_SLASH + section.getGradeName())
        .concat(BACK_SLASH + section.getName().concat(".pdf"));
  }

  public TimeTableResponse.StudentTimeTableResponse sectionTimeTableResponse(
      String orgSlug, String studentAuthId) {
    User user = userRepository.findByAuthUserIdAndOrganization(studentAuthId, orgSlug);
    Student student = studentRepository.findByUserId(user.getId());
    MgcvTimeTable mgcvTimeTable =
        mgcvTimeTableRepository
            .findByOrgSlugAndSectionUuid(orgSlug, String.valueOf(student.getSection().getUuid()))
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.TimeTableNotFound",
                        new String[] {student.getSection().getName()}));

    return TimeTableResponse.StudentTimeTableResponse.builder()
        .timeTableId(mgcvTimeTable.getId())
        .orgSlug(mgcvTimeTable.getOrgSlug())
        .sectionUuid(mgcvTimeTable.getSectionUuid())
        .sectionName(student.getSection().getName())
        .url(
            storageService.generatePreSignedUrlForFetch(mgcvTimeTable.getFilePath()) != null
                ? storageService.generatePreSignedUrlForFetch(mgcvTimeTable.getFilePath())
                : null)
        .build();
  }
}
