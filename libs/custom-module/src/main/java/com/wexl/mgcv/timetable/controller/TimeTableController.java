package com.wexl.mgcv.timetable.controller;

import com.wexl.mgcv.timetable.dto.TimeTableResponse;
import com.wexl.mgcv.timetable.service.TimeTableService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class TimeTableController {

  @Autowired private final TimeTableService timeTableService;

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/sections/{sectionUuid}/time-tables")
  public void uploadSectionTimeTable(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("sectionUuid") String sectionUuid,
      @RequestPart(value = "file") MultipartFile multipartFile)
      throws IOException {

    if (multipartFile == null || multipartFile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    timeTableService.uploadSectionTimeTableService(orgSlug, sectionUuid, multipartFile.getBytes());
  }

  @IsStudent
  @GetMapping("/student/{studentAuthId}/time-tables")
  public TimeTableResponse.StudentTimeTableResponse getSectionTimeTable(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId) {
    return timeTableService.sectionTimeTableResponse(orgSlug, studentAuthId);
  }
}
