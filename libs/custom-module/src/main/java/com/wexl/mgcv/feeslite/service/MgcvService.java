package com.wexl.mgcv.feeslite.service;

import com.wexl.mgcv.feeslite.dto.MgcvStudent;
import com.wexl.mgcv.feeslite.dto.MgcvStudentFeesRequest;
import com.wexl.mgcv.feeslite.dto.MgcvStudentsResponse;
import com.wexl.mgcv.feeslite.model.MgcvFeesLite;
import com.wexl.mgcv.feeslite.repository.MgcvFeesLiteRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MgcvService {

  private final SectionService sectionService;
  private final MgcvFeesLiteRepository mgcvFeesLiteRepository;
  private final StudentService studentService;
  private final DateTimeUtil dateTimeUtil;
  private final StrapiService strapiService;
  private final ContentService contentService;

  public List<MgcvStudentsResponse> getStudentsBySection(String orgSlug, String sectionUuid) {
    Section section = sectionService.findByUuid(sectionUuid);
    List<MgcvStudent> mgcvStudents =
        mgcvFeesLiteRepository.getMgcvStudentsBySection(orgSlug, section.getId());
    return mgcvStudents.stream()
        .map(
            mgcvStudent -> {
              String academicYear = getAcademicYearName(mgcvStudent.getAcademicYear());
              return MgcvStudentsResponse.builder()
                  .studentId(mgcvStudent.getStudentId())
                  .userId(mgcvStudent.getUserId())
                  .academicYear(academicYear)
                  .admissionNo(mgcvStudent.getRollNumber())
                  .classRollNumber(mgcvStudent.getClassRollNumber())
                  .userName(mgcvStudent.getUsername())
                  .firstName(mgcvStudent.getFirstName())
                  .lastName(mgcvStudent.getLastName())
                  .gradeName(section.getGradeName())
                  .sectionName(section.getName())
                  .term1Status(
                      Objects.isNull(mgcvStudent.getTerm1Status())
                          ? "Not Paid"
                          : mgcvStudent.getTerm1Status())
                  .term2Status(
                      Objects.isNull(mgcvStudent.getTerm2Status())
                          ? "Not Paid"
                          : mgcvStudent.getTerm2Status())
                  .build();
            })
        .sorted(
            Comparator.comparing(
                mgcvStudentsResponse -> {
                  String classRollNumber = mgcvStudentsResponse.getClassRollNumber();
                  return classRollNumber == null || classRollNumber.isEmpty()
                      ? Long.MAX_VALUE
                      : Long.parseLong(classRollNumber);
                },
                Comparator.nullsLast(Comparator.naturalOrder())))
        .toList();
  }

  public List<MgcvStudentsResponse> getStudentsByAuthUserId(String orgSlug, String authUserId) {
    List<MgcvStudent> mgcvStudents =
        mgcvFeesLiteRepository.getMgcvStudentByAuthUserId(orgSlug, authUserId);
    return mgcvStudents.stream()
        .map(
            mgcvStudent -> {
              String academicYear = getAcademicYearName(mgcvStudent.getAcademicYear());
              return MgcvStudentsResponse.builder()
                  .userId(mgcvStudent.getUserId())
                  .studentId(mgcvStudent.getStudentId())
                  .admissionNo(mgcvStudent.getRollNumber())
                  .classRollNumber(mgcvStudent.getClassRollNumber())
                  .academicYear(academicYear)
                  .userName(mgcvStudent.getUsername())
                  .firstName(mgcvStudent.getFirstName())
                  .lastName(mgcvStudent.getLastName())
                  .term1Status(mgcvStudent.getTerm1Status())
                  .term2Status(mgcvStudent.getTerm2Status())
                  .sectionName(mgcvStudent.getSectionName())
                  .term1Date(
                      Objects.nonNull(mgcvStudent.getTerm1Date())
                          ? mgcvStudent.getTerm1Date().getTime()
                          : null)
                  .term2Date(
                      Objects.nonNull(mgcvStudent.getTerm2Date())
                          ? mgcvStudent.getTerm2Date().getTime()
                          : null)
                  .gradeName(getGrade(mgcvStudent.getClassId()))
                  .boardName(getBoard(mgcvStudent.getBoardId()))
                  .build();
            })
        .sorted(
            Comparator.comparing(
                MgcvStudentsResponse::getClassRollNumber,
                Comparator.nullsLast(Comparator.naturalOrder())))
        .toList();
  }

  private String getGrade(int classId) {
    final Grade grade = contentService.getGradeById(classId);
    return grade.getName();
  }

  private String getBoard(int boardId) {
    final Entity board = strapiService.getEduBoardById(boardId);
    return board.getName();
  }

  public void updateStudentFees(String authUserId, MgcvStudentFeesRequest mgcvStudentFeesRequest) {
    Student student = studentService.getStudentByAuthId(authUserId);
    MgcvFeesLite mgcvFeesLite = mgcvFeesLiteRepository.findByStudent(student);
    Timestamp term1Date = null;
    Timestamp term2Date = null;
    if (mgcvStudentFeesRequest != null) {
      term1Date = convertToTimestamp(mgcvStudentFeesRequest.getTerm1Date());
      term2Date = convertToTimestamp(mgcvStudentFeesRequest.getTerm2Date());
    }
    if (mgcvFeesLite == null) {
      mgcvFeesLiteRepository.save(
          MgcvFeesLite.builder()
              .student(student)
              .term1Status(
                  mgcvStudentFeesRequest != null ? mgcvStudentFeesRequest.getTerm1Status() : null)
              .term1PaidDate(term1Date)
              .term2PaidDate(term2Date)
              .term2Status(
                  mgcvStudentFeesRequest != null ? mgcvStudentFeesRequest.getTerm2Status() : null)
              .build());
      return;
    }
    mgcvFeesLite.setTerm1Status(
        mgcvStudentFeesRequest != null ? mgcvStudentFeesRequest.getTerm1Status() : null);
    mgcvFeesLite.setTerm1PaidDate(term1Date);
    mgcvFeesLite.setTerm2PaidDate(term2Date);
    mgcvFeesLite.setTerm2Status(
        mgcvStudentFeesRequest != null ? mgcvStudentFeesRequest.getTerm2Status() : null);
    mgcvFeesLiteRepository.save(mgcvFeesLite);
  }

  private String getAcademicYearName(String academicYear) {
    if (academicYear != null && academicYear.length() == 5) {
      String[] parts = academicYear.split("-");
      if (parts.length == 2) {
        academicYear = "20" + parts[0] + "-20" + parts[1];
      }
    }
    return academicYear;
  }

  private Timestamp convertToTimestamp(Long epochTime) {
    if (epochTime != null) {
      return dateTimeUtil.convertEpochToTimestamp(epochTime);
    }
    return null;
  }
}
