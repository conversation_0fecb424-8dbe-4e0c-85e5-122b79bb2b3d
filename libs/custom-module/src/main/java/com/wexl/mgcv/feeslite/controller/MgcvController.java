package com.wexl.mgcv.feeslite.controller;

import com.wexl.mgcv.feeslite.dto.MgcvStudentFeesRequest;
import com.wexl.mgcv.feeslite.dto.MgcvStudentsResponse;
import com.wexl.mgcv.feeslite.service.MgcvService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class MgcvController {
  private final MgcvService mgcvService;
  private final SimpleDataControllerHelper simpleDataControllerHelper;

  @GetMapping("/mgcv-students")
  public List<MgcvStudentsResponse> getAllStudentBySection(
      @PathVariable String orgSlug,
      @RequestParam(value = "section_uuid", required = false) String sectionUuid,
      @RequestParam(value = "auth_user_id", required = false) String authUserId) {
    if (sectionUuid == null && authUserId == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Please select sectionId");
    }
    if (authUserId != null) {
      return mgcvService.getStudentsByAuthUserId(orgSlug, authUserId);
    }
    return mgcvService.getStudentsBySection(orgSlug, sectionUuid);
  }

  @PutMapping("/mgcv-students/{authUserId}")
  public void updateStudentFees(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestBody MgcvStudentFeesRequest mgcvStudentFeesRequest) {
    mgcvService.updateStudentFees(authUserId, mgcvStudentFeesRequest);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/mgcv-custom-subjects", produces = MediaType.APPLICATION_JSON_VALUE)
  public String getMgcvCustomSubjects(@PathVariable String orgSlug) throws IOException {
    return simpleDataControllerHelper.getStringResponseEntity("mgcv-subjects.json");
  }
}
