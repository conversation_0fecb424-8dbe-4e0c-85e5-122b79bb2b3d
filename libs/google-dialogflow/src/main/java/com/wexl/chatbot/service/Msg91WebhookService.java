package com.wexl.chatbot.service;

import com.wexl.chatbot.DialogflowService;
import com.wexl.chatbot.Msg91Sender;
import com.wexl.chatbot.dto.WhatsAppBotDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class Msg91WebhookService {
  private final DialogflowService df;
  private final Msg91Sender sender;

  public void handleWebhook(WhatsAppBotDto.Msg91WebhookEvent evt) {
    // must reply < 5 s or MSG91 drops it
    log.info("Received MSG91 webhook event from sender: {}", evt.sender());

    String messageText;
    String senderNumber = evt.sender();

    // Extract message text - either from direct text field or from messages list
    if (evt.text() != null && !evt.text().isEmpty()) {
      messageText = evt.text();
      log.debug("Extracted message from text field: {}", messageText);
    } else if (evt.messages() != null && !evt.messages().isEmpty()) {
      WhatsAppBotDto.Messages message = evt.messages().get(0);
      if (message.text() != null && message.text().body() != null) {
        messageText = message.text().body();
        log.debug("Extracted message from messages list: {}", messageText);
      } else {
        // Handle non-text messages (like documents, images, etc.)
        messageText = "I received your message but can only process text for now.";
        log.info("Received non-text message type: {}", message.type());
      }
    } else {
      messageText = "I couldn't understand your message.";
      log.warn("Could not extract message text from webhook event");
    }

    try {
      // Process message with DialogFlow
      log.info("Processing message with DialogFlow: {}", messageText);
      // Using sender's number as session ID for conversation context
      String response = df.detectIntent(senderNumber, messageText);
      log.info("Received response from DialogFlow: {}", response);

      // Send response back to the user
      log.info("Sending response to sender: {}", senderNumber);
      sender.sendWhatsAppBotMessage(response, senderNumber);

    } catch (Exception e) {
      log.error("Error processing webhook event", e);
    }
  }
}
