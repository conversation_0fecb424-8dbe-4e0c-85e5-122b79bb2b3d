package com.wexl.chatbot;

import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.dialogflow.cx.v3.*;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DialogflowService {

  private final SessionsClient client;
  private final String project = "wexl-voice";
  private final String location = "asia-south1";
  private final String agent = "2d5cbcaf-b50d-450c-a62e-f47f15f8f050";

  public DialogflowService() throws IOException {
    Resource dialogFlowServiceAccount = new ClassPathResource("dialogflow-sa.json");
    GoogleCredentials creds =
        GoogleCredentials.fromStream(dialogFlowServiceAccount.getInputStream())
            .createScoped(List.of("https://www.googleapis.com/auth/cloud-platform"));

    SessionsSettings.Builder settingsBuilder = SessionsSettings.newBuilder();
    settingsBuilder.setCredentialsProvider(FixedCredentialsProvider.create(creds));
    settingsBuilder.setEndpoint("asia-south1-dialogflow.googleapis.com:443");
    SessionsSettings settings = settingsBuilder.build();

    this.client = SessionsClient.create(settings);
  }

  public String detectIntent(String sessionId, String text) {
    SessionName session =
        SessionName.ofProjectLocationAgentSessionName(project, location, agent, sessionId);

    TextInput input = TextInput.newBuilder().setText(text).build();
    QueryInput queryInput = QueryInput.newBuilder().setText(input).setLanguageCode("en").build();
    DetectIntentRequest request =
        DetectIntentRequest.newBuilder()
            .setSession(session.toString())
            .setQueryInput(queryInput)
            .build();
    DetectIntentResponse res = client.detectIntent(request);
    QueryResult queryResult = res.getQueryResult();
    log.info("Query text: " + queryResult.getText());
    log.info("Intent detected: " + queryResult.getIntent().getDisplayName());
    if (queryResult.getResponseMessagesCount() > 0
        && queryResult.getResponseMessages(0).hasText()) {
      return queryResult.getResponseMessages(0).getText().getText(0);
    } else {
      log.info("No text response found");
      return "I'm not sure how to respond to that.";
    }
  }
}
