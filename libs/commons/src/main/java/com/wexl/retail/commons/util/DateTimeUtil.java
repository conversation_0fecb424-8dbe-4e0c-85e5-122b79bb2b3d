package com.wexl.retail.commons.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.MutableDateTime;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DateTimeUtil {

  String dateFormat = "yyyyMMdd";

  static List<String> possiblePatterns =
      List.of(
          "yyyy-MM-dd",
          "dd-MM-yyyy",
          "dd/MM/yyyy",
          "MM/dd/yyyy",
          "yyyy/MM/dd",
          "dd MMM yyyy",
          "yyyyMMdd");

  public static Long toEpochSecond(LocalDateTime examConductedAt) {
    return examConductedAt.toEpochSecond(ZoneOffset.UTC);
  }

  public LocalDateTime convertEpochToIso8601Legacy(Long epochMillis) {
    var sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    sdf.setTimeZone(TimeZone.getDefault());
    return LocalDateTime.parse(sdf.format(new Date(epochMillis)));
  }

  public LocalDateTime convertEpochToIso8601(Long epochMillis) {
    var sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    sdf.setTimeZone(TimeZone.getTimeZone("GMT+5:30"));
    return LocalDateTime.parse(sdf.format(new Date(epochMillis)));
  }

  public OffsetTime convertEpochToOffsetTime(Long epochMillis) {
    var instant = Instant.ofEpochMilli(epochMillis);
    return OffsetTime.ofInstant(instant, TimeZone.getDefault().toZoneId());
  }

  public List<LocalDateTime> getDatesBetween(LocalDateTime startDate, LocalDateTime endDate) {
    List<LocalDateTime> requiredDates = new ArrayList<>();
    for (java.time.LocalDate i = startDate.toLocalDate();
        i.isBefore(endDate.toLocalDate().plusDays(1));
        i = i.plus(1, ChronoUnit.DAYS)) {
      requiredDates.add(i.atTime(LocalTime.now()));
    }
    return requiredDates;
  }

  @SneakyThrows
  public static Long convertIso8601ToEpoch(LocalDateTime localDateTime) {
    var timeInstant =
        Instant.from(
            ZonedDateTime.ofInstant(localDateTime, ZoneOffset.UTC, ZoneId.systemDefault()));
    return Timestamp.from(timeInstant).getTime();
  }

  // Only for Classrooms
  public static Long convertLocalDateTimeToIstEpochForClassroomOnly(LocalDateTime localDateTime) {
    ZoneId istZone = ZoneId.of("Asia/Kolkata");
    ZonedDateTime istZonedDateTime = ZonedDateTime.of(localDateTime, istZone);
    return istZonedDateTime.toInstant().toEpochMilli();
  }

  public Long convertTimeStampToLong(Timestamp timestamp) {
    final Instant instant = timestamp.toInstant();
    return instant.toEpochMilli();
  }

  public Timestamp convertEpochToTimestamp(Long epochMillis) {
    var instant = Instant.ofEpochMilli(epochMillis);
    return Timestamp.from(instant);
  }

  public String repositoryFriendlyDateFromEpoch(Long epochMillis) {
    Timestamp dateTimeStamp = convertEpochToTimestamp(epochMillis);
    return new SimpleDateFormat("yyyy-MM-dd").format(dateTimeStamp.getTime());
  }

  private long addFiveHoursThirtyMinutesToEpoch(Long epochMillis) {
    var fiveHoursThirtyMinutes = (((5 * 60) + 30) * 60) * 1000L;
    return (epochMillis + fiveHoursThirtyMinutes);
  }

  /**
   * converts "2021-12-24T04:10:19Z" to 2021-12-24 09:40:19.000000
   *
   * @param timestampString of format "2021-12-24T04:10:19Z"
   * @return Timestamp
   */
  public Timestamp convertStringToTimestamp(String timestampString) {
    return convertEpochToTimestamp(Instant.parse(timestampString).getEpochSecond() * 1000L);
  }

  public Instant subtractFifteenMinutesFromLocalDateTime(LocalDateTime localDateTime) {
    var getEpochInSeconds =
        addFiveHoursThirtyMinutesToEpoch(Timestamp.valueOf(localDateTime).getTime()) / 1000L;
    return Instant.ofEpochSecond(getEpochInSeconds - 900);
  }

  public DayOfWeek getDayOfWeekFromEpoch(Timestamp timestamp) {
    var dayOfWeek =
        (new SimpleDateFormat("EEEE")).format(timestamp.getTime()).toUpperCase(Locale.ROOT);
    return DayOfWeek.valueOf(dayOfWeek);
  }

  public Long anonymizeDateInEpoch(Long epochMillis) {
    var mutableDateTime = new MutableDateTime(epochMillis);
    mutableDateTime.setDate(1970, 1, 1);
    return mutableDateTime.getMillis();
  }

  public Long updateDate(Long epochMillis) {
    var mutableDateTime = new MutableDateTime(epochMillis);
    var date = new org.joda.time.LocalDate();
    mutableDateTime.setDate(date.getYear(), date.getMonthOfYear(), date.getDayOfMonth());
    return mutableDateTime.getMillis();
  }

  public Timestamp getUpdatedTimestamp(Long epochMillis) {
    var updatedEpoch = this.updateDate(epochMillis);
    return this.convertEpochToTimestamp(updatedEpoch);
  }

  public Date getCurrentDate() {
    return new java.sql.Date(System.currentTimeMillis());
  }

  public Integer convertToIntegerFormat(Date localDateTime) {
    var sdf = new SimpleDateFormat(dateFormat);
    return Integer.parseInt(sdf.format(localDateTime));
  }

  public List<java.time.LocalDate> getDates(
      java.time.LocalDate startDate, java.time.LocalDate endDate) {

    long numOfDaysBetween = ChronoUnit.DAYS.between(startDate, endDate);
    return IntStream.iterate(0, i -> i + 1)
        .limit(numOfDaysBetween + 1)
        .mapToObj(startDate::plusDays)
        .collect(Collectors.toList());
  }

  public Integer convertEpocToIntegerFormat(Long epochMillis) {
    Timestamp dateTimeStamp = convertEpochToTimestamp(epochMillis);
    var sdf = new SimpleDateFormat(dateFormat);
    return Integer.parseInt(sdf.format(dateTimeStamp));
  }

  public static long getEpochFromStringDate(String date) {
    java.time.LocalDate localDateFormat = java.time.LocalDate.parse(String.valueOf(date));
    LocalDateTime dateTime = LocalDateTime.parse(String.valueOf(localDateFormat.atStartOfDay()));
    return convertIso8601ToEpoch(dateTime);
  }

  public static java.time.LocalDate getLocalDateFromDate(Date dateToConvert) {
    return Instant.ofEpochMilli(dateToConvert.getTime())
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
  }

  public static java.time.LocalDateTime getLocalDateTimeFromDate(Date dateToConvert) {
    return Instant.ofEpochMilli(dateToConvert.getTime())
        .atZone(ZoneId.systemDefault())
        .toLocalDateTime();
  }

  public static int findYearByMonth(String monthName) {
    int month = Month.valueOf(monthName.trim()).getValue();
    int currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1;
    if (currentMonth < month) {
      return Calendar.getInstance().get(Calendar.YEAR) - 1;
    }
    return Calendar.getInstance().get(Calendar.YEAR);
  }

  public LocalDateTime updateToNewTime(LocalDateTime date, OffsetTime offsetTime) {
    return date.withMinute(offsetTime.getMinute()).withHour(offsetTime.getHour());
  }

  public long getOffSetTimeToEpoch(OffsetTime offsetTime) {
    OffsetDateTime offsetDateTime = offsetTime.atDate(java.time.LocalDate.now());
    return convertIso8601ToEpoch(offsetDateTime.toLocalDateTime());
  }

  public static List<Integer> getLast5Months() {
    List<Integer> requiredMonths = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      requiredMonths.add(java.time.LocalDate.now().minusMonths(i).getMonth().getValue());
    }
    return requiredMonths;
  }

  public static int findYearByMonthNumber(Integer month) {
    int currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1;
    if (currentMonth < month) {
      return Calendar.getInstance().get(Calendar.YEAR) - 1;
    }
    return Calendar.getInstance().get(Calendar.YEAR);
  }

  public int getDuration(LocalDateTime startTime, LocalDateTime endTime) {
    if (startTime == null || endTime == null) {
      return 0;
    }
    return (int) Duration.between(startTime, endTime).toMinutes();
  }

  public Long getAvailableExamDuration(LocalDateTime startTime, LocalDateTime endTime) {
    return Duration.between(startTime, endTime).getSeconds();
  }

  public OffsetDateTime convertEpochToOffsetDateTime(long epochMillis) {
    return OffsetDateTime.ofInstant(Instant.ofEpochMilli(epochMillis), ZoneOffset.UTC);
  }

  public String updateDateFormat(String date, String dateFormat) {
    LocalDateTime localDateTime = LocalDateTime.parse(date);
    DateTimeFormatter customFormatter = DateTimeFormatter.ofPattern(dateFormat);
    return localDateTime.format(customFormatter);
  }

  public String buildDate(Long date) {
    if (date == null) {
      return "";
    }
    Date epoch = new Date(date);
    SimpleDateFormat sdf = new SimpleDateFormat("dd MMM hh:mm a");
    return sdf.format(epoch);
  }

  public static long convertDateIdToEpoch(int dateId) {
    String dateString = String.valueOf(dateId);
    LocalDate localDate =
        LocalDate.parse(dateString, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
    LocalDateTime localDateTime = localDate.atStartOfDay();
    return localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
  }

  public static String convertEpochFormatToDate(Long epochDate, String format) {
    var sdf = new SimpleDateFormat(format);
    sdf.setTimeZone(TimeZone.getDefault());
    return sdf.format(new Date(epochDate * 1000));
  }

  public LocalTime convertEpochToLocalTime(Long epochTime) {
    Instant instant = Instant.ofEpochMilli(epochTime);
    return LocalDateTime.ofInstant(instant, ZoneId.systemDefault()).toLocalTime();
  }

  public Long convertLocalTimeToEpoch(LocalTime localTime) {
    LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), localTime);
    return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
  }

  public LocalTime convertGmtTimeToLocalTime(LocalTime localTime) {
    if (localTime == null) {
      return null;
    }

    ZoneId gmtZone = ZoneId.of("GMT");
    ZonedDateTime gmtDateTime = localTime.atDate(java.time.LocalDate.now()).atZone(gmtZone);

    ZoneId localZone = ZoneId.systemDefault();

    ZonedDateTime localDateTime = gmtDateTime.withZoneSameInstant(localZone);

    return localDateTime.toLocalTime();
  }

  public static Long parseDateToEpochMilli(String dateValue) {
    if (StringUtils.isEmpty(dateValue)) return null;
    for (String pattern : possiblePatterns) {
      try {
        return LocalDate.parse(dateValue, DateTimeFormatter.ofPattern(pattern))
            .atStartOfDay(ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli();
      } catch (Exception e) {
        log.debug("Failed to parse date {} with pattern {}", dateValue, pattern);
      }
    }
    return null;
  }
}
