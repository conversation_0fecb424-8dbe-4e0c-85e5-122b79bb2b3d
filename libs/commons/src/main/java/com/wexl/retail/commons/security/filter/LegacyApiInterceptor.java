package com.wexl.retail.commons.security.filter;

import com.wexl.retail.commons.security.annotation.LegacyApi;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@Slf4j
public class LegacyApiInterceptor implements HandlerInterceptor {
  @Value("${app.legacy-api.enabled:false}")
  private boolean legacyApisEnabled;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {

    if (handler instanceof HandlerMethod handlerMethod) {
      LegacyApi legacyApi = handlerMethod.getMethodAnnotation(LegacyApi.class);
      if (!legacyApisEnabled && legacyApi != null) {
        // Log or audit here if needed
        // Log legacy API usage with method information
        String methodName = handlerMethod.getMethod().getName();
        String className = handlerMethod.getBeanType().getSimpleName();
        String requestPath = request.getRequestURI();
        String httpMethod = request.getMethod();

        log.info(
            "Legacy API called: class={}, method={}, path={}, httpMethod={}, reason={}",
            className,
            methodName,
            requestPath,
            httpMethod);

        response.sendError(HttpStatus.GONE.value(), "Legacy API is disabled");
        return false;
      }
    }
    return true;
  }
}
