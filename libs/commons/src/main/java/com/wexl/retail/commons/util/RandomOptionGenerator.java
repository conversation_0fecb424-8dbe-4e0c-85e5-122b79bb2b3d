package com.wexl.retail.commons.util;

import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RandomOptionGenerator {
  private final Map<String, List<String>> permutationsMap;

  public RandomOptionGenerator() {
    permutationsMap = new HashMap<>();
    permutationsMap.put("Perm1", Arrays.asList("A", "B", "D", "C"));
    permutationsMap.put("Perm2", Arrays.asList("A", "B", "C", "D"));
    permutationsMap.put("Perm3", Arrays.asList("A", "D", "B", "C"));
    permutationsMap.put("Perm4", Arrays.asList("A", "D", "C", "B"));
    permutationsMap.put("Perm5", Arrays.asList("A", "C", "B", "D"));
    permutationsMap.put("Perm6", Arrays.asList("A", "C", "D", "B"));
    permutationsMap.put("Perm7", Arrays.asList("B", "A", "D", "C"));
    permutationsMap.put("Perm8", Arrays.asList("B", "A", "C", "D"));
    permutationsMap.put("Perm9", Arrays.asList("B", "D", "A", "C"));
    permutationsMap.put("Perm10", Arrays.asList("B", "D", "C", "A"));
    permutationsMap.put("Perm11", Arrays.asList("B", "C", "A", "D"));
    permutationsMap.put("Perm12", Arrays.asList("B", "C", "D", "A"));
    permutationsMap.put("Perm13", Arrays.asList("D", "A", "B", "C"));
    permutationsMap.put("Perm14", Arrays.asList("D", "A", "C", "B"));
    permutationsMap.put("Perm15", Arrays.asList("D", "B", "A", "C"));
    permutationsMap.put("Perm16", Arrays.asList("D", "B", "C", "A"));
    permutationsMap.put("Perm17", Arrays.asList("D", "C", "A", "B"));
    permutationsMap.put("Perm18", Arrays.asList("D", "C", "B", "A"));
    permutationsMap.put("Perm19", Arrays.asList("C", "A", "B", "D"));
    permutationsMap.put("Perm20", Arrays.asList("C", "A", "D", "B"));
    permutationsMap.put("Perm21", Arrays.asList("C", "B", "A", "D"));
    permutationsMap.put("Perm22", Arrays.asList("C", "B", "D", "A"));
    permutationsMap.put("Perm23", Arrays.asList("C", "D", "A", "B"));
    permutationsMap.put("Perm24", Arrays.asList("C", "D", "B", "A"));
  }

  public Map<String, List<String>> randomOptionPicker() {
    List<String> keys = new ArrayList<>(permutationsMap.keySet());
    String randomKey = keys.get(new Random().nextInt(keys.size()));
    List<String> randomPermutation = permutationsMap.get(randomKey);
    Map<String, List<String>> result = new HashMap<>();
    result.put(randomKey, randomPermutation);
    return result;
  }

  public List<String> getPermutationByKey(String key) {
    return permutationsMap.get(key);
  }
}
