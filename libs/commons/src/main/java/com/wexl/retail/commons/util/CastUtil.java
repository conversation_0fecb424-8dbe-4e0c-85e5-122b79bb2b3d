package com.wexl.retail.commons.util;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class CastUtil {

  private CastUtil() {
    throw new IllegalStateException("Utility class");
  }

  public static <T> List<T> castList(Object obj, Class<T> clazz) {
    return (obj instanceof List<?> l)
        ? l.stream().map(clazz::cast).collect(Collectors.toList())
        : Collections.emptyList();
  }
}
