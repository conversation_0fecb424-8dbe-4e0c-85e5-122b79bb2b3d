package com.wexl.retail.storage;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import org.springframework.core.io.InputStreamSource;

public interface StorageService {
  String generatePreSignedUrlForFetch(String objKey);

  String generatePreSignedUrlForUpload(String objKey);

  String generatePreSignedUrlForFetchWithMaxExpiry(String objKey, long days);

  String generatePreSignedUrlForFetchImage(String objKey);

  String generatePreSignedUrlForUpload(
      String bucketName, String objKey, Map<String, String> metadata);

  String generatePreSignedUrlForUpload(String objKey, Map<String, String> metadata);

  boolean isFileAvailable(String objKey);

  boolean isFileAvailableInBucket(String betReportCardPath, String bucketName);

  void uploadFile(InputStreamSource multipartFile, String filePath);

  void uploadFile(byte[] content, String filePath, String contentType);

  void uploadFile(InputStream inputStream, String filePath, int size);

  InputStream getInputStream(String filePath);

  String generatePreSignedUrlForCourseContentUpload(String objKey);

  File downloadFile(String key) throws IOException;

  File downloadFile(String key, String bucketName) throws IOException;

  <T> T downloadFile(String key, Class<T> cls) throws IOException;

  void uploadDirectory(File srcDir, String keyPrefix);

  void uploadDirectory(String bucketName, File srcDir, String keyPrefix);

  void writeFile(
      String bucketName, String contentType, String key, byte[] array, boolean makePublic);

  String generatePreSignedUrlForFetch(String objKey, String buckName);

  void copyFile(
      String sourceBucket, String sourceKey, String destinationBucket, String destinationKey);

  void uploadDirectoryInBulk(File files, String keyPrefix);

  void uploadFile(
      String bucketName, String contentType, String key, byte[] array, boolean makePublic);
}
