package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.Concession;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ConcessionRepository extends JpaRepository<Concession, Long> {
  List<Concession> findAllByOrgSlug(String orgSlug);

  Optional<Concession> findByIdAndOrgSlug(UUID concessionId, String orgSlug);
}
