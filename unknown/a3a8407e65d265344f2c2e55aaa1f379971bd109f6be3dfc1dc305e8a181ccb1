package com.wexl.retail.test.schedule.repository;

import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.dto.AdmissionTestStudentInfo;
import com.wexl.retail.test.schedule.service.ScheduledTestData;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.dto.SchoolTestQueryResponse;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduleTestRepository extends JpaRepository<ScheduleTest, Long> {
  List<ScheduleTest> findAllByParent(ScheduleTest parent);

  @Query(
      value =
          """
          SELECT ts FROM ScheduleTest ts LEFT JOIN FETCH ts.testDefinition td\s
          WHERE ts.orgSlug=:orgSlug\s
          AND td.active=TRUE AND ts.status='active'\s
          AND td.subjectSlug in (:teachingSubjects)\s
          AND ts.type in (:types)
      """)
  List<ScheduleTest> getScheduledTestsOrg(
      String orgSlug, List<String> teachingSubjects, List<String> types, Pageable pageable);

  @Query(
      value =
          """
          SELECT ts.* FROM test_schedule ts\s
          LEFT JOIN test_definitions td on td.id = ts.test_definition_id
          WHERE ts.org_slug = :orgSlug
          AND td.active=TRUE AND ts.status='active'
          AND td.subject_slug in (:teachingSubjects)\s
          AND (cast((:boardSlug) as varChar) is null or td.board_slug =(:boardSlug))
          AND (cast((:subjectSlug) as varChar) is null or subject_slug =(:subjectSlug))
          AND (cast((:gradeSlug) as varChar) is null or td.grade_slug =(:gradeSlug))
          AND ts.type in (:types)
      """,
      nativeQuery = true)
  List<ScheduleTest> getScheduledTestsByBoardSubjectAndGradeAndOrg(
      String orgSlug,
      String boardSlug,
      String subjectSlug,
      String gradeSlug,
      List<String> teachingSubjects,
      List<String> types,
      Pageable pageable);

  @Query(
      value =
          """
          select count(*) from test_schedule ts where ts.status='active'\
          and ts.test_definition_id =:testDefinitionId\
          """,
      nativeQuery = true)
  long countAllTestSchedulesForDefinition(long testDefinitionId);

  @Query(
      value =
          """
          select ts.* from test_schedule ts join test_definitions td \
          on td.id=ts.test_definition_id where td.active=true and ts.status='active' \
          and ts.test_definition_id =:testDefinitionId order by ts.updated_at desc\
          """,
      nativeQuery = true)
  List<ScheduleTest> findByStatusAndDefinationId(long testDefinitionId);

  @Query(
      value = "select id from test_schedule where Parent_test_schedule_id in (:testScheduleId)",
      nativeQuery = true)
  List<Long> findChildTestScheduleIds(long testScheduleId);

  @Query(
      value =
          """
                  select ts.id as id,u.first_name as teacherName,td.test_name as testName,ts.start_date as scheduledDate,td.subject_slug as subject,td.grade_slug as gradeSlug,td.organization as organization,o."name" as institution,
                  coalesce((td.total_marks), 0) as totalMarks,
                  coalesce((max(e.marks_scored)), 0) as highestMarksScored,
                  coalesce((min(marks_scored)), 0) as leastMarksScored,
                  (select distinct Count(*) from test_schedule_student tss3 where  tss3.schedule_test_id = tss.schedule_test_id  ) as Assigned,
                  (select count(*) from test_schedule_student tss1 where  tss1.schedule_test_id = tss.schedule_test_id  and tss1.status = 'COMPLETED') as Attempted,
                  (select count(*) from test_schedule_student tss2 where  tss2.schedule_test_id  = tss.schedule_test_id  and tss2.status = 'PENDING') as NotAttempted
                  from  test_schedule ts
                  join test_definitions  td on ts.test_definition_id = td.id
                  join test_schedule_student tss on ts.id = tss.schedule_test_id
                  join orgs o on o.slug =ts.org_slug\s
                  join users u on u.id = ts.teacher_id
                  left join exams e on ts.id = e.schedule_test_id
                  where  td.grade_slug in (:gradeList)
                  and to_char(ts.created_at , 'YYYY-MM-DD')  >= :fromDate
                  and  ts.org_slug in (:orgs)
                  and  (cast((:subject) as varChar) is null
                  or (td.subject_slug) in (:subject))
                  group by teacherName,testName,scheduledDate,subject,gradeSlug,totalMarks,Assigned,Attempted,NotAttempted, ts.id,td.organization,o."name"\s""",
      nativeQuery = true)
  List<ScheduledTestData> getScheduledTestsByInstitute(
      List<String> orgs, String fromDate, List<String> subject, List<String> gradeList);

  @Query(
      value =
          """
                      select ea.question_uuid as questionUuid,
                      sum(case when ea.is_correct = true then 0
                      when ea.is_correct = false then 1 end) as incorrectAnsweredCount
                      from exam_answers ea
                      inner join exams e on ea.exam_id = e.id  where e.schedule_test_id in (:scheduledIds)
                      group by ea.question_uuid
                      order by incorrectAnsweredCount desc

                      """,
      nativeQuery = true)
  List<QuestionDetails> getQuestionDetailsByScheduleTestId(List<Long> scheduledIds);

  @Query(
      value =
          """
                          with test_definition_chapter as (
                          select  jsonb_array_elements(metadata -> 'chapters')->>'slug' as "chapter",* from test_definitions
                          where organization=:orgSlug and deleted_at is null
                          and type in('SCHOOL_TEST'))

                          select id,test_name as testName from test_definition_chapter
                          where chapter=:chapterSlug
                          order by id desc limit :limit
                                  """,
      nativeQuery = true)
  List<SchoolTestQueryResponse> getAllSchoolTest(String orgSlug, String chapterSlug, int limit);

  long countByTestDefinition(TestDefinition testDefinition);

  @Query(
      value =
          """
                  SELECT ts.* FROM test_Schedule ts LEFT JOIN  Test_definitions td on ts.test_definition_id = td.id
                   WHERE ts.org_slug=:orgSlug
                   AND td.active=TRUE AND ts.status='active'
                  AND td.published_at is not null and td.type = 'MOCK_TEST'
                  AND (cast((:gradeSlug) as varChar) is null or (td.grade_slug) in (:gradeSlug))
                  order by ts.created_at desc limit 100
                  """,
      nativeQuery = true)
  List<ScheduleTest> findMockTestsByOrgSlug(String orgSlug, String gradeSlug);

  @Query(
      value =
          """
                                  SELECT * FROM test_Schedule
                                   WHERE org_slug=:orgSlug AND status='active'
                                  """,
      nativeQuery = true)
  List<ScheduleTest> findByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                      select distinct  ts.*
                                  from test_definitions td
                                  inner join test_schedule ts on td.id = ts.test_definition_id
                                  inner join test_schedule_student tss on tss.schedule_test_id = ts.id
                                  inner join users u on u.id  = tss.student_id
                                  inner join students s on s.user_id = u.id
                                  inner join sections sec on sec.id = s.section_id
                                  where td.deleted_at is null and td.active = true and td.teacher_id= :teacherId
                                  and  td.organization = :orgSlug and to_char(ts.created_at,'yyyy-MM-dd') >= :fromDate  and
                                  to_char(ts.created_at,'yyyy-MM-dd') <= :toDate and
                                  sec.id in (:sections) order by ts.created_at desc""",
      nativeQuery = true)
  List<ScheduleTest> getScheduleTestsByTeacherAndDate(
      long teacherId, String fromDate, String toDate, String orgSlug, List<Long> sections);

  @Query(
      value =
          """
                              select distinct  ts.*
                                          from test_definitions td
                                          inner join test_schedule ts on td.id = ts.test_definition_id
                                          inner join test_schedule_student tss on tss.schedule_test_id = ts.id
                                          inner join users u on u.id  = tss.student_id
                                          inner join students s on s.user_id = u.id
                                          inner join sections sec on sec.id = s.section_id
                                          where td.deleted_at is null and td.active = true and td.teacher_id= :teacherId
                                          and  td.organization = :orgSlug and
                                          sec.id in (:sections) order by ts.created_at desc""",
      nativeQuery = true)
  List<ScheduleTest> getScheduleTestsByTeacher(long teacherId, String orgSlug, List<Long> sections);

  @Query(
      value =
          """

                                   SELECT  COUNT(distinct ts.*)
                                   FROM test_schedule ts
                                   JOIN test_schedule_student tss ON ts.id = tss.schedule_test_id
                                   JOIN orgs o ON ts.org_slug = o.slug
                                   WHERE tss.image_path IS NOT null and o.slug = :orgSlug
                                  """,
      nativeQuery = true)
  Long getDacCountByOrg(String orgSlug);

  @Query(
      value =
          """
                                  select   COUNT(distinct m.*) from mlp m where org_slug =:orgSlug
                                  """,
      nativeQuery = true)
  Long getMlpCountByOrg(String orgSlug);

  @Query(
      value =
          """
                  select  COUNT(distinct ts.*)  from test_schedule ts
                           join test_definitions td  on ts.test_definition_id  = td.id
                           where ts.org_slug  = :orgSlug and td.type in ('MOCK_TEST','SCHOOL_TEST')
                                  """,
      nativeQuery = true)
  Long getOnlineTestCountByOrg(String orgSlug);

  List<ScheduleTest> findByOrgSlugInAndIsDacIsTrue(List<String> orgSlug);

  List<ScheduleTest> findByOrgSlugAndMetadataIsNotNull(String orgSlug);

  List<ScheduleTest> findAllByIdInAndDacMigrationStatus(List<Long> scheduleTestId, String name);

  List<ScheduleTest> findAllByIdIn(List<Long> scheduleTestId);

  Optional<ScheduleTest> findTop1ByTestDefinition(TestDefinition testDefinition);

  @Query(
      value =
          """
                          select distinct ts.* from test_schedule ts
                          join test_definitions td on td.id = ts.test_definition_id
                          join test_schedule_student tss on ts.id = tss.schedule_test_id
                          join exams e on e.schedule_test_id = ts.id
                          where td.organization = :orgSlug and td.category = :category
                          and tss.student_id = :studentUserId and tss.status = :status and e.corrected = true""",
      nativeQuery = true)
  List<ScheduleTest> getStudentBetTests(
      String orgSlug, Long studentUserId, TestCategory category, String status);

  @Query(
      value =
          """
                  select ts.* from test_schedule ts
                  join test_definitions td on td.id  = ts.test_definition_id
                  join test_schedule_student tss on tss.schedule_test_id = ts.id
                  where td.test_name ilike :testName and ts.org_slug = :orgSlug and td.board_slug = :boardSlug
                  and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug)) and tss.status != 'COMPLETED'
                  and (cast((:userId) as varChar) is null or tss.student_id in (:userId))
                  order by td.id desc
                  """,
      nativeQuery = true)
  List<ScheduleTest> getSchedulesByOrgAndBoard(
      String orgSlug, String boardSlug, String testName, String gradeSlug, Long userId);

  @Query(
      value =
          """
                          select ts.* from test_schedule ts
                          join test_definitions td on td.id  = ts.test_definition_id
                          where td.test_name ilike :testName and ts.org_slug = :orgSlug and td.board_slug = :boardSlug
                          and (cast((:gradeSlug) as varChar) is null or td.grade_slug in (:gradeSlug))
                          order by td.id desc
                          """,
      nativeQuery = true)
  List<ScheduleTest> getNewSchedulesByOrgAndBoard(
      String orgSlug, String boardSlug, String testName, String gradeSlug);

  @Query(
      value =
          """
                  select at."name" as name,at.grade_name as grade from test_schedule ts
                  join admission_tests at on at.test_schedule_id = ts.id
                  where at.test_schedule_id = :testScheduleId
                  """,
      nativeQuery = true)
  Optional<AdmissionTestStudentInfo> getAdmissionTestStudentInfo(Long testScheduleId);
}
