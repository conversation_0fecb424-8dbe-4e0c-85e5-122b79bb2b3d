package com.wexl.retail.test.school.service;

import static com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST;
import static com.wexl.retail.util.Constants.COMMA_SEPERATOR;
import static java.lang.String.format;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toList;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import com.wexl.ai.EnglishTutor;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.*;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.liveworksheet.service.LiveWorkSheetService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.metrics.dto.QuestionsAnalytics;
import com.wexl.retail.metrics.handler.RangeByExam;
import com.wexl.retail.metrics.handler.TestDetailsInterface;
import com.wexl.retail.model.GenericResponse;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.qpgen.model.BluePrintSections;
import com.wexl.retail.qpgen.model.InternalChoice;
import com.wexl.retail.qpgen.model.QPGenPro;
import com.wexl.retail.qpgen.model.QpGen;
import com.wexl.retail.qpgen.repository.*;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.competitive.TestDefinitionValidatorProcessor;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.domain.TestDefinitionMetadata.VideoExplanationMetadata;
import com.wexl.retail.test.school.dto.*;
import com.wexl.retail.test.school.dto.QuestionDto.Answer;
import com.wexl.retail.test.school.dto.QuestionDto.TestDefinitionSectionResponse;
import com.wexl.retail.test.school.repository.*;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiContentHelper;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Slf4j
@Service
@RequiredArgsConstructor
public class TestDefinitionService {

  private static final String SCHEDULED = "SCHEDULED";
  private static final String NOT_SCHEDULED = "NOT-SCHEDULED";
  private static final String VIMEO_URL_PREFIX = "https://player.vimeo.com";
  private static final String ERROR_READ_QUESTIONS = "Error.read.questions";
  private static final String SERVER_ERROR = "error.serverError";
  private final AuthService authService;
  private final StrapiService strapiService;
  private final ContentService contentService;
  private final StorageService storageService;
  private final ScheduleTestService scheduleTestService;
  private final StrapiContentHelper strapiContentHelper;
  private final ScheduleTestRepository scheduleTestRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final QpGenRepository qpGenRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final DateTimeUtil dateTimeUtil;
  private final UserRepository userRepository;
  private final TestDefinitionSectionRepository testDefinitionSectionRepository;
  private final TestQuestionRepository testQuestionRepository;
  private final UserService userService;
  private final LiveWorkSheetService liveWorkSheetService;
  private final ValidationUtils validationUtils;
  private final TestDefinitionValidatorProcessor testDefinitionValidatorProcessor;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final InternalChoiceRepository internalChoiceRepository;
  private final QpGenProRepository qpGenProRepository;
  private final BluePrintSectionRepository bluePrintSectionRepository;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final TestEnrichmentRepository testEnrichmentRepository;
  private final UserRoleHelper userRoleHelper;
  private final EnglishTutor englishTutor;
  private final SpringTemplateEngine templateEngine;

  private static final SecureRandom rand = new SecureRandom();

  @Value("${app.contentToken}")
  private String contentBearerToken;

  @Transactional
  public TestDefinition createTestDefinition(
      TestDefinitionRequest testDefinitionRequest, String bearerToken, String organization) {
    log.debug(
        "test definition request received to save test questions data:{} ", testDefinitionRequest);
    var testDefinitionEntity = new TestDefinition();
    testDefinitionEntity.setBoardSlug(testDefinitionRequest.getBoardSlug());
    testDefinitionEntity.setTotalMarks(getTotalMarks(testDefinitionRequest.getQuestions()));
    testDefinitionEntity.setIsAutoEnabled(testDefinitionRequest.getIsAutoEnabled());
    testDefinitionEntity.setNoOfQuestions(testDefinitionRequest.getNoOfQuestions());
    testDefinitionEntity.setGradeSlug(testDefinitionRequest.getGradeSlug());
    testDefinitionEntity.setSubjectSlug(testDefinitionRequest.getSubjectSlug());
    testDefinitionEntity.setTeacher(authService.getTeacherDetails());
    testDefinitionEntity.setTestName(testDefinitionRequest.getTestName());
    testDefinitionEntity.setCategory(inferCategory(testDefinitionRequest.getTestName()));
    testDefinitionEntity.setMessage(testDefinitionRequest.getMessage());
    testDefinitionEntity.setComplexityLevelSlug(testDefinitionRequest.getComplexityLevelSlug());
    testDefinitionEntity.setActive(testDefinitionRequest.getActive());
    testDefinitionEntity.setOrganization(organization);
    testDefinitionEntity.setType(massageTestType(testDefinitionRequest.getTestType()));
    testDefinitionEntity.setQuestionPath(testDefinitionRequest.getQuestionPath());
    testDefinitionEntity.setSolutionPath(testDefinitionRequest.getSolutionPath());
    testDefinitionEntity.setInstructions(testDefinitionRequest.getInstructions());
    if (!testDefinitionEntity.getType().equals(TestType.MOCK_TEST)) {
      testDefinitionEntity.setPublishedAt(Timestamp.from(Instant.now()));
    }
    final TestDefinitionSection testDefinitionSection =
        createTestDefinitionSection(testDefinitionEntity, testDefinitionRequest.getQuestions());
    testDefinitionEntity.setTestDefinitionSections(
        testDefinitionSection == null ? new ArrayList<>() : List.of(testDefinitionSection));
    testDefinitionEntity.setMetadata(
        generateChapterMetadata(testDefinitionSection, testDefinitionRequest));

    if (TestType.SCHOOL_TEST.equals(testDefinitionEntity.getType())) {
      List<QuestionResponse> questionResponses =
          getShuffledQuestions(
              getTestDefQuestionsFromContentService(
                  bearerToken,
                  getTestQuestionsForTestDefinition(testDefinitionEntity),
                  testDefinitionEntity.getType()));
      testDefinitionEntity.setQuestionV1(
          uploadTestResponses(testDefinitionEntity, questionResponses));
    }
    testDefinitionEntity.setTheme(testDefinitionRequest.getTheme());
    var testDefinition = testDefinitionRepository.save(testDefinitionEntity);
    if (testDefinitionEntity.getType().equals(TestType.LIVE_WORKSHEET)) {
      createLiveWorkSheet(testDefinitionRequest, bearerToken, testDefinition);
    }
    return testDefinition;
  }

  private void createLiveWorkSheet(
      TestDefinitionRequest testDefinitionRequest,
      String bearerToken,
      TestDefinition testDefinition) {
    liveWorkSheetService.createLiveWorkSheet(testDefinitionRequest, bearerToken);
    liveWorkSheetService.uploadLiveWorksheetResponseInS3(
        testDefinition,
        getQuestionsByUuid(
            bearerToken,
            testDefinition.getTestDefinitionSections().get(0).getTestQuestions(),
            testDefinition.getOrganization()));
  }

  private Integer getTotalMarks(List<TestQuestionRequest> questions) {

    return Objects.nonNull(questions) && !questions.isEmpty()
        ? questions.stream()
            .filter(question -> Objects.nonNull(question.getMarks()))
            .map(TestQuestionRequest::getMarks)
            .reduce(Integer::sum)
            .orElse(questions.size())
        : null;
  }

  private TestDefinitionSection createTestDefinitionSection(
      TestDefinition testDefinition, List<TestQuestionRequest> questions) {
    if (ObjectUtils.isEmpty(questions)) {
      return null;
    }
    final TestDefinitionSection defaultTestDefinitionSection =
        createDefaultTestDefinitionSection(testDefinition, questions.size());
    defaultTestDefinitionSection.setTestQuestions(
        getTestQuestionsFromTestQuestionRequest(defaultTestDefinitionSection, questions));
    return defaultTestDefinitionSection;
  }

  private List<TestQuestion> getTestQuestionsFromTestQuestionRequest(
      TestDefinitionSection defaultTestDefinitionSection, List<TestQuestionRequest> questions) {
    if (ObjectUtils.isEmpty(questions)) {
      return new ArrayList<>();
    }
    return questions.stream()
        .map(
            questionRequest ->
                TestQuestion.builder()
                    .questionUuid(getQuestionUuid(questionRequest))
                    .marks(
                        Objects.nonNull(questionRequest.getMarks())
                            ? questionRequest.getMarks()
                            : 1)
                    .type(questionRequest.getType())
                    .chapterSlug(questionRequest.getChapterSlug())
                    .subtopicSlug(questionRequest.getSubtopicSlug())
                    .subjectSlug(questionRequest.getSubjectSlug())
                    .testDefinitionSection(defaultTestDefinitionSection)
                    .build())
        .toList();
  }

  private String getQuestionUuid(TestQuestionRequest questionRequest) {
    if (Objects.nonNull(questionRequest.getQuestionUuid())) {
      return questionRequest.getQuestionUuid();
    }
    var uuid = UUID.randomUUID().toString();
    questionRequest.setQuestionUuid(uuid);
    return uuid;
  }

  private TestType massageTestType(TestType testType) {
    if (Objects.isNull(testType)) {
      return TestType.SCHOOL_TEST;
    }
    return testType;
  }

  public List<String> uploadTestResponses(
      TestDefinition testDefinition, List<QuestionResponse> questionResponses) {
    ObjectMapper objectMapper = new ObjectMapper();

    List<String> signedUrls = new ArrayList<>();
    DateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmssSSS");
    String simpleDate = sdf.format(new Date());

    for (int i = 0; i < questionResponses.size(); i++) {
      try {
        var contentAsBytes = objectMapper.writeValueAsBytes(questionResponses.get(i));
        final String objectKey =
            format(
                Constants.TEST_DEFINITION_QUESTIONS_STRING_PATH,
                testDefinition.getOrganization(),
                simpleDate,
                i);
        storageService.uploadFile(contentAsBytes, objectKey, MediaType.APPLICATION_JSON_VALUE);
        signedUrls.add(objectKey);
      } catch (JsonProcessingException e) {
        throw new ApiException(INVALID_REQUEST, e.getMessage(), e);
      }
    }
    return signedUrls;
  }

  private List<QuestionResponse> getShuffledQuestions(List<Question> questionsAndOptions) {
    List<QuestionResponse> questionResponses = new ArrayList<>();

    IntStream.rangeClosed(0, 3)
        .forEach(
            round -> {
              List<Question> clonedQuestions = new ArrayList<>(questionsAndOptions);
              Collections.shuffle(clonedQuestions, new SecureRandom());
              questionResponses.add(QuestionResponse.builder().data(clonedQuestions).build());
            });
    questionResponses.addFirst(QuestionResponse.builder().data(questionsAndOptions).build());
    return questionResponses;
  }

  public List<TestDefinitionResponse> getTestDefinitionsCreatedByTeacher(
      User teacherUser, int limit) {
    var orgSlug = authService.getTeacherDetails().getOrganization();

    Set<TestDefinitionResponse> testDefinitionResponses = new HashSet<>();
    buildTestDefinitionResponses(
        testDefinitionResponses,
        SCHEDULED,
        testDefinitionRepository.getTeacherScheduledTests(orgSlug, teacherUser.getId(), limit));
    buildTestDefinitionResponses(
        testDefinitionResponses,
        NOT_SCHEDULED,
        testDefinitionRepository.getTeacherTestsThatAreNotScheduled(
            orgSlug, teacherUser.getId(), limit));

    return sortTestDefinitions(testDefinitionResponses, limit);
  }

  @SneakyThrows
  public List<TestDefinitionResponse> getAllTestDefinitions(
      User teacherUser, int limit, String testName) {
    var teacher = teacherUser.getTeacherInfo();
    List<String> subjects = new ArrayList<>();
    List<String> gradeSlug = new ArrayList<>();
    List<TeacherSubjects> teacherSubjects = new ArrayList<>();
    if (teacher.getRoleTemplate().getTemplate().equals(AppTemplate.TEACHER)) {
      teacherSubjects.addAll(teacherSubjectsRepository.findByTeacher(teacherUser.getAuthUserId()));
      if (teacherSubjects.isEmpty()) {
        return Collections.emptyList();
      }
      subjects.addAll(
          teacherSubjects.stream().map(TeacherSubjects::getSubject).distinct().toList());
      gradeSlug.addAll(
          teacherSubjects.stream().map(TeacherSubjects::getSection).toList().stream()
              .map(Section::getGradeSlug)
              .distinct()
              .toList());
    }

    var orgSlug = teacherUser.getOrganization();

    List<TestDefinition> teacherTestSchedules = new ArrayList<>();
    List<TestDefinition> scheduledTestDef;
    if (testName != null) {
      teacherTestSchedules.addAll(
          testDefinitionRepository
              .getTeacherAndOrgAndTestNameAndDeletedAtIsNullOrderByCreatedAtDesc(
                  teacherUser.getId(), orgSlug, testName));
      teacherTestSchedules.addAll(
          testDefinitionRepository.getTeacherTestsThatAreNotScheduled(
              orgSlug, limit, gradeSlug, subjects, testName));

      scheduledTestDef =
          testDefinitionRepository.getTeacherScheduledTests(
              orgSlug, teacherUser.getId(), limit, testName);
    } else {
      teacherTestSchedules.addAll(
          getTestSchedules(teacherUser, orgSlug, gradeSlug, subjects, limit));
      scheduledTestDef =
          teacherTestSchedules.stream()
              .filter(x -> x.getTeacher().getId() == teacherUser.getId())
              .toList();
    }
    Set<TestDefinition> filterTestDef =
        teacherTestSchedules.stream()
            .filter(td -> !scheduledTestDef.contains(td))
            .collect(Collectors.toSet());
    Set<TestDefinitionResponse> testDefinitionResponses = new HashSet<>();
    buildTestDefinitionResponses(
        testDefinitionResponses, NOT_SCHEDULED, new ArrayList<>(filterTestDef));

    buildTestDefinitionResponses(testDefinitionResponses, SCHEDULED, scheduledTestDef);

    if (!userRoleHelper.isOrgAdmin(teacherUser)) {
      Set<TestDefinitionResponse> finalTestDefinitions = new HashSet<>();

      teacherSubjects.forEach(
          subject ->
              finalTestDefinitions.addAll(
                  testDefinitionResponses.stream()
                      .filter(
                          x ->
                              x.getBoardSlug().equals(subject.getBoard())
                                  && x.getGradeSlug().equals(subject.getSection().getGradeSlug())
                                  && x.getSubjectSlug().equals(subject.getSubject()))
                      .toList()));

      return sortTestDefinitions(finalTestDefinitions, limit);
    }
    return sortTestDefinitions(testDefinitionResponses, limit);
  }

  private List<TestDefinition> getTestSchedules(
      User teacherUser, String orgSlug, List<String> gradeSlug, List<String> subjects, int limit) {
    if (Boolean.TRUE.equals(AuthUtil.isOrgAdmin(teacherUser))) {
      return testDefinitionRepository.findAllByOrganizationAndDeletedAtIsNullOrderByCreatedAtDesc(
          orgSlug, PageRequest.of(0, limit));
    } else {
      return testDefinitionRepository.getTestsByTeacherSubjects(
          orgSlug, limit, gradeSlug, subjects);
    }
  }

  private LinkedList<TestDefinitionResponse> sortTestDefinitions(
      Set<TestDefinitionResponse> testDefinitionResponses, int limit) {
    return testDefinitionResponses.stream()
        .map(
            response -> {
              if (response.getType() == null) {
                response.setType(TestType.SCHOOL_TEST);
              }
              return response;
            })
        .sorted(Comparator.comparing(TestDefinitionResponse::getCreatedAtDate).reversed())
        .limit(limit)
        .collect(toCollection(LinkedList::new));
  }

  public TestDefinitionResponse getQuestionsPreconfigured(
      TestDefinition testDefinition, int questionSet) {
    if (TestType.MOCK_TEST.equals(testDefinition.getType())) {
      return getMockTestQuestionsPreconfigured(testDefinition, questionSet);
    }
    if (testDefinition.getQuestionV1() == null || testDefinition.getQuestionV1().isEmpty()) {
      throw new ApiException(INVALID_REQUEST, SERVER_ERROR);
    }
    final String s3Path = testDefinition.getQuestionV1().get(questionSet);
    try {
      var questionResponse = storageService.downloadFile(s3Path, QuestionResponse.class);
      return buildTestDefinitionResponse(testDefinition, questionResponse.getData());

    } catch (IOException e) {
      throw new ApiException(INVALID_REQUEST, ERROR_READ_QUESTIONS);
    }
  }

  private TestDefinitionResponse buildTestDefinitionResponse(
      TestDefinition testDefinition, List<Question> questions) {
    var response =
        TestDefinitionResponse.builder()
            .testDefinitionId(testDefinition.getId())
            .subjectName(testDefinition.getSubjectSlug())
            .gradeName(testDefinition.getGradeSlug())
            .questionsAndOptions(questions)
            .build();

    response.setTestName(testDefinition.getTestName());
    response.setNoOfQuestions(testDefinition.getNoOfQuestions());

    return updatePreviewUrls(testDefinition, response);
  }

  public TestDefinitionResponse getMockTestQuestionsPreconfigured(
      TestDefinition testDefinition, int questionSet) {
    if (testDefinition.getQuestionV2() == null || testDefinition.getQuestionV2().isEmpty()) {
      throw new ApiException(INVALID_REQUEST, SERVER_ERROR);
    }
    final String s3Path = testDefinition.getQuestionV2().get(questionSet);
    try {
      var questionResponse =
          storageService.downloadFile(s3Path, QuestionDto.QuestionResponse.class);
      List<QuestionDto.Question> questionList =
          questionResponse.testDefinitionSectionResponses().stream()
              .map(TestDefinitionSectionResponse::questions)
              .flatMap(List::stream)
              .toList();
      var questions = transformQuestions(questionList);
      return buildTestDefinitionResponse(testDefinition, questions);

    } catch (IOException e) {
      throw new ApiException(INVALID_REQUEST, ERROR_READ_QUESTIONS);
    }
  }

  private List<Question> transformQuestions(List<QuestionDto.Question> questionList) {
    if (questionList == null || questionList.isEmpty()) {
      return Collections.emptyList();
    }
    List<Question> questions = new ArrayList<>();
    questionList.forEach(
        question ->
            questions.add(
                contentService.getQuestionByUuid(
                    contentBearerToken, question.type(), question.uuid())));
    return questions;
  }

  public static int randomNumber() {
    return rand.nextInt(0, 5);
  }

  @SneakyThrows
  public TestDefinitionResponse getTestDefinitionById(String bearerToken, long testDefId) {
    var testDefinition = scheduleTestService.findTestDefinitionById(testDefId);
    String chapterName =
        (testDefinition.getMetadata().getChapters() != null
                && !testDefinition.getMetadata().getChapters().isEmpty())
            ? testDefinition.getMetadata().getChapters().get(0).getName()
            : null;
    LiveWorkSheetDto.LiveWorkSheetQuestionResponse liveWorkSheetQuestionResponse = null;
    if (TestType.LIVE_WORKSHEET.equals(testDefinition.getType())) {
      liveWorkSheetQuestionResponse = liveWorkSheetService.getLiveWorksheetResponse(testDefinition);
    }

    var response =
        TestDefinitionResponse.builder()
            .testDefinitionId(testDefId)
            .assetSlug(
                Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
                    ? testDefinition.getMetadata().getAssetSlug()
                    : null)
            .subjectName(
                Objects.nonNull(testDefinition.getSubjectSlug())
                    ? strapiService.getSubjectBySlug(testDefinition.getSubjectSlug()).getName()
                    : null)
            .gradeName(contentService.getGradeBySlug(testDefinition.getGradeSlug()).getName())
            .questionsAndOptions(
                Objects.nonNull(liveWorkSheetQuestionResponse)
                    ? liveWorkSheetQuestionResponse.workSheetQuestion()
                    : getTestDefQuestionsFromContentService(
                        bearerToken,
                        getTestQuestionsForTestDefinition(testDefinition),
                        testDefinition.getType()))
            .liveWorksheetAnswers(
                Objects.nonNull(liveWorkSheetQuestionResponse)
                    ? liveWorkSheetQuestionResponse.answers()
                    : null)
            .theme(testDefinition.getTheme())
            .build();

    response.setTestDefinitionId(testDefId);
    response.setTestName(testDefinition.getTestName());
    response.setNoOfQuestions(testDefinition.getNoOfQuestions());
    response.setChapterNames(chapterName);
    response.setInstructions(testDefinition.getInstructions());
    response.setCreatedBy(userService.getNameByUserInfo(testDefinition.getTeacher()));
    if (Objects.nonNull(testDefinition.getMetadata().getExplanationVideo())) {
      response.setExplanationVideoLink(
          Objects.isNull(testDefinition.getMetadata().getExplanationVideo().getVideoLink())
              ? null
              : testDefinition.getMetadata().getExplanationVideo().getVideoLink());
    }
    addScheduledTestDetails(response, testDefId);

    return updatePreviewUrls(testDefinition, response);
  }

  private TestDefinitionResponse updatePreviewUrls(
      TestDefinition testDefinition, TestDefinitionResponse response) {

    if (testDefinition.getQuestionPath() != null) {
      response.setQuestionPreviewUrl(
          storageService.generatePreSignedUrlForFetch(testDefinition.getQuestionPath()));
    }

    if (testDefinition.getSolutionPath() != null) {
      response.setSolutionPreviewUrl(
          storageService.generatePreSignedUrlForFetch(testDefinition.getSolutionPath()));
    }

    return response;
  }

  public GenericResponse deleteTestDefinitionById(long testDefinitionId) {
    var testDefinition =
        testDefinitionRepository
            .findById(testDefinitionId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND,
                        "error.Test.Find",
                        new String[] {Long.toString(testDefinitionId)}));
    validateIfTeacherCanDeleteTest(testDefinition);
    validateIfThereAreActiveSchedules(testDefinition);

    testDefinition.setActive(false);
    testDefinition.setDeletedAt(new Date());
    testDefinition.setUpdatedAt(new Timestamp(new Date().getTime()));
    testDefinitionRepository.save(testDefinition);
    return GenericResponse.builder().status(true).message("Deleted successfully!").build();
  }

  private void validateIfTeacherCanDeleteTest(TestDefinition testDefinition) {
    if (authService.getUserDetails().getId() != testDefinition.getTeacher().getId()
        && !AuthUtil.isOrgAdmin(authService.getUserDetails())) {
      throw new ApiException(INVALID_REQUEST, "error.DeleteTest.Unauthorized");
    }
  }

  private void validateIfThereAreActiveSchedules(TestDefinition testDefinition) {
    long count = scheduleTestRepository.countAllTestSchedulesForDefinition(testDefinition.getId());

    if (count > 0) {
      throw new ApiException(INVALID_REQUEST, "error.Test.Definition");
    }
  }

  private TestDefinitionMetadata generateChapterMetadata(
      TestDefinitionSection testDefinitionSection, TestDefinitionRequest testDefinitionRequest) {
    if (testDefinitionSection == null) {
      return TestDefinitionMetadata.builder()
          .chapters(new ArrayList<>())
          .subtopics(new ArrayList<>())
          .assetSlug(
              Objects.nonNull(testDefinitionRequest.getAssetSlug())
                  ? testDefinitionRequest.getAssetSlug()
                  : null)
          .explanationVideo(
              VideoExplanationMetadata.builder()
                  .videoLink(testDefinitionRequest.getExplanationVideoLink())
                  .build())
          .build();
    }

    final List<TestQuestion> testQuestions = testDefinitionSection.getTestQuestions();
    Set<String> chapterSlugs =
        testQuestions.stream().map(TestQuestion::getChapterSlug).collect(Collectors.toSet());

    var chapters = new ArrayList<ChapterMetadata>();
    chapterSlugs.forEach(
        slug ->
            chapters.add(
                ChapterMetadata.builder()
                    .name(strapiService.getChapterBySlug(slug).getName())
                    .slug(slug)
                    .build()));

    Set<String> subtopicSlugs =
        testQuestions.stream().map(TestQuestion::getSubtopicSlug).collect(Collectors.toSet());

    var subtopics =
        subtopicSlugs.stream()
            .map(
                subtopicSlug ->
                    SubTopicMetadata.builder()
                        .name(strapiService.getSubTopicBySlug(subtopicSlug).getName())
                        .slug(subtopicSlug)
                        .build())
            .toList();

    return TestDefinitionMetadata.builder()
        .chapters(chapters)
        .subtopics(subtopics)
        .assetSlug(
            Objects.nonNull(testDefinitionRequest.getAssetSlug())
                ? testDefinitionRequest.getAssetSlug()
                : null)
        .explanationVideo(
            VideoExplanationMetadata.builder()
                .videoLink(
                    Objects.nonNull(testDefinitionRequest.getExplanationVideoLink())
                        ? testDefinitionRequest.getExplanationVideoLink()
                        : null)
                .build())
        .build();
  }

  public ChapterMetadata getChapterMetadata(TestDefinition testDefinition) {
    List<ChapterMetadata> chapters = testDefinition.getMetadata().getChapters();
    return chapters == null || chapters.isEmpty()
        ? ChapterMetadata.builder().name("").slug("").build()
        : chapters.getFirst();
  }

  public SubTopicMetadata getSubTopicMetadata(TestDefinition testDefinition) {
    List<SubTopicMetadata> subtopics = testDefinition.getMetadata().getSubtopics();
    return subtopics == null || subtopics.isEmpty()
        ? SubTopicMetadata.builder().name("").slug("").build()
        : subtopics.getFirst();
  }

  private void buildTestDefinitionResponses(
      Set<TestDefinitionResponse> responses,
      String scheduledTestStatus,
      List<TestDefinition> testDefinitions) {
    var gradesMap = strapiContentHelper.getGradesMap();
    var subjectsMap = strapiContentHelper.getSubjectsMap();
    AtomicReference<String> status = new AtomicReference<>(scheduledTestStatus);
    testDefinitions.forEach(
        testDef -> {
          var response = new TestDefinitionResponse();
          var testSchedule = scheduleTestRepository.findTop1ByTestDefinition(testDef);
          if (testSchedule.isEmpty()) {
            status.set(NOT_SCHEDULED);
          } else if (testSchedule.get().getTeacher().equals(testDef.getTeacher())) {
            status.set(SCHEDULED);
          }
          response.setStatus(String.valueOf(status));
          response.setTestName(testDef.getTestName());
          response.setGradeSlug(testDef.getGradeSlug());
          response.setBoardSlug(testDef.getBoardSlug());
          response.setTestDefinitionId(testDef.getId());
          response.setSubjectSlug(testDef.getSubjectSlug() == null ? "" : testDef.getSubjectSlug());
          response.setNoOfQuestions(testDef.getNoOfQuestions());
          response.setGradeName(
              testDef.getGradeSlug().isEmpty()
                  ? null
                  : Optional.ofNullable(gradesMap.get(testDef.getGradeSlug()))
                      .map(Grade::getName)
                      .orElse(null));
          if (testDef.getSubjectSlug() != null) {
            response.setSubjectName(
                testDef.getSubjectSlug().isEmpty()
                    ? null
                    : Optional.ofNullable(subjectsMap.get(testDef.getSubjectSlug()))
                        .map(Entity::getName)
                        .orElse(null));

            response.setChapterNames(
                String.join(COMMA_SEPERATOR, getChapterNamesOfTestDefinition(testDef)));
          }
          response.setCreatedAtDate(
              dateTimeUtil.convertIso8601ToEpoch(testDef.getCreatedAt().toLocalDateTime()));
          response.setUpdatedAtDate(
              dateTimeUtil.convertIso8601ToEpoch(testDef.getUpdatedAt().toLocalDateTime()));
          response.setType(testDef.getType());
          response.setPublishedAt(testDef.getPublishedAt());
          response.setCreatedBy(
              testDef.getTeacher().getFirstName() + testDef.getTeacher().getLastName());
          Optional<QPGenPro> qPGenPro = qpGenProRepository.findByTestDefinitionId(testDef.getId());
          response.setQpGenPresent(qPGenPro.isPresent());
          response.setAiTest(isAiTest(testDef));
          responses.add(response);
        });
  }

  public List<String> getChapterNamesOfTestDefinition(TestDefinition testDefinition) {
    if (Objects.isNull(testDefinition.getMetadata())
        || Objects.isNull(testDefinition.getMetadata().getChapters())) {
      return Collections.emptyList();
    }
    return testDefinition.getMetadata().getChapters().stream()
        .map(ChapterMetadata::getName)
        .toList();
  }

  private List<TestQuestion> getTestDefQuestions(
      final TestDefinition testDefinition, final TestDefinitionRequest testDefinitionRequest) {
    if (testDefinitionRequest.getQuestions() != null
        && !testDefinitionRequest.getQuestions().isEmpty()) {
      return testDefinitionRequest.getQuestions().stream()
          .map(
              questionRequest ->
                  TestQuestion.builder()
                      .questionUuid(questionRequest.getQuestionUuid())
                      .marks(questionRequest.getMarks())
                      .type(questionRequest.getType())
                      .chapterSlug(questionRequest.getChapterSlug())
                      .subtopicSlug(questionRequest.getSubtopicSlug())
                      .negativeMarks(questionRequest.getNegativeMarks())
                      .subjectSlug(questionRequest.getSubjectSlug())
                      .mcqAnswer(questionRequest.getMcqAnswer())
                      .natAnswer(questionRequest.getNatAnswer())
                      .fbqAnswer(questionRequest.getFbqAnswer())
                      .msqAnswer(questionRequest.getMsqAnswer())
                      .yesNo(questionRequest.getYesNo())
                      .pbqAnswers(questionRequest.getPbqAnswers())
                      .spchAnswer(questionRequest.getSpchAnswer())
                      .amcqAnswer(questionRequest.getAmcqAnswer())
                      .ddFbqAnswer(questionRequest.getDdFbqAnswer())
                      .testDefinitionSection(
                          buildTestDefinitionSections(
                              testDefinition,
                              questionRequest.getTestDefinitionSectionId() == null
                                  ? 0
                                  : questionRequest.getTestDefinitionSectionId()))
                      .build())
          .toList();
    }
    return Collections.emptyList();
  }

  public List<Question> getTestDefQuestionsFlattened(
      TestDefinition testDefinitionEntity, String bearerToken) {
    return getTestDefQuestionsFromContentService(
        bearerToken,
        getTestQuestionsForTestDefinition(testDefinitionEntity),
        testDefinitionEntity.getType());
  }

  private List<Question> getTestDefQuestionsFromContentService(
      String bearerToken, List<TestQuestion> testQuestions, TestType type) {

    if (Objects.nonNull(type) && TestType.ASSIGNMENT.name().equals(type.name())) {
      return testQuestions.stream()
          .map(
              question ->
                  contentService.getAssignmentQuestionByUuid(
                      bearerToken, question.getQuestionUuid()))
          .collect(Collectors.toList());
    }
    User user = authService.getUserDetails();
    List<Question> questions =
        testQuestions.stream()
            .map(
                question -> {
                  final var questionType = QuestionType.getByType(question.getType());
                  var ques = new Question();
                  if (AuthUtil.isStudent(user)) {
                    ques =
                        contentService.getQuestionBySubjectSlugAndUuidForStudent(
                            bearerToken,
                            questionType,
                            question.getSubjectSlug(),
                            question.getQuestionUuid(),
                            Boolean.TRUE);
                  } else if (QuestionType.MCQ.equals(questionType)
                      || QuestionType.SUBJECTIVE.equals(questionType)) {
                    ques =
                        contentService.getQuestionBySubjectSlugAndUuid(
                            bearerToken,
                            questionType,
                            question.getSubjectSlug(),
                            question.getQuestionUuid(),
                            Boolean.TRUE);
                  }
                  assert ques != null;
                  ques.setMarks(question.getMarks());
                  ques.setSubjectSlug(
                      Objects.nonNull(question.getSubjectSlug())
                          ? question.getSubjectSlug()
                          : null);
                  ques.setSubjectName(
                      Objects.nonNull(question.getSubjectSlug())
                          ? strapiService.getSubjectBySlug(question.getSubjectSlug()).getName()
                          : null);
                  return ques;
                })
            .collect(toCollection(LinkedList::new));
    if (AuthUtil.isStudent(user)) {
      Collections.shuffle(questions);
    }
    return questions;
  }

  private void addScheduledTestDetails(TestDefinitionResponse response, long testDefId) {
    var scheduledTestById = scheduleTestRepository.findByStatusAndDefinationId(testDefId);
    if (scheduledTestById != null && !scheduledTestById.isEmpty()) {
      ScheduleTest scheduledTest = scheduledTestById.getFirst();
      response.setDuration(scheduledTest.getDuration());
      response.setStartDate(scheduledTest.getStartDate());
      response.setEndDate(scheduledTest.getEndDate());
      response.setStatus(
          (scheduleTestService.getScheduledTestStatus(
              scheduledTest.getStartDate(), scheduledTest.getEndDate())));
    }
  }

  public QuestionsAnalytics getQuestionsAnalyticsById(
      String authUserId, long scheduledTestId, String bearerToken) {

    var scheduledTest = scheduleTestService.findTestScheduleById(scheduledTestId);

    var childTestScheduleIds = getChildTestSchedules(scheduledTest);
    var testDefinition = scheduledTest.getTestDefinition();

    List<Question> questions =
        getTestDefQuestionsFromContentService(
            bearerToken,
            getTestQuestionsForTestDefinition(testDefinition),
            testDefinition.getType());

    List<QuestionDetails> questionDetails =
        scheduleTestRepository.getQuestionDetailsByScheduleTestId(childTestScheduleIds);
    List<ScheduleTestStudent> scheduleTestStudents =
        scheduleTestStudentRepository.getAllStudentsByScheduledId(childTestScheduleIds);
    List<ScheduleTestStudent> attemptedStudents =
        scheduleTestStudents.stream()
            .filter(s -> StringUtils.equals(s.getStatus(), "COMPLETED"))
            .toList();
    User user = userRepository.getUserByAuthUserId(authUserId);

    var subjectMap = strapiContentHelper.getSubjectsMap();

    return QuestionsAnalytics.builder()
        .title(testDefinition.getTestName())
        .createdDate(
            DateTimeUtil.convertIso8601ToEpoch(scheduledTest.getCreatedAt().toLocalDateTime()))
        .attemptedStudentCount(attemptedStudents.size())
        .totalStudentsCount(scheduleTestStudents.size())
        .chapter(getChapterNamesOfTestDefinition(testDefinition))
        .subject(
            StringUtils.isBlank(testDefinition.getSubjectSlug())
                ? ""
                : subjectMap.get(testDefinition.getSubjectSlug()).getName())
        .teacherName(user.getFirstName() + " " + user.getLastName())
        .subTopic(getSubTopics(testDefinition))
        .questionDetails(questionDetails)
        .questionData(questions)
        .build();
  }

  private List<String> getSubTopics(TestDefinition testDefinition) {
    return testDefinition.getMetadata().getSubtopics().stream()
        .map(SubTopicMetadata::getName)
        .toList();
  }

  public List<Long> getChildTestSchedules(ScheduleTest scheduleTest) {
    List<Long> childTestScheduleIds;

    List<Long> repositoryChildTestScheduleIds =
        scheduleTestRepository.findChildTestScheduleIds(scheduleTest.getId());

    // When a test is scheduled by manager to couple of child orgs, repositoryChildTestScheduleIds
    // wont be empty and there will be a Parent_test_schedule_id. In all other cases, other logic
    // precedes
    if (Objects.isNull(scheduleTest.getParent()) && !repositoryChildTestScheduleIds.isEmpty()) {
      childTestScheduleIds = repositoryChildTestScheduleIds;
    } else {
      childTestScheduleIds = Collections.singletonList(scheduleTest.getId());
    }
    return childTestScheduleIds;
  }

  public List<TestDefinitionResponse> getSchoolTestByChapter(
      String orgSlug, String chapterSlug, int limit) {
    List<SchoolTestQueryResponse> allSchoolTest =
        scheduleTestRepository.getAllSchoolTest(orgSlug, chapterSlug, limit);
    if (allSchoolTest.isEmpty()) {
      return new ArrayList<>();
    }
    List<TestDefinitionResponse> td = new ArrayList<>();
    allSchoolTest.forEach(
        testDefinition -> {
          TestDefinitionResponse tds = new TestDefinitionResponse();
          tds.setTestName(testDefinition.getTestName());
          tds.setTestDefinitionId(testDefinition.getId());
          td.add(tds);
        });
    return td;
  }

  public List<TestDefinitionResponse> getMockTestByGrade(
      String orgSlug, String gradeSlug, int limit) {
    List<TestDefinition> allMockTest =
        testDefinitionRepository.getAllByGradeAndOrgSlug(
            gradeSlug, orgSlug, TestType.MOCK_TEST.name(), limit);

    if (allMockTest.isEmpty()) {
      return new ArrayList<>();
    }
    List<TestDefinitionResponse> testDefinitionResponses = new ArrayList<>();
    allMockTest.forEach(
        testDefinition -> {
          TestDefinitionResponse testDefinitionResponse = new TestDefinitionResponse();
          testDefinitionResponse.setTestName(testDefinition.getTestName());
          testDefinitionResponse.setTestDefinitionId(testDefinition.getId());
          testDefinitionResponses.add(testDefinitionResponse);
        });
    return testDefinitionResponses;
  }

  public void editTestDefinition(
      long testDefinitionId, TestDefinitionsDto.EditTestDefinitionRequest testDefinitionRequest) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    if (!StringUtils.isBlank(testDefinitionRequest.name())) {
      testDefinition.setTestName(testDefinitionRequest.name());
      testDefinition.setCategory(inferCategory(testDefinitionRequest.name()));
      testDefinition.setInstructions(testDefinitionRequest.instruction());
    }
    testDefinitionRepository.save(testDefinition);
  }

  private TestCategory inferCategory(String name) {
    final TestCategory[] values = TestCategory.values();

    final Optional<TestCategory> optionalTestCategory =
        Arrays.stream(values)
            .sorted(Comparator.comparing(TestCategory::getValue))
            .filter(
                category ->
                    category.getProcessor().supports(name)
                        && !category.equals(TestCategory.DEFAULT))
            .findFirst();
    return optionalTestCategory.orElse(TestCategory.DEFAULT);
  }

  public void createTestDefinitionSection(
      long testDefinitionId,
      TestDefinitionsDto.TestDefinitionSectionRequest testDefinitionRequest) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    testDefinitionSectionRepository.save(
        buildTestDefinitionSectionsRequest(testDefinition, testDefinitionRequest));
  }

  public TestDefinition getTestDefinitionById(long testDefinitionId) {
    return testDefinitionRepository
        .findById(testDefinitionId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.NO_RECORD_FOUND,
                    "error.Test.Find",
                    new String[] {Long.toString(testDefinitionId)}));
  }

  private TestDefinitionSection buildTestDefinitionSectionsRequest(
      TestDefinition testDefinition,
      TestDefinitionsDto.TestDefinitionSectionRequest testDefinitionRequest) {
    var nextSeqNum = testDefinitionSectionRepository.countByTestDefinition(testDefinition) + 1;
    return TestDefinitionSection.builder()
        .testDefinition(testDefinition)
        .sequenceNumber(nextSeqNum)
        .name(testDefinitionRequest.name())
        .noOfQuestions(testDefinitionRequest.noOfQuestions())
        .build();
  }

  public TestDefinitionsDto.TestDefinitionResponse getTestDefinitionByIdV1(long testDefinitionId) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    var testDefinitionSections =
        testDefinitionSectionRepository.findByTestDefinitionOrderById(testDefinition);
    return TestDefinitionsDto.TestDefinitionResponse.builder()
        .testDefId(testDefinition.getId())
        .name(testDefinition.getTestName())
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .instructions(testDefinition.getInstructions())
        .sectionsList(buildTestDefinitionSections(testDefinitionSections))
        .boardSlug(testDefinition.getBoardSlug())
        .gradeSlug(testDefinition.getGradeSlug())
        .createdBy(userService.getNameByUserInfo(testDefinition.getTeacher()))
        .message(testDefinition.getMessage())
        .noOfQuestions(Long.valueOf(testDefinition.getNoOfQuestions()))
        .build();
  }

  private List<TestDefinitionsDto.Sections> buildTestDefinitionSections(
      List<TestDefinitionSection> testDefinitionSections) {
    if (testDefinitionSections.isEmpty()) {
      return Collections.emptyList();
    }
    var testDef = testDefinitionSections.getFirst().getTestDefinition();
    List<TestDefinitionsDto.Sections> sectionsList;
    var qpGen = qpGenProRepository.findByTestDefinitionId(testDef.getId());

    if (qpGen.isPresent() && qpGen.get().getBluePrint() != null) {
      var bluePrint = qpGen.get().getBluePrint();
      var bluePrintSections =
          bluePrintSectionRepository.findAllByBluePrintOrderBySectionName(bluePrint);
      Map<String, TestDefinitionsDto.Sections.SectionsBuilder> combinedSectionsMap =
          new HashMap<>();
      for (TestDefinitionSection section : testDefinitionSections) {
        var matchingBluePrintSection =
            bluePrintSections.stream()
                .filter(sec -> section.getName().equalsIgnoreCase(sec.getSectionName()))
                .min(
                    Comparator.comparing(
                        sec -> sec.getInstructions() == null || sec.getInstructions().isEmpty()));

        var questionsSaved = testQuestionRepository.countByTestDefinitionSection(section);
        var testDefinitionResponse =
            getQuestionsByTestDefinitionSectionId(
                section.getId(), contentBearerToken, testDef.getOrganization());
        var testQuestions = testDefinitionResponse.getQuestionsList();
        List<QuestionDto.Question> questionsList = new ArrayList<>();
        testQuestions.forEach(
            question ->
                questionsList.add(
                    buildQuestions(question, section.getId(), testDef.getOrganization())));
        testDefinitionResponse.setQuestionsList(questionsList);
        combinedSectionsMap.compute(
            section.getName(),
            (name, builder) -> {
              if (builder == null) {
                builder =
                    TestDefinitionsDto.Sections.builder()
                        .sequenceNumber(section.getSequenceNumber())
                        .name(section.getName())
                        .instructions(
                            matchingBluePrintSection
                                .map(BluePrintSections::getInstructions)
                                .orElse(null))
                        .noOfQuestions(0L)
                        .blueprintSectionId(
                            matchingBluePrintSection.map(BluePrintSections::getId).orElse(null))
                        .questions(new ArrayList<>())
                        .questionTag(matchingBluePrintSection.get().getTags())
                        .noOfQuestionsSaved(0L);
              }
              builder.noOfQuestions(builder.build().noOfQuestions() + section.getNoOfQuestions());
              builder.noOfQuestionsSaved(builder.build().noOfQuestionsSaved() + questionsSaved);
              List<QuestionDto.Question> existingQuestions = builder.build().questions();
              existingQuestions.addAll(testDefinitionResponse.getQuestionsList());
              builder.questions(existingQuestions);
              builder.questionTag(matchingBluePrintSection.get().getTags());
              return builder;
            });
      }
      sectionsList =
          combinedSectionsMap.values().stream()
              .map(TestDefinitionsDto.Sections.SectionsBuilder::build)
              .sorted(Comparator.comparing(TestDefinitionsDto.Sections::name))
              .toList();
    } else {
      sectionsList = new ArrayList<>(buildTestDefinitionSectionsDetails(testDefinitionSections));
    }
    return sectionsList;
  }

  public QuestionDto.Question buildQuestions(
      QuestionDto.Question question, long testDefinitionSectionId, String orgSlug) {
    return QuestionDto.Question.builder()
        .testSectionId(testDefinitionSectionId)
        .explanation(question.explanation())
        .audioPath(question.audioPath())
        .videoPath(question.videoPath())
        .mcq(question.mcq())
        .subjectSlug(question.subjectSlug())
        .category(question.category())
        .questionTags(question.questionTags())
        .active(question.active())
        .chapterSlug(question.chapterSlug())
        .id(question.id())
        .organization(question.organization())
        .subtopicSlug(question.subtopicSlug())
        .bloomsTaxonomyId(question.bloomsTaxonomyId())
        .question(question.question())
        .type(question.type())
        .uuid(question.uuid())
        .mcq(question.mcq())
        .marks(question.marks())
        .questionCategoryId(question.questionCategoryId())
        .internalChoice(buildInternalChoice(question, testDefinitionSectionId, orgSlug))
        .build();
  }

  private QuestionDto.Question buildInternalChoice(
      QuestionDto.Question question, long testDefinitionSectionId, String orgSlug) {
    var internalChoice =
        internalChoiceRepository.findByTestSectionIdAndTestQuestionUUid(
            testDefinitionSectionId, question.uuid());
    return internalChoice
        .map(
            choice ->
                transformInternalChoice(
                    buildInternalChoiceQuestion(choice, orgSlug), choice, question))
        .orElse(null);
  }

  private QuestionDto.Question transformInternalChoice(
      QuestionDto.Question question,
      InternalChoice internalChoice,
      QuestionDto.Question parentQuestion) {
    return QuestionDto.Question.builder()
        .id(question.id())
        .uuid(question.uuid())
        .category(question.category())
        .questionTags(question.questionTags())
        .question(question.question())
        .type(question.type())
        .fbq(question.fbq())
        .mcq(question.mcq())
        .active(question.active())
        .marks(parentQuestion.marks())
        .questionCategoryId(question.questionCategoryId())
        .subjectSlug(question.subjectSlug())
        .chapterSlug(question.chapterSlug())
        .explanation(question.explanation())
        .questionCategoryId(question.questionCategoryId())
        .build();
  }

  private QuestionDto.Question buildInternalChoiceQuestion(
      InternalChoice internalChoice, String orgSlug) {
    var searchResults = getInternalChoiceQuestionsByUuid(internalChoice, orgSlug);
    return searchResults.questions().get(0);
  }

  private QuestionDto.SearchQuestionResponse getInternalChoiceQuestionsByUuid(
      InternalChoice internalChoice, String orgSlug) {
    return contentService.getQuestionsByUuid(
        contentBearerToken, internalChoice.getType(), internalChoice.getQuestionUuid(), orgSlug);
  }

  private List<TestDefinitionsDto.Sections> buildTestDefinitionSectionsDetails(
      List<TestDefinitionSection> sections) {

    return sections.stream()
        .map(
            section ->
                TestDefinitionsDto.Sections.builder()
                    .id(section.getId())
                    .sequenceNumber(section.getSequenceNumber())
                    .name(section.getName())
                    .noOfQuestions(section.getNoOfQuestions())
                    .noOfQuestionsSaved(
                        testQuestionRepository.countByTestDefinitionSection(section))
                    .build())
        .toList();
  }

  public void editTestDefinitionSection(
      long testDefinitionSectionId,
      TestDefinitionsDto.TestDefinitionSectionRequest testDefinitionRequest) {
    var testDefinitionSection = getTestDefinitionSection(testDefinitionSectionId);
    testDefinitionSection.setName(testDefinitionRequest.name());
    testDefinitionSection.setNoOfQuestions(testDefinitionRequest.noOfQuestions());
    testDefinitionSectionRepository.save(testDefinitionSection);
  }

  @Transactional
  public void deleteTestDefinitionSectionById(long testDefinitionSectionId) {
    var testDefinitionSection = getTestDefinitionSection(testDefinitionSectionId);
    try {
      testQuestionRepository.deleteByTestDefinitionSection(testDefinitionSection);
      testDefinitionSectionRepository.deleteById(testDefinitionSection.getId());
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Delete.TestDefinitionSection");
    }
  }

  public TestDefinitionSection getTestDefinitionSection(long testDefinitionSectionId) {
    return testDefinitionSectionRepository
        .findById(testDefinitionSectionId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.NO_RECORD_FOUND,
                    "error.TestDefinitionSection",
                    new String[] {Long.toString(testDefinitionSectionId)}));
  }

  public void addQuestionsToSection(
      long testDefinitionId,
      TestDefinitionRequest testDefinitionRequest,
      long testDefinitionSectionId) {
    var testDefinitionEntity = getTestDefinitionById(testDefinitionId);
    var questions = getTestDefQuestions(testDefinitionEntity, testDefinitionRequest);
    var newQuestions = validateTestQuestions(questions, testDefinitionSectionId);
    testQuestionRepository.saveAll(newQuestions);
  }

  public List<TestQuestion> validateTestQuestions(
      List<TestQuestion> questionsList, long testDefinitionSectionId) {
    var testDefinitionSection = getTestDefinitionSection(testDefinitionSectionId);
    var existingQuestions =
        testQuestionRepository.findByTestDefinitionSectionOrderById(testDefinitionSection);
    var existingQuestionUuids =
        existingQuestions.stream().map(TestQuestion::getQuestionUuid).toList();
    return questionsList.stream()
        .filter(element -> !existingQuestionUuids.contains(element.getQuestionUuid()))
        .toList();
  }

  private TestDefinitionSection buildTestDefinitionSections(
      TestDefinition testDefinition, long testDefinitionSectionId) {
    if (testDefinitionSectionId != 0 && testDefinition.getId() != 0) {
      var testDefinitionSection = testDefinitionSectionRepository.findById(testDefinitionSectionId);
      if (testDefinitionSection.isEmpty()) {
        return createDefaultTestDefinitionSection(testDefinition, 0);
      }
      return testDefinitionSection.get();
    }
    return null;
  }

  private TestDefinitionSection createDefaultTestDefinitionSection(
      TestDefinition testDefinition, long countOfQuestions) {
    return TestDefinitionSection.builder()
        .testDefinition(testDefinition)
        .name("default")
        .sequenceNumber(1L)
        .noOfQuestions(countOfQuestions)
        .build();
  }

  public TestDefinitionResponse getQuestionsByTestDefinitionSectionId(
      Long testDefinitionSectionId, String bearerToken, String orgSlug) {
    var testDefinitionSection = getTestDefinitionSection(testDefinitionSectionId);
    var testDefinition = getTestDefinitionById(testDefinitionSection.getTestDefinition().getId());
    var questions =
        testQuestionRepository.findByTestDefinitionSectionOrderById(testDefinitionSection);
    var response =
        TestDefinitionResponse.builder()
            .testDefinitionId(testDefinition.getId())
            .questionsList(
                buildQuestionsResponse(
                    transformQuestionsResult(getQuestionsByUuid(bearerToken, questions, orgSlug)),
                    questions))
            .build();

    if (testDefinition.getSubjectSlug() != null && !testDefinition.getSubjectSlug().isEmpty()) {
      var subject = strapiService.getSubjectBySlug(testDefinition.getSubjectSlug());
      response.setSubjectName(subject.getName());
      response.setSubjectSlug(subject.getSlug());
      response.setSubjectId(subject.getId());
    }
    var board = strapiService.getEduBoardBySlug(testDefinition.getBoardSlug());
    var grade = contentService.getGradeBySlug(testDefinition.getGradeSlug());
    response.setBoardSlug(testDefinition.getBoardSlug());
    response.setBoardName(board.getName());
    response.setGradeName(grade.getName());
    response.setGradeSlug(testDefinition.getGradeSlug());
    response.setId(testDefinitionSectionId);
    response.setTestDefinitionId(testDefinition.getId());
    response.setTestName(testDefinition.getTestName());
    response.setNoOfQuestions(testDefinitionSection.getTestQuestions().size());
    addScheduledTestDetails(response, testDefinition.getId());
    return updatePreviewUrls(testDefinition, response);
  }

  public List<QuestionDto.Question> buildQuestionsResponse(
      List<QuestionDto.Question> transformQuestionsResult, List<TestQuestion> questions) {
    List<QuestionDto.Question> questionArrayList = new ArrayList<>();
    transformQuestionsResult.forEach(
        questionResult -> {
          var question =
              questions.stream()
                  .filter(ques -> ques.getQuestionUuid().equals(questionResult.uuid()))
                  .findFirst();
          questionArrayList.add(
              QuestionDto.Question.builder()
                  .id(questionResult.id())
                  .testSectionId(
                      question.map(tq -> tq.getTestDefinitionSection().getId()).orElse(null))
                  .uuid(questionResult.uuid())
                  .question(questionResult.question())
                  .audioPath(questionResult.audioPath())
                  .videoPath(questionResult.videoPath())
                  .marks(question.map(TestQuestion::getMarks).orElse(null))
                  .chapterSlug(questionResult.chapterSlug())
                  .active(questionResult.active())
                  .bloomsTaxonomyId(questionResult.bloomsTaxonomyId())
                  .category(questionResult.category())
                  .questionTags(questionResult.questionTags())
                  .complexity(questionResult.complexity())
                  .explanation(questionResult.explanation())
                  .msq(questionResult.msq())
                  .mcq(questionResult.mcq())
                  .nat(questionResult.nat())
                  .pbq(questionResult.pbq())
                  .amcq(questionResult.amcq())
                  .spch(questionResult.spch())
                  .fbq(questionResult.fbq())
                  .yesNo(questionResult.yesNo())
                  .ddFbq(questionResult.ddFbq())
                  .negativeMarks(question.map(TestQuestion::getNegativeMarks).orElse(0.0f))
                  .organization(questionResult.organization())
                  .organization(questionResult.organizationSlug())
                  .type(questionResult.type())
                  .subtopicSlug(questionResult.subtopicSlug())
                  .chapterSlug(questionResult.chapterSlug())
                  .subjectSlug(questionResult.subjectSlug())
                  .published(questionResult.published())
                  .questionCategoryId(questionResult.questionCategoryId())
                  .videoAssetLink(questionResult.videoAssetLink())
                  .shaLink(questionResult.shaLink())
                  .build());
        });
    questionArrayList.addAll(buildDummyQuestions(questions));
    return questionArrayList;
  }

  private List<QuestionDto.Question> buildDummyQuestions(List<TestQuestion> questions) {
    return questions.stream()
        .filter(question -> "dummy_question".equals(question.getCategory()))
        .map(
            question ->
                QuestionDto.Question.builder()
                    .uuid(question.getQuestionUuid())
                    .question("replace this question")
                    .marks(question.getMarks())
                    .chapterSlug(question.getChapterSlug())
                    .category(question.getCategory())
                    .questionTags(Collections.singletonList(question.getQuestionTags()))
                    .complexity(question.getComplexity())
                    .type(QuestionType.valueOf(question.getType()))
                    .subtopicSlug(question.getSubtopicSlug())
                    .questionCategoryId(question.getCategory())
                    .build())
        .collect(Collectors.toList());
  }

  public List<QuestionDto.Question> transformQuestionsResult(
      List<QuestionDto.SearchQuestionResponse> questionsResults) {
    return questionsResults.stream()
        .map(QuestionDto.SearchQuestionResponse::questions)
        .flatMap(List::stream)
        .toList();
  }

  @Transactional
  public void deleteQuestionByTestDefinitionSectionById(
      long testDefinitionSectionId, String questionUuid) {
    var testDefinitionSection = getTestDefinitionSection(testDefinitionSectionId);
    try {
      var testQuestion =
          testQuestionRepository.findByTestDefinitionSectionAndQuestionUuid(
              testDefinitionSection, questionUuid);
      testQuestionRepository.delete(testQuestion);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Delete.Question");
    }
  }

  public void publishTestDefinitionById(
      long testDefinitionId, boolean publishStatus, String bearerToken, boolean allowOverride) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    if (publishStatus) {
      testDefinitionValidatorProcessor.validate(testDefinition);
      testDefinition
          .getTestDefinitionSections()
          .forEach(x -> x.setNoOfQuestions((long) x.getTestQuestions().size()));
      var questionsCount =
          testDefinition.getTestDefinitionSections().stream()
              .map(TestDefinitionSection::getTestQuestions)
              .mapToLong(List::size)
              .sum();
      var totalMarks =
          testDefinition.getTestDefinitionSections().stream()
              .flatMap(section -> section.getTestQuestions().stream())
              .mapToInt(TestQuestion::getMarks)
              .sum();
      var testDefinitionCount = testDefinition.getNoOfQuestions();
      if (!allowOverride) {
        validateQuestionCount(questionsCount, testDefinitionCount);
      }
      testDefinition.setPublishedAt(Timestamp.from(Instant.now()));
      testDefinition.setNoOfQuestions(((int) questionsCount));
      if (testDefinition.getType().equals(TestType.MOCK_TEST)
          && (Objects.isNull(testDefinition.getSubjectSlug())
              || testDefinition.getSubjectSlug().isEmpty())
          && !testDefinition.getTestDefinitionSections().get(0).getTestQuestions().isEmpty()) {
        var subjectSlug =
            testDefinition
                .getTestDefinitionSections()
                .get(0)
                .getTestQuestions()
                .get(0)
                .getSubjectSlug();
        testDefinition.setSubjectSlug(subjectSlug);
      }
      testDefinition.setTotalMarks(totalMarks);
      testDefinition.setQuestionV2(
          uploadV2QuestionResponses(
              testDefinition,
              buildQuestionResponse(
                  buildTestDefinitionSection(testDefinition, bearerToken), testDefinition)));
    } else {
      var status = checkTestDefinitionScheduleStatus(testDefinition);
      if (status) {
        throw new ApiException(INVALID_REQUEST, "error.TestDefinition.UnPublish");
      }
      testDefinition.setPublishedAt(null);
    }
    testDefinitionRepository.save(testDefinition);
  }

  private void validateQuestionCount(long questionsCount, Integer testDefinitionCount) {

    if (questionsCount != testDefinitionCount) {
      throw new ApiException(
          INVALID_REQUEST,
          "error.TestDefinitionPublish.Conflict",
          new String[] {String.valueOf(testDefinitionCount), String.valueOf(questionsCount)});
    }
  }

  private List<QuestionDto.QuestionResponse> buildQuestionResponse(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses,
      TestDefinition testDefinition) {

    List<QuestionDto.QuestionResponse> questionResponses = new ArrayList<>();
    questionResponses.add(
        build1stSetWithoutShuffle(testDefinition, testDefinitionSectionResponses));
    IntStream.range(1, 5)
        .forEach(
            response ->
                questionResponses.add(
                    QuestionDto.QuestionResponse.builder()
                        .testDefinitionId(testDefinition.getId())
                        .testName(testDefinition.getTestName())
                        .gradeName(contentService.getGradeNameBySlug(testDefinition.getGradeSlug()))
                        .noOfQuestions((long) testDefinition.getNoOfQuestions())
                        .testDefinitionSectionResponses(
                            new ArrayList<>(shuffleMockQuestions(testDefinitionSectionResponses)))
                        .build()));

    return questionResponses;
  }

  private QuestionDto.QuestionResponse build1stSetWithoutShuffle(
      TestDefinition testDefinition,
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses) {
    return QuestionDto.QuestionResponse.builder()
        .testDefinitionId(testDefinition.getId())
        .testName(testDefinition.getTestName())
        .gradeName(contentService.getGradeNameBySlug(testDefinition.getGradeSlug()))
        .noOfQuestions((long) testDefinition.getNoOfQuestions())
        .testDefinitionSectionResponses(testDefinitionSectionResponses)
        .build();
  }

  private List<QuestionDto.TestDefinitionSectionResponse> shuffleMockQuestions(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses) {

    List<QuestionDto.TestDefinitionSectionResponse> shuffledTestDefintion = new ArrayList<>();
    SecureRandom random = new SecureRandom();
    testDefinitionSectionResponses.forEach(
        tds -> {
          List<QuestionDto.Question> clonedQuestions = new ArrayList<>(tds.questions());
          Collections.shuffle(clonedQuestions, random);
          shuffledTestDefintion.add(
              QuestionDto.TestDefinitionSectionResponse.builder()
                  .id(tds.id())
                  .name(tds.name())
                  .seqNo(tds.seqNo())
                  .noOfQuestions(tds.noOfQuestions())
                  .questions(clonedQuestions)
                  .marks(tds.marks())
                  .build());
        });
    return shuffledTestDefintion;
  }

  private List<QuestionDto.TestDefinitionSectionResponse> buildTestDefinitionSection(
      TestDefinition testDefinition, String bearerToken) {

    List<TestDefinitionSection> testDefinitionSections = testDefinition.getTestDefinitionSections();

    if (testDefinitionSections.isEmpty()
        || testDefinitionSections.getFirst().getTestQuestions().isEmpty()) {
      throw new ApiException(
          INVALID_REQUEST, "error.NoTestSectionFound", new String[] {testDefinition.getTestName()});
    }

    List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses =
        new ArrayList<>();
    testDefinitionSections.forEach(
        tds -> {
          var questions =
              getQuestionsByUuid(
                  bearerToken, tds.getTestQuestions(), testDefinition.getOrganization());
          testDefinitionSectionResponses.add(
              TestDefinitionSectionResponse.builder()
                  .id(tds.getId())
                  .name(tds.getName())
                  .seqNo(tds.getSequenceNumber())
                  .noOfQuestions(tds.getNoOfQuestions())
                  .marks(
                      questions.stream()
                          .map(QuestionDto.SearchQuestionResponse::questions)
                          .flatMap(Collection::stream)
                          .mapToInt(QuestionDto.Question::marks)
                          .sum())
                  .questions(
                      buildV2Questions(transformQuestionsResult(questions), tds.getTestQuestions()))
                  .build());
        });
    testDefinition.setNoOfQuestions(
        (int)
            testDefinitionSections.stream()
                .map(TestDefinitionSection::getTestQuestions)
                .mapToLong(List::size)
                .sum());
    return testDefinitionSectionResponses;
  }

  private List<QuestionDto.Question> buildV2Questions(
      List<QuestionDto.Question> questions, List<TestQuestion> testQuestions) {
    List<TestQuestion> sortedQuestions =
        testQuestions.stream().sorted(Comparator.comparing(TestQuestion::getId)).toList();
    List<QuestionDto.Question> mockQuestions = new ArrayList<>();
    sortedQuestions.forEach(
        testQuestion -> {
          var optionalQuestion =
              questions.stream()
                  .filter(x -> x.uuid().equals(testQuestion.getQuestionUuid()))
                  .findAny();
          if (optionalQuestion.isEmpty()) {
            throw new ApiException(INVALID_REQUEST, "Invalid Question");
          }
          var data = optionalQuestion.get();
          mockQuestions.add(
              QuestionDto.Question.builder()
                  .question(massageQuestionByType(data))
                  .category(data.category())
                  .questionTags(data.questionTags())
                  .id(data.id())
                  .complexity(data.complexity())
                  .chapterSlug(data.chapterSlug())
                  .subtopicSlug(data.subtopicSlug())
                  .subjectSlug(data.subjectSlug())
                  .uuid(data.uuid())
                  .mcq(buildMcqQuestion(data.mcq(), null))
                  .explanation(data.explanation())
                  .msq(buildMsqQuestion(data.msq(), null))
                  .nat(buildNatQuestion(data.nat(), null))
                  .fbq(buildFbqQuestion(data.fbq(), null))
                  .yesNo(buildYesNoQuestion(data.yesNo(), null))
                  .pbq(buildPbqQuestion(data.pbq(), null, null))
                  .amcq(buildAmcqQuestion(data.amcq(), null))
                  .spch(buildSpchQuestion(data.spch(), null))
                  .ddFbq(buildDdFbqQuestion(data.ddFbq(), null))
                  .marks(testQuestion.getMarks())
                  .type(data.type())
                  .audioPath(data.audioPath())
                  .videoPath(data.videoPath())
                  .videoAssetLink(data.videoAssetLink())
                  .shaLink(data.shaLink())
                  .questionCategoryId(data.questionCategoryId())
                  .build());
        });

    return mockQuestions;
  }

  private String massageQuestionByType(QuestionDto.Question data) {
    if (!QuestionType.DDFBQ.equals(data.type())) {
      return data.question();
    }
    return data.question().replaceAll("\\$.*?\\$", "\\$ANSWER\\$");
  }

  public List<QuestionDto.Pbq> buildPbqQuestion(
      List<QuestionDto.Pbq> pbqs, PbqDto.Data selectedAnswers, PbqDto.Data testAnswers) {
    if (Objects.isNull(pbqs) || pbqs.isEmpty()) {
      return Collections.emptyList();
    }
    Map<String, Integer> answersMap = new HashMap<>();
    Map<String, Integer> selectedAnswersMap = new HashMap<>();

    if (Objects.nonNull(testAnswers)) {
      testAnswers
          .answers()
          .forEach(data -> answersMap.put(data.mcq().questionUuid(), data.mcq().answer()));
    }
    if (Objects.nonNull(selectedAnswers)) {
      selectedAnswers
          .answers()
          .forEach(
              data ->
                  selectedAnswersMap.put(data.mcq().questionUuid(), data.mcq().selectedAnswer()));
    }
    return pbqs.stream()
        .map(
            pbq ->
                QuestionDto.Pbq.builder()
                    .id(pbq.id())
                    .marks(pbq.marks())
                    .question(pbq.question())
                    .uuid(pbq.uuid())
                    .selectedAnswer(
                        selectedAnswersMap.isEmpty() ? null : selectedAnswersMap.get(pbq.uuid()))
                    .explanation(null)
                    .type(pbq.type())
                    .mcq(
                        buildMcqQuestion(
                            pbq.mcq(),
                            answersMap.isEmpty() || Objects.isNull(answersMap.get(pbq.uuid()))
                                ? null
                                : Long.valueOf(answersMap.get(pbq.uuid()))))
                    .build())
        .toList();
  }

  public QuestionDto.YesNo buildYesNoQuestion(QuestionDto.YesNo data, Boolean answer) {
    if (Objects.isNull(data)) {
      return null;
    }
    return QuestionDto.YesNo.builder()
        .yesLabel(data.yesLabel())
        .noLabel(data.yesLabel())
        .answer(answer)
        .build();
  }

  public QuestionDto.Nat buildNatQuestion(QuestionDto.Nat nat, Float answer) {
    if (Objects.isNull(nat)) {
      return null;
    }
    return QuestionDto.Nat.builder().answer(answer).build();
  }

  public QuestionDto.Fbq buildFbqQuestion(QuestionDto.Fbq fbq, String answer) {
    if (Objects.isNull(fbq)) {
      return null;
    }
    return QuestionDto.Fbq.builder().answer(answer).build();
  }

  public QuestionDto.Msq buildMsqQuestion(QuestionDto.Msq data, List<Long> answers) {
    if (Objects.isNull(data)) {
      return null;
    }
    return QuestionDto.Msq.builder()
        .option1(data.option1())
        .option2(data.option2())
        .option3(data.option3())
        .option4(data.option4())
        .answers(answers)
        .build();
  }

  public QuestionDto.Amcq buildAmcqQuestion(QuestionDto.Amcq data, Integer answer) {
    if (Objects.isNull(data)) {
      return null;
    }
    String videoUrl =
        data.videoPath() != null && (!data.videoPath().contains(VIMEO_URL_PREFIX))
            ? storageService.generatePreSignedUrlForFetchWithMaxExpiry(data.videoPath(), 7)
            : data.videoAssetUrl();
    return QuestionDto.Amcq.builder()
        .option1(data.option1())
        .option2(data.option2())
        .option3(data.option3())
        .option4(data.option4())
        .audioPath(data.audioPath())
        .answer(answer)
        .isAudio(data.isAudio())
        .videoAssetSlug(data.videoAssetSlug())
        .videoAssetUrl(videoUrl)
        .shaLink(data.shaLink())
        .build();
  }

  public QuestionDto.Subjective buildSubjectiveQuestion(QuestionDto.Subjective data) {
    if (Objects.isNull(data)) {
      return null;
    }
    return QuestionDto.Subjective.builder().explanation(data.explanation()).build();
  }

  public QuestionDto.DdFbq buildDdFbqQuestion(QuestionDto.DdFbq data, QuestionDto.DdFbq ddfbq) {
    if (Objects.isNull(data)) {
      return null;
    }
    var words = data.words();
    if (Objects.isNull(ddfbq)) {
      Collections.shuffle(words);
    }
    return QuestionDto.DdFbq.builder()
        .words(words)
        .questionAndAnswer(Objects.nonNull(ddfbq) ? ddfbq.questionAndAnswer() : null)
        .build();
  }

  public QuestionDto.Spch buildSpchQuestion(QuestionDto.Spch data, String answer) {
    if (Objects.isNull(data)) {
      return null;
    }
    return QuestionDto.Spch.builder()
        .answerAudioPath(data.answerAudioPath())
        .explanationAudioPath(data.explanationAudioPath())
        .build();
  }

  public QuestionDto.Mcq buildMcqQuestion(QuestionDto.Mcq data, Long answer) {
    if (Objects.isNull(data)) {
      return null;
    }
    return QuestionDto.Mcq.builder()
        .option1(data.option1())
        .option2(data.option2())
        .option3(data.option3())
        .option4(data.option4())
        .answer(answer)
        .build();
  }

  public List<String> uploadV2QuestionResponses(
      TestDefinition testDefinition, List<QuestionDto.QuestionResponse> questionResponses) {

    ObjectMapper objectMapper = new ObjectMapper();

    List<String> signedUrls = new ArrayList<>();
    DateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmssSSS");
    String simpleDate = sdf.format(new Date());
    if (!questionResponses.isEmpty()) {
      for (int i = 0; i < questionResponses.size(); i++) {
        try {
          var contentAsBytes = objectMapper.writeValueAsBytes(questionResponses.get(i));
          final String objectKey =
              format(
                  Constants.TEST_DEFINITION_V2_QUESTIONS_STRING_PATH,
                  testDefinition.getOrganization(),
                  simpleDate,
                  i);
          storageService.uploadFile(contentAsBytes, objectKey, MediaType.APPLICATION_JSON_VALUE);
          signedUrls.add(objectKey);
        } catch (JsonProcessingException e) {
          throw new ApiException(INVALID_REQUEST, e.getMessage(), e);
        }
      }
    }
    if (signedUrls.isEmpty()) {
      new ArrayList<>();
    }
    return signedUrls;
  }

  private boolean checkTestDefinitionScheduleStatus(TestDefinition testDefinition) {
    boolean status = false;
    var scheduleTestCount = scheduleTestRepository.countByTestDefinition(testDefinition);
    if (scheduleTestCount > 0) {
      status = true;
    }
    return status;
  }

  public void addVideoExplanation(
      long testDefinitionId, TestDefinitionVideoRequest testDefinitionVideoRequest) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    final TestDefinitionMetadata metadata = testDefinition.getMetadata();
    metadata.setAssetSlug(testDefinitionVideoRequest.getAssetSlug());
    testDefinitionRepository.save(testDefinition);
  }

  public List<TestQuestion> getTestQuestionsForTestDefinition(TestDefinition testDefinition) {
    final List<TestDefinitionSection> testDefinitionSections =
        testDefinition.getTestDefinitionSections();
    return testDefinitionSections.stream()
        .map(TestDefinitionSection::getTestQuestions)
        .flatMap(List::stream) // flattens the list of questions across sections
        .sorted(Comparator.comparing(TestQuestion::getId)) // sort by id to preserve order
        .toList();
  }

  public List<QuestionDto.SearchQuestionResponse> getQuestionsByUuid(
      String bearerToken, List<TestQuestion> questions, String orgSlug) {
    return questions.stream()
        .map(
            question ->
                contentService.getQuestionsByUuid(
                    bearerToken, question.getType(), question.getQuestionUuid(), orgSlug))
        .toList();
  }

  public void endTest(long scheduledTestId) {
    var scheduleTest = scheduleTestRepository.findById(scheduledTestId);
    if (scheduleTest.isEmpty()) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.TestSchedule");
    }
    List<ScheduleTestStudent> scheduleTestStudent =
        scheduleTestStudentRepository.findByScheduleTest(scheduleTest.get());
    for (ScheduleTestStudent testStudent : scheduleTestStudent) {
      testStudent.setResultProcessingTime(LocalDateTime.now());
    }
    scheduleTestStudentRepository.saveAll(scheduleTestStudent);
  }

  public QuestionDto.QuestionResponse getTestDefinitionQuestions(
      Long testDefinitionId, Integer questionSetNo) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    if (testDefinition == null
        || testDefinition.getQuestionV2() == null
        || testDefinition.getPublishedAt() == null) {
      throw new ApiException(INVALID_REQUEST, "error.TestDefinitionNotPublished");
    }
    try {
      validateSetNo(testDefinition.getQuestionV2(), questionSetNo);
      String s3Path = testDefinition.getQuestionV2().get(questionSetNo - 1);
      return storageService.downloadFile(s3Path, QuestionDto.QuestionResponse.class);
    } catch (Exception ex) {
      throw new ApiException(INVALID_REQUEST, "Error.read.questions", ex);
    }
  }

  private void validateSetNo(List<String> questionV2, Integer questionSetNo) {
    var questionCount = questionV2.stream().count();
    if (questionSetNo - 1 > questionCount) {
      throw new ApiException(INVALID_REQUEST, "error.InvalidSetNo");
    }
  }

  public TestDefinition getTestDefinitionByIdAndOrgSlug(long id, String orgSlug) {
    return testDefinitionRepository
        .findByIdAndOrganization(id, orgSlug)
        .orElseThrow(() -> new ApiException(INVALID_REQUEST, ""));
  }

  public List<QuestionDto.SectionAnswerKey> getTestDefinitionAnswers(
      QuestionDto.QuestionResponse questions) {
    List<QuestionDto.SectionAnswerKey> sectionAnswerKeys = new ArrayList<>();
    AtomicReference<Long> questionNo = new AtomicReference<>(1L);
    questions
        .testDefinitionSectionResponses()
        .forEach(
            tdResponse -> {
              var testDefinitionSection =
                  validationUtils.getTestDefinitionSectionById(tdResponse.id());
              sectionAnswerKeys.add(
                  QuestionDto.SectionAnswerKey.builder()
                      .name(tdResponse.name())
                      .answerKeys(
                          buildAnswers(
                              tdResponse.questions(), testDefinitionSection, questionNo.get()))
                      .build());
              questionNo.updateAndGet(v -> v + testDefinitionSection.getNoOfQuestions());
            });
    return sectionAnswerKeys;
  }

  private List<Answer> buildAnswers(
      List<QuestionDto.Question> questions,
      TestDefinitionSection testDefinitionSection,
      Long questionNo) {

    List<Answer> answers = new ArrayList<>();
    var testQuestion =
        validationUtils.getTestQuestionByTestDefinitionSection(testDefinitionSection);
    for (QuestionDto.Question question : questions) {
      var ques =
          testQuestion.stream()
              .filter(x -> x.getQuestionUuid().equals(question.uuid()))
              .findFirst();
      if (ques.isEmpty()) {
        return new ArrayList<>();
      }
      switch (question.type()) {
        case MCQ -> addMcqAnswers(answers, questionNo, String.valueOf(ques.get().getMcqAnswer()));
        case NAT -> addAnswers(answers, questionNo, String.valueOf(ques.get().getNatAnswer()));
        case AMCQ -> addAnswers(answers, questionNo, String.valueOf(ques.get().getAmcqAnswer()));
        case PBQ -> addPbqAnswers(answers, questionNo, ques.get().getPbqAnswers());
        case SPCH -> addAnswers(answers, questionNo, ques.get().getSpchAnswer());
        case FBQ ->
            addAnswers(
                answers,
                questionNo,
                ques.get().getFbqAnswer() == null ? "" : ques.get().getFbqAnswer());
        case YESNO -> addAnswers(answers, questionNo, String.valueOf(ques.get().getYesNo()));
        case SUBJECTIVE -> addAnswers(answers, questionNo, ques.get().getSubjectiveAnswer());
        case MSQ -> addMsqAnswers(answers, questionNo, ques.get().getMsqAnswer());
        case DDFBQ -> addDdFbqAnswers(answers, questionNo, ques.get().getDdFbqAnswer());

        default -> throw new ApiException(INVALID_REQUEST, "Invalid QuestionType");
      }
      questionNo++;
    }
    return answers;
  }

  private void addPbqAnswers(List<Answer> answers, Long questionNo, PbqDto.Data pbqAnswers) {
    answers.add(
        Answer.builder()
            .pbqAnswers(pbqAnswers)
            .type(QuestionType.PBQ.getType())
            .questionNumber(String.valueOf(questionNo))
            .build());
  }

  private void addMcqAnswers(List<Answer> answers, Long questionNo, String answerValue) {
    answers.add(
        Answer.builder()
            .questionNumber(String.valueOf(questionNo))
            .answer(convertMcqAnswer(answerValue))
            .build());
  }

  public String convertMcqAnswer(String answerValue) {
    return switch (answerValue) {
      case "1" -> "A";
      case "2" -> "B";
      case "3" -> "C";
      case "4" -> "D";
      default -> null;
    };
  }

  private void addAnswers(List<Answer> answers, Long questionNo, String answerValue) {
    answers.add(
        Answer.builder().questionNumber(String.valueOf(questionNo)).answer(answerValue).build());
  }

  private void addMsqAnswers(List<Answer> answers, Long questionNo, List<Long> answer) {
    answers.add(
        QuestionDto.Answer.builder()
            .questionNumber(String.valueOf(questionNo))
            .type(QuestionType.MSQ.getType())
            .msqAnswer(answer)
            .build());
  }

  private void addDdFbqAnswers(List<Answer> answers, Long questionNo, QuestionDto.DdFbq answer) {
    answers.add(
        QuestionDto.Answer.builder()
            .questionNumber(String.valueOf(questionNo))
            .type(QuestionType.DDFBQ.getType())
            .ddFbqAnswers(answer.words())
            .build());
  }

  public QuestionDto.QuestionResponse buildQuestionResponse(
      QuestionDto.QuestionResponse questions) {
    Optional<QpGen> data =
        qpGenRepository.getDetailsByTestDefinationId(questions.testDefinitionId());
    Long duration = data.map(QpGen::getDuration).orElse(null);
    var testDefinition = validationUtils.validateTestDefinition(questions.testDefinitionId());
    var subjectName = strapiService.getSubjectNameBySlug(testDefinition.getSubjectSlug());
    sortTestDefinitionSections(questions.testDefinitionSectionResponses());
    List<TestDefinitionSectionResponse> updatedSections =
        questions.testDefinitionSectionResponses().stream()
            .map(
                section -> {
                  QuestionType type =
                      section.questions().isEmpty() ? null : section.questions().get(0).type();
                  List<QuestionDto.Question> updatedQuestions =
                      section.questions().stream()
                          .map(
                              question -> {
                                QuestionDto.Question internalChoice =
                                    buildInternalChoice(
                                        question,
                                        section.id(),
                                        contentBearerToken,
                                        type,
                                        testDefinition.getOrganization());
                                return buildQuestion(question, internalChoice);
                              })
                          .collect(Collectors.toList());

                  return QuestionDto.TestDefinitionSectionResponse.builder()
                      .id(section.id())
                      .name(section.name())
                      .noOfQuestions(section.noOfQuestions())
                      .seqNo(section.seqNo())
                      .questions(updatedQuestions)
                      .marks(section.marks())
                      .build();
                })
            .collect(Collectors.toList());

    return QuestionDto.QuestionResponse.builder()
        .testDefinitionId(questions.testDefinitionId())
        .testName(questions.testName())
        .duration(duration)
        .noOfQuestions(questions.noOfQuestions())
        .subjectName(subjectName)
        .gradeName(questions.gradeName())
        .testDefinitionSectionResponses(updatedSections)
        .totalMarks(testDefinition.getTotalMarks())
        .instructions(testDefinition.getInstructions())
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .answerKey(getTestDefinitionAnswers(questions))
        .build();
  }

  public QuestionDto.Question buildInternalChoice(
      QuestionDto.Question question,
      Long sectionId,
      String contentBearerToken,
      QuestionType type,
      String organization) {
    var internalChoiceList =
        internalChoiceRepository.findByTestSectionIdAndTestQuestionUUid(sectionId, question.uuid());
    if (!internalChoiceList.isEmpty()) {
      var internalChoice = internalChoiceList.get();
      var internalChoiceQuestionUuid = internalChoice.getQuestionUuid();
      QuestionDto.SearchQuestionResponse questionResponse =
          contentService.getQuestionsByUuid(
              contentBearerToken, type.toString(), internalChoiceQuestionUuid, organization);
      String internalQuestion =
          (questionResponse != null && !questionResponse.questions().isEmpty())
              ? questionResponse.questions().get(0).question()
              : null;
      return QuestionDto.Question.builder()
          .id(internalChoice.getId().intValue())
          .uuid(internalChoice.getQuestionUuid())
          .marks(internalChoice.getMarks())
          .subtopicSlug(internalChoice.getSubtopicSlug())
          .type(QuestionType.valueOf(internalChoice.getType()))
          .chapterSlug(internalChoice.getChapterSlug())
          .subjectSlug(internalChoice.getSubjectSlug())
          .question(internalQuestion)
          .negativeMarks(internalChoice.getNegativeMarks())
          .build();
    }
    return null;
  }

  private QuestionDto.Question buildQuestion(
      QuestionDto.Question question, QuestionDto.Question internalChoice) {
    return QuestionDto.Question.builder()
        .id(question.id())
        .uuid(question.uuid())
        .question(question.question())
        .marks(question.marks())
        .subtopicSlug(question.subtopicSlug())
        .type(question.type())
        .complexity(question.complexity())
        .explanation(question.explanation())
        .category(question.category())
        .questionTags(question.questionTags())
        .chapterSlug(question.chapterSlug())
        .subjectSlug(question.subjectSlug())
        .bloomsTaxonomyId(question.bloomsTaxonomyId())
        .active(question.active())
        .published(question.published())
        .organizationSlug(question.organizationSlug())
        .organization(question.organization())
        .negativeMarks(question.negativeMarks())
        .mcq(question.mcq())
        .msq(question.msq())
        .yesNo(question.yesNo())
        .pbq(question.pbq())
        .fbq(question.fbq())
        .nat(question.nat())
        .amcq(question.amcq())
        .spch(question.spch())
        .audioPath(question.audioPath())
        .videoPath(question.videoPath())
        .internalChoice(internalChoice)
        .worksheet(question.worksheet())
        .questionCategoryId(question.questionCategoryId())
        .build();
  }

  private void sortTestDefinitionSections(
      List<TestDefinitionSectionResponse> testDefinitionSectionResponses) {
    testDefinitionSectionResponses.sort(
        (o1, o2) -> {
          final Long seq = o1.seqNo() - o2.seqNo();
          return seq.intValue();
        });
  }

  public List<TestDefinitionResponse> getTestByGrade(String orgId, String gradeSlug) {
    List<TestDefinition> testDefinitions =
        testDefinitionRepository
            .findTop100ByGradeSlugAndOrganizationAndTypeAndDeletedAtIsNullOrderByCreatedAtDesc(
                gradeSlug, orgId, TestType.SCHOOL_TEST);
    return testDefinitions.stream()
        .map(
            testDefinition -> {
              TestDefinitionResponse response = new TestDefinitionResponse();
              response.setTestName(testDefinition.getTestName());
              response.setId(testDefinition.getId());
              response.setType(testDefinition.getType());
              response.setGradeName(testDefinition.getGradeSlug());
              return response;
            })
        .toList();
  }

  public List<TestDefinitionExamInfo> getCompetitiveExams(String orgSlug) {
    return testDefinitionRepository.getCompetitiveExams(orgSlug);
  }

  public void validateTestDefinitions(List<Long> testDefinitionId) {
    testDefinitionId.forEach(
        id -> {
          var optionalTestDefinition = testDefinitionRepository.findById(id);
          if (optionalTestDefinition.isEmpty()) {
            throw new ApiException(
                INVALID_REQUEST, "error.Invalid.TestDefinition", new String[] {id.toString()});
          }
        });
  }

  public void removeVideoExplanation(long testDefinitionId) {
    TestDefinition testDefinition = getTestDefinitionById(testDefinitionId);
    TestDefinitionMetadata testDefinitionMetadata = testDefinition.getMetadata();
    testDefinitionMetadata.setAssetSlug(null);
    testDefinitionRepository.save(testDefinition);
  }

  public QuestionDto.TestQuestionResponse getTestDefinitionSections(Long testDefinitionId) {
    var testDefinition = testDefinitionRepository.findById(testDefinitionId);
    if (testDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Test definition not found");
    }
    var testDefinitionEntity = testDefinition.get();
    var sectionDetails = testDefinitionEntity.getTestDefinitionSections();
    List<Long> testDefinitionSectionIds =
        sectionDetails.stream().map(section -> section.getId()).collect(toList());

    var bluePrintSections =
        bluePrintSectionRepository.getBpSectionsByTestDefinitionId(testDefinitionId);

    List<QuestionDto.SectionResponse> sectionResponses =
        testQuestionRepository
            .findSectionResponsesByTestDefinitionSectionIds(testDefinitionSectionIds)
            .stream()
            .map(
                section -> {
                  List<String> tags = new ArrayList<>();
                  if (!bluePrintSections.isEmpty()) {
                    tags =
                        bluePrintSections.stream()
                            .filter(bps -> bps.getSectionName().equals(section.getSectionName()))
                            .map(BluePrintSections::getTags)
                            .toList();
                  }
                  return QuestionDto.SectionResponse.builder()
                      .sectionId(section.getSectionId())
                      .sectionName(section.getSectionName())
                      .chapterName(section.getChapterName())
                      .chapterSlug(section.getChapterSlug())
                      .marks(section.getMarks())
                      .questionCount(section.getQuestionCount())
                      .category(section.getCategory())
                      .complexity(section.getComplexity())
                      .type(QuestionType.valueOf(section.getType()))
                      .questionTag(String.valueOf(tags))
                      .build();
                })
            .toList();
    return new QuestionDto.TestQuestionResponse(testDefinitionId, sectionResponses);
  }

  public void refreshTestDefinitionById(long testDefinitionId, String bearerToken) {
    var testDefinition = testDefinitionRepository.findById(testDefinitionId);

    if (testDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Test definition not found");
    }

    var testDefinitionEntity = testDefinition.get();

    var sectionDetails = testDefinitionEntity.getTestDefinitionSections();
    if (!sectionDetails.isEmpty()) {
      // unPublish testDefinition
      publishTestDefinitionById(testDefinitionId, false, bearerToken, true);
      sectionDetails.forEach(
          section -> {
            var sectionSubtopics =
                section.getTestQuestions().stream()
                    .map(TestQuestion::getSubtopicSlug)
                    .distinct()
                    .toList();
            if (sectionSubtopics.isEmpty()) {
              return;
            }

            var firstQuestion = section.getTestQuestions().get(0);
            var subtopicQuestions =
                contentService.getQuestionsBySubjectAndSubtopics(
                    testDefinitionEntity.getOrganization(),
                    QuestionType.valueOf(firstQuestion.getType()),
                    testDefinitionEntity.getSubjectSlug(),
                    sectionSubtopics);

            var existingQuestions = section.getTestQuestions();

            Set<String> subtopicQuestionUuids =
                subtopicQuestions.questions().stream()
                    .map(QuestionDto.Question::uuid)
                    .collect(Collectors.toSet());

            Set<String> existingQuestionUuids =
                existingQuestions.stream()
                    .map(TestQuestion::getQuestionUuid)
                    .collect(Collectors.toSet());

            List<String> questionsToAdd =
                subtopicQuestionUuids.stream()
                    .filter(q -> !existingQuestionUuids.contains(q))
                    .toList();

            List<String> questionsToDelete =
                existingQuestionUuids.stream()
                    .filter(q -> !subtopicQuestionUuids.contains(q))
                    .toList();
            var testDefinitionRequest =
                TestDefinitionRequest.builder()
                    .questions(
                        buildElpTestQuestions(questionsToAdd, subtopicQuestions, section.getId()))
                    .build();

            if (testDefinitionRequest.getQuestions().stream().count() > 0) {
              addQuestionsToSection(testDefinitionId, testDefinitionRequest, section.getId());
            }
            if (questionsToDelete.stream().count() > 0) {
              deleteQuestions(questionsToDelete, section.getId());
            }
          });
      // Publish testDefinition
      publishTestDefinitionById(testDefinitionId, true, bearerToken, true);
    }
  }

  private void deleteQuestions(List<String> questionsToDelete, long sectionId) {
    if (!questionsToDelete.isEmpty()) {
      questionsToDelete.forEach(
          uuid -> testQuestionRepository.deleteQuestionBySectionAndUuid(uuid, sectionId));
    }
  }

  private List<TestQuestionRequest> buildElpTestQuestions(
      List<String> questionsToAdd,
      QuestionDto.SearchQuestionResponse sectionSubtopics,
      Long sectionId) {
    List<TestQuestionRequest> newQuestions = new ArrayList<>();
    questionsToAdd.forEach(
        s -> {
          var question =
              sectionSubtopics.questions().stream().filter(x -> x.uuid().equals(s)).findAny();
          if (question.isPresent()) {
            var q = question.get();
            newQuestions.add(
                TestQuestionRequest.builder()
                    .questionUuid(q.uuid())
                    .chapterSlug(q.chapterSlug())
                    .marks(q.marks())
                    .type(String.valueOf(q.type()))
                    .subtopicSlug(q.subtopicSlug())
                    .subjectSlug(q.subjectSlug())
                    .negativeMarks(q.negativeMarks())
                    .testDefinitionSectionId(sectionId)
                    .mcqAnswer(q.type().equals(QuestionType.MCQ) ? q.mcq().answer() : null)
                    .natAnswer(q.type().equals(QuestionType.NAT) ? q.nat().answer() : null)
                    .yesNo(q.type().equals(QuestionType.YESNO) ? q.yesNo().answer() : null)
                    .fbqAnswer(q.type().equals(QuestionType.FBQ) ? q.fbq().answer() : null)
                    .amcqAnswer(q.type().equals(QuestionType.AMCQ) ? q.amcq().answer() : null)
                    .build());
          }
        });
    return newQuestions;
  }

  public List<QuestionDto.SearchQuestionResponse> getTestDefQuestionsResponses(
      TestDefinition testDefinition, String bearerToken, String orgSlug) {
    if (Objects.nonNull(testDefinition.getType())
        && TestType.ASSIGNMENT.name().equals(testDefinition.getType().name())) {
      List<QuestionDto.SearchQuestionResponse> searchQuestionResponse = new ArrayList<>();
      List<QuestionDto.Question> listOfQuestions = new ArrayList<>();
      var testQuestions = getTestQuestionsForTestDefinition(testDefinition);
      testQuestions.forEach(
          question ->
              listOfQuestions.add(
                  buildQuestionData(
                      contentService.getAssignmentQuestionByUuid(
                          bearerToken, question.getQuestionUuid()))));
      searchQuestionResponse.add(
          QuestionDto.SearchQuestionResponse.builder().questions(listOfQuestions).build());
      return searchQuestionResponse;
    }
    return getQuestionsByUuid(
        bearerToken, getTestQuestionsForTestDefinition(testDefinition), orgSlug);
  }

  public QuestionDto.Question buildQuestionData(Question questionByUuid) {
    return QuestionDto.Question.builder()
        .id(questionByUuid.getId())
        .question(questionByUuid.getQuestions())
        .uuid(questionByUuid.getUuid())
        .type(QuestionType.getByType(questionByUuid.getType()))
        .complexity(questionByUuid.getComplexity())
        .marks(questionByUuid.getMarks())
        .explanation(questionByUuid.getExplanation())
        .chapterSlug(questionByUuid.getChapterSlug())
        .subtopicSlug(questionByUuid.getSubtopicSlug())
        .subjectSlug(questionByUuid.getSubjectSlug())
        .organization(questionByUuid.getOrganization())
        .category(questionByUuid.getQuestionCategoryId())
        .active(questionByUuid.isActive())
        .organizationSlug(questionByUuid.getOrganizationSlug())
        .questionCategoryId(questionByUuid.getQuestionCategoryId())
        .build();
  }

  public QuestionDto.QuestionResponse getQuestionResponsePreconfigured(
      TestDefinition testDefinition, Integer questionSetNo) {
    if (testDefinition.getQuestionV2() == null || testDefinition.getQuestionV2().isEmpty()) {
      throw new ApiException(INVALID_REQUEST, "error.serverError");
    }
    final String s3Path = testDefinition.getQuestionV2().get(questionSetNo);
    try {
      var questionResponse =
          storageService.downloadFile(s3Path, QuestionDto.QuestionResponse.class);
      return QuestionDto.QuestionResponse.builder()
          .noOfQuestions(questionResponse.noOfQuestions())
          .gradeName(questionResponse.gradeName())
          .testName(questionResponse.testName())
          .subjectName(questionResponse.subjectName())
          .testDefinitionId(questionResponse.testDefinitionId())
          .testDefinitionSectionResponses(
              buildTestDefinitionSectionResponse(
                  questionResponse.testDefinitionSectionResponses(), testDefinition))
          .build();

    } catch (IOException e) {
      throw new ApiException(INVALID_REQUEST, "Error.read.questions");
    }
  }

  private List<QuestionDto.TestDefinitionSectionResponse> buildTestDefinitionSectionResponse(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses,
      TestDefinition testDefinition) {
    List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionList = new ArrayList<>();
    testDefinitionSectionResponses.forEach(
        section ->
            testDefinitionSectionList.add(
                QuestionDto.TestDefinitionSectionResponse.builder()
                    .id(section.id())
                    .name(section.name())
                    .noOfQuestions(section.noOfQuestions())
                    .seqNo(section.seqNo())
                    .questions(
                        buildSectionQuestions(section.questions(), section.id(), testDefinition))
                    .build()));
    Collections.sort(
        testDefinitionSectionList,
        Comparator.comparing(QuestionDto.TestDefinitionSectionResponse::id));
    return testDefinitionSectionList;
  }

  private List<QuestionDto.Question> buildSectionQuestions(
      List<QuestionDto.Question> transformQuestionsResult,
      Long sectionId,
      TestDefinition testDefinition) {

    var section =
        testDefinition.getTestDefinitionSections().stream()
            .filter(x -> x.getId().equals(sectionId))
            .findFirst();

    if (section.isEmpty()) {
      return Collections.emptyList();
    }

    return buildQuestionsResponse(transformQuestionsResult, section.get().getTestQuestions());
  }

  public void migrateTestQuestionAnswers(Long testDefinitionId) {
    var testDefinition = testDefinitionRepository.findById(testDefinitionId).orElseThrow();
    List<TestQuestion> testQuestions =
        testQuestionRepository.findAllByTestDefinitionSectionInAndType(
            testDefinition.getTestDefinitionSections(), QuestionType.PBQ.name());
    var toBeUpdatedQuestions =
        testQuestions.stream()
            .filter(
                question ->
                    Objects.isNull(question.getPbqAnswers())
                        || question.getPbqAnswers().answers().isEmpty())
            .toList();
    List<PbqDto.Answers> pbqAnswerResponses = new ArrayList<>();
    if (!toBeUpdatedQuestions.isEmpty()) {
      for (TestQuestion question : toBeUpdatedQuestions) {
        List<TestQuestion> questionWithAnswer =
            testQuestionRepository.findAllByQuestionUuidAndPbqAnswersIsNotNull(
                question.getQuestionUuid());
        if (!questionWithAnswer.isEmpty()) {
          question.setPbqAnswers(questionWithAnswer.getFirst().getPbqAnswers());
        } else {
          var questionResponse =
              contentService.getQuestionsByUuid(
                  contentBearerToken,
                  question.getType(),
                  question.getQuestionUuid(),
                  testDefinition.getOrganization());
          var pbqQuestion = questionResponse.questions().getFirst();
          for (QuestionDto.Pbq pbq : pbqQuestion.pbq()) {
            pbqAnswerResponses.add(
                PbqDto.Answers.builder()
                    .mcq(
                        PbqDto.Mcq.builder()
                            .questionUuid(pbq.uuid())
                            .answer(pbq.mcq().answer().intValue())
                            .build())
                    .type(pbq.type())
                    .build());
          }
          question.setPbqAnswers(PbqDto.Data.builder().answers(pbqAnswerResponses).build());
        }
      }
      testQuestionRepository.saveAll(toBeUpdatedQuestions);
    }
  }

  public void addScAnswerSheetTemplate(Long testDefinitionId, Long reportCardTemplateId) {
    if (!reportCardTemplateRepository.existsById(reportCardTemplateId)) {
      throw new ApiException(INVALID_REQUEST, "error.InvalidScAnswerSheetTemplateId");
    }
    var testDefinition = testDefinitionRepository.findById(testDefinitionId).orElseThrow();
    testDefinition.setReportCardTemplateId(reportCardTemplateId);
    testDefinitionRepository.save(testDefinition);
  }

  public void testEnrichmentByTestDef(long testDefinitionId) {
    var testDefinition = validationUtils.validateTestDefinition(testDefinitionId);
    List<TestQuestion> testQuestions =
        testDefinition.getTestDefinitionSections().stream()
            .map(TestDefinitionSection::getTestQuestions)
            .flatMap(Collection::stream)
            .toList();
    var testEnrichments = getTestEnrichmentByTestQuestions(testQuestions);
    var testEnrichmentMap =
        testEnrichments.stream().collect(Collectors.groupingBy(TestEnrichment::getQuestionUuid));
    var questionList = getQuestionsByTestDef(testDefinition);
    var questionMap =
        questionList.stream().collect(Collectors.groupingBy(QuestionDto.Question::uuid));
    List<TestEnrichment> testEnrichmentList = new ArrayList<>();
    testQuestions.forEach(
        tq -> {
          if (StringUtils.containsAnyIgnoreCase(
                  tq.getType(), QuestionType.MCQ.name(), QuestionType.SUBJECTIVE.name())
              && !testEnrichmentMap.containsKey(tq.getQuestionUuid())) {
            var question = questionMap.get(tq.getQuestionUuid()).getFirst();
            var enrichQuestion = enrichQuestion(question);
            if (Objects.nonNull(enrichQuestion)) {
              testEnrichmentList.add(
                  TestEnrichment.builder()
                      .questionType(QuestionType.getByType(tq.getType()))
                      .concept(enrichQuestion.concept())
                      .content(enrichQuestion.data())
                      .summary(enrichQuestion.summary())
                      .reference(enrichQuestion.references())
                      .questionUuid(tq.getQuestionUuid())
                      .subtopicSlug(question.subtopicSlug())
                      .build());
            }
          }
        });
    testEnrichmentRepository.saveAll(testEnrichmentList);
  }

  private List<QuestionDto.Question> getQuestionsByTestDef(TestDefinition testDefinition) {
    if (TestType.MOCK_TEST.equals(testDefinition.getType())) {
      var testDefinitionQuestions = getTestDefinitionQuestions(testDefinition.getId(), 1);
      return testDefinitionQuestions.testDefinitionSectionResponses().stream()
          .map(QuestionDto.TestDefinitionSectionResponse::questions)
          .flatMap(Collection::stream)
          .toList();
    } else if (TestType.SCHOOL_TEST.equals(testDefinition.getType())) {
      var testQuestions =
          testDefinition.getTestDefinitionSections().stream()
              .map(TestDefinitionSection::getTestQuestions)
              .flatMap(Collection::stream)
              .toList();
      var questions =
          getQuestionsByUuid(contentBearerToken, testQuestions, testDefinition.getOrganization());
      return questions.stream()
          .map(QuestionDto.SearchQuestionResponse::questions)
          .flatMap(Collection::stream)
          .toList();
    }
    throw new ApiException(
        INVALID_REQUEST,
        "Could not support test enrichment for : %s".formatted(testDefinition.getType().name()));
  }

  private ExamAnalysis.TestEnrichmentAiResponse enrichQuestion(QuestionDto.Question question) {
    try {
      var subTopicBySlug =
          contentService.getSubTopicBySlug(
              Constants.WEXL_INTERNAL, question.subtopicSlug(), contentBearerToken);
      return englishTutor.enrichQuestion(
          generateTestEnrichment(
              TestDefinitionsDto.TestEnrichmentRequest.builder()
                  .board(subTopicBySlug.getBoardSlug())
                  .grade(subTopicBySlug.getGradeName())
                  .subject(question.subjectSlug())
                  .question(question.question())
                  .chapter(subTopicBySlug.getChapterName())
                  .subtopic(subTopicBySlug.getName())
                  .build()));
    } catch (Exception e) {
      log.error("Failed to generate test enrichment question : {}", e.getMessage(), e);
      return null;
    }
  }

  private String generateTestEnrichment(
      TestDefinitionsDto.TestEnrichmentRequest testEnrichmentRequest) {
    var context = new Context();
    context.setVariable("str", testEnrichmentRequest);
    context.setVariable("content", testEnrichmentRequest.question());
    return templateEngine.process("prompts/test-enrichment-prompt", context);
  }

  private List<TestEnrichment> getTestEnrichmentByTestQuestions(List<TestQuestion> testQuestions) {
    var questionUuids = testQuestions.stream().map(TestQuestion::getQuestionUuid).toList();
    return testEnrichmentRepository.findByQuestionUuidIn(questionUuids);
  }

  public List<GenericMetricResponse> getMockTestReport(String org, Long testDefinition) {
    List<TestDetailsInterface> testDetails;
    List<RangeByExam> testRange;
    User user = authService.getUserDetails();

    if (userRoleHelper.isManager(user)) {
      testDetails = testDefinitionRepository.getTestInsights(testDefinition, null);
    } else {
      testDetails = testDefinitionRepository.getTestInsights(testDefinition, org);
    }
    testRange = testDefinitionRepository.getRangePercent(testDefinition);
    if (testDetails == null || testDetails.isEmpty()) {
      return Collections.emptyList();
    }

    return testDetails.stream()
        .map(
            testData -> {
              Map<String, Object> data = new HashMap<>();
              Map<String, Object> summary = new HashMap<>();
              double attemptedPercentage =
                  ((double) testData.getStudentAttemptedCount() / testData.getStudentCount()) * 100;
              data.put("test_name", testData.getTestName());
              summary.put("organisation", testData.getOrgName());
              summary.put("total_students", testData.getStudentCount());
              summary.put("attempted_students", testData.getStudentAttemptedCount());
              summary.put("attempted_percentage", String.format("%.2f", attemptedPercentage));
              RangeByExam matchingRange =
                  testRange.stream()
                      .filter(range -> range.getOrgName().equals(testData.getOrgSlug()))
                      .findFirst()
                      .orElse(null);
              if (matchingRange != null) {
                summary.put("greater_than_80", matchingRange.getGreaterThan80());
                summary.put("between_70_and_80", matchingRange.getRange70to80());
                summary.put("between_50_and_70", matchingRange.getRange50to70());
                summary.put("between_30_and_50", matchingRange.getRange30to50());
                summary.put("lesser_than_30", matchingRange.getLesserThan30());
              }
              return GenericMetricResponse.builder().data(data).summary(summary).build();
            })
        .collect(Collectors.toList());
  }

  public void uploadQuestions(
      MultipartFile file, String orgSlug, String board, String grade, String subject) {
    List<QuestionDto.UploadQuestionsRequest> questionList = parseCsv(file);
    QuestionDto.UploadQuestionsResponse contentResponse =
        contentService.uploadQuestions(questionList, orgSlug, board, grade, subject);

    TestDefinition testDefinition =
        saveTestDefinition(contentResponse, board, grade, subject, orgSlug);

    saveTestDefinitionSectionsAndQuestions(testDefinition, contentResponse);
    saveQuestions(testDefinition, contentResponse);
  }

  private void saveQuestions(
      TestDefinition testDefinition, QuestionDto.UploadQuestionsResponse contentResponse) {

    List<TestQuestion> testQuestions = new ArrayList<>();
    var testDefinitionSections =
        testDefinitionSectionRepository.findByTestDefinitionOrderById(testDefinition);
    for (TestDefinitionSection section : testDefinitionSections) {
      List<QuestionDto.UploadQuestionsRequest> sectionQuestions =
          contentResponse.uploadedQuestions().stream()
              .filter(x -> x.sectionName().equals(section.getName()))
              .toList();

      sectionQuestions.forEach(
          sec -> {
            Optional<QuestionDto.Question> optionalQuestion =
                contentResponse.questions().stream()
                    .filter(x -> x.uuid().equals(sec.uuid()))
                    .findFirst();

            optionalQuestion.ifPresent(
                question -> {
                  TestQuestion testQuestion = createTestQuestion(section, question);
                  testQuestions.add(testQuestion);
                });
          });
      if (!testQuestions.isEmpty()) {
        testQuestionRepository.saveAll(testQuestions);
      }
    }
  }

  private TestQuestion createTestQuestion(
      TestDefinitionSection section, QuestionDto.Question question) {
    return TestQuestion.builder()
        .testDefinitionSection(section)
        .questionUuid(question.uuid())
        .type(question.type().name())
        .chapterSlug(question.chapterSlug())
        .chapterName(question.chapterSlug())
        .subjectiveAnswer(
            question.subjective() == null ? null : question.subjective().explanation())
        .yesNo(question.yesNo() == null ? null : question.yesNo().answer())
        .spchAnswer(question.spch() == null ? null : question.spch().answerAudioPath())
        .subjectSlug(question.subjectSlug())
        .marks(question.marks())
        .mcqAnswer(question.mcq() == null ? null : question.mcq().answer())
        .msqAnswer(question.msq() == null ? null : question.msq().answers())
        .fbqAnswer(question.fbq() == null ? null : question.fbq().answer())
        .category(question.category())
        .complexity(question.complexity())
        .natAnswer(question.nat() == null ? null : question.nat().answer())
        .build();
  }

  private void saveTestDefinitionSectionsAndQuestions(
      TestDefinition testDefinition, QuestionDto.UploadQuestionsResponse getContentResponse) {

    var sectionNames =
        getContentResponse.uploadedQuestions().stream()
            .map(QuestionDto.UploadQuestionsRequest::sectionName)
            .distinct()
            .toList();

    AtomicLong sequence = new AtomicLong(1L);

    sectionNames.forEach(
        section -> {
          List<TestDefinitionSection> testDefinitionSectionList = new ArrayList<>();
          long questionsCount =
              getContentResponse.uploadedQuestions().stream()
                  .filter(x -> x.sectionName().equals(section))
                  .count();

          testDefinitionSectionList.add(
              TestDefinitionSection.builder()
                  .testDefinition(testDefinition)
                  .sequenceNumber(sequence.getAndIncrement())
                  .name(section)
                  .noOfQuestions(questionsCount)
                  .build());

          testDefinitionSectionRepository.saveAll(testDefinitionSectionList);
        });
  }

  private TestDefinition saveTestDefinition(
      QuestionDto.UploadQuestionsResponse getContentResponse,
      String board,
      String grade,
      String subject,
      String orgSlug) {
    return createTestDefinition(
        buildTestDefinitionRequest(getContentResponse, board, grade, subject), null, orgSlug);
  }

  private TestDefinitionRequest buildTestDefinitionRequest(
      QuestionDto.UploadQuestionsResponse getContentResponse,
      String board,
      String grade,
      String subject) {
    return TestDefinitionRequest.builder()
        .testType(TestType.MOCK_TEST)
        .testName("slgTest")
        .active(Boolean.TRUE)
        .boardSlug(board)
        .subjectSlug(subject)
        .gradeSlug(grade)
        .isAutoEnabled(Boolean.TRUE)
        .message("All the Best")
        .noOfQuestions(getContentResponse.uploadedQuestions().size())
        .teacherId(authService.getTeacherDetails().getId())
        .build();
  }

  private List<QuestionDto.UploadQuestionsRequest> parseCsv(MultipartFile file) {
    List<QuestionDto.UploadQuestionsRequest> questionList = new ArrayList<>();

    try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
        CSVReader csvReader = new CSVReader(reader)) {

      String[] header = csvReader.readNext();
      if (header == null || header.length < 12) {
        throw new IllegalArgumentException("Invalid CSV format. Expected 12 columns.");
      }

      String[] nextRecord;
      while ((nextRecord = csvReader.readNext()) != null) {
        if (nextRecord.length < 12) {
          continue;
        }

        QuestionDto.UploadQuestionsRequest question =
            new QuestionDto.UploadQuestionsRequest(
                nextRecord[0],
                Integer.parseInt(nextRecord[1]),
                nextRecord[2],
                nextRecord[3],
                nextRecord[4],
                nextRecord[5],
                nextRecord[6],
                nextRecord[7],
                nextRecord[8],
                nextRecord[9],
                nextRecord[10],
                Integer.parseInt(nextRecord[11]),
                UUID.randomUUID().toString());

        questionList.add(question);
      }

      return questionList;
    } catch (CsvValidationException | IOException ex) {
      throw new RuntimeException("Error processing CSV file: " + ex.getMessage(), ex);
    }
  }

  public TestDefinitionsDto.MockTestResponse createMockTestDefinition(
      String orgSlug,
      String teacherAuthUserId,
      TestDefinitionsDto.MockTestRequest mockTestRequest) {
    var user = userRepository.getUserByAuthUserId(teacherAuthUserId);
    if (user == null) {
      throw new ApiException(INVALID_REQUEST, "error.InvalidUser");
    }
    String uuid = UUID.randomUUID().toString();
    String path = getS3path(orgSlug, uuid);
    var testDefinition = new TestDefinition();
    testDefinition.setTestName(mockTestRequest.testName());
    testDefinition.setBoardSlug(mockTestRequest.boardSlug());
    testDefinition.setGradeSlug(mockTestRequest.gradeSlug());
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setActive(true);
    testDefinition.setMessage("Subjective-Correction-v2-test");
    testDefinition.setOrganization(orgSlug);
    testDefinition.setTeacher(user);
    testDefinition.setS3Path(path);
    testDefinition.setNoOfQuestions(0);
    testDefinitionRepository.save(testDefinition);
    return TestDefinitionsDto.MockTestResponse.builder()
        .id(testDefinition.getId())
        .name(testDefinition.getTestName())
        .build();
  }

  private String getS3path(String orgSlug, String uuid) {
    return String.format("%s/qp_subjective/%s.pdf", orgSlug, uuid);
  }

  public Map<String, String> getPdfFileUploadUrl(String orgSlug, Long testDefinitionId) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    String preSignedUrl = storageService.generatePreSignedUrlForUpload(testDefinition.getS3Path());
    String previewUrl = storageService.generatePreSignedUrlForFetch(testDefinition.getS3Path());
    return Map.of(
        "path", testDefinition.getS3Path(), "url", preSignedUrl, "previewUrl", previewUrl);
  }

  public void addQuestionsToTestDefinitionSection(
      long testDefinitionId, TestDefinitionsDto.SectionRequest sectionRequest) {
    var testDefinition = getTestDefinitionById(testDefinitionId);
    int totalQuestions =
        sectionRequest.sections().stream()
            .mapToInt(section -> section.questions() != null ? section.questions().size() : 0)
            .sum();
    testDefinition.setNoOfQuestions(totalQuestions);
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinitionRepository.save(testDefinition);
    List<TestDefinitionSection> savedTestDefinitionSections =
        testDefinitionSectionRepository.saveAll(buildSections(testDefinition, sectionRequest));
    if (savedTestDefinitionSections.isEmpty()) {
      throw new ApiException(INVALID_REQUEST, "No sections were saved for the test definition.");
    }
    List<TestQuestion> testQuestions =
        buildTestQuestions(savedTestDefinitionSections, sectionRequest);
    testQuestionRepository.saveAll(testQuestions);
  }

  private List<TestDefinitionSection> buildSections(
      TestDefinition testDefinition, TestDefinitionsDto.SectionRequest sectionRequest) {
    List<TestDefinitionSection> testDefinitionSections = new ArrayList<>();
    sectionRequest
        .sections()
        .forEach(
            section -> {
              TestDefinitionSection testDefinitionSection =
                  TestDefinitionSection.builder()
                      .testDefinition(testDefinition)
                      .name(section.name())
                      .sequenceNumber(section.seqNo())
                      .noOfQuestions((long) section.questions().size())
                      .build();
              testDefinitionSections.add(testDefinitionSection);
            });
    return testDefinitionSections;
  }

  private List<TestQuestion> buildTestQuestions(
      List<TestDefinitionSection> savedSections, TestDefinitionsDto.SectionRequest sectionRequest) {

    List<TestQuestion> testQuestions = new ArrayList<>();

    for (int i = 0; i < savedSections.size(); i++) {
      TestDefinitionSection savedSection = savedSections.get(i);
      TestDefinitionsDto.Section section = sectionRequest.sections().get(i);

      List<TestQuestion> sectionQuestions =
          section.questions().stream()
              .map(
                  question ->
                      TestQuestion.builder()
                          .questionUuid(question.uuid())
                          .type(String.valueOf(question.type()))
                          .marks(question.marks())
                          .testDefinitionSection(savedSection)
                          .chapterSlug(question.chapterSlug())
                          .chapterName(question.chapterName())
                          .subtopicSlug(question.subtopicSlug())
                          .subjectSlug(question.subjectSlug())
                          .mcqAnswer(
                              (question.type().equals(QuestionType.MCQ)
                                      && question.answer() != null
                                      && !"null".equalsIgnoreCase(question.answer()))
                                  ? Long.valueOf(question.answer())
                                  : null)
                          .subjectiveAnswer(
                              (question.type().equals(QuestionType.SUBJECTIVE)
                                      && question.answer() != null)
                                  ? question.answer()
                                  : null)
                          .build())
              .toList();

      testQuestions.addAll(sectionQuestions);
    }
    return testQuestions;
  }

  private boolean isAiTest(TestDefinition testDefinition) {
    return !Objects.isNull(testDefinition.getS3Path());
  }
}
