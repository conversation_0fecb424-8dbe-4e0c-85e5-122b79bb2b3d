package com.wexl.retail.student.profile;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.parent.auth.ProfileEditRequest;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProfileService {

  private static final String PROFILE_IMAGE = "profileImage";
  private final UserRepository profileRepository;
  private final StrapiService strapiService;
  private final StorageService storageService;
  private final UserIdpService userIdpService;
  private final ContentService contentService;

  @Value("${app.validImageExtension}")
  private final List<String> validImageExtensions;

  public StudentProfileResponse getStudentProfileDetails(String authUserId) {

    User studentUser;
    try {
      studentUser = profileRepository.getUserByAuthUserId(authUserId);
    } catch (ApiException e) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, " error.UserNotFound1", e);
    }

    Student student = studentUser.getStudentInfo();

    Grade grade = contentService.getGradeById(studentUser.getStudentInfo().getClassId());

    Section section = studentUser.getStudentInfo().getSection();

    return StudentProfileResponse.builder()
        .studentFirstName(studentUser.getFirstName())
        .studentLastName(studentUser.getLastName())
        .gender(studentUser.getGender())
        .studentUserName(authUserId)
        .gradeName(grade.getName())
        .sectionName(section.getName())
        .schoolName(studentUser.getStudentInfo().getSchoolName())
        .studentEmail(studentUser.getEmail())
        .orgSlug(studentUser.getOrganization())
        .boardId(student.getBoardId())
        .gradeId(student.getClassId())
        .parentEmail("")
        .parentFirstName("")
        .parentLastName("")
        .parentMobileNumber("")
        .profileImageUrl(
            Objects.isNull(studentUser.getProfileImage())
                ? null
                : getProfileImageUrl(studentUser.getProfileImage()))
        .isDefaultParent(true)
        .attributes(student.getAttributes())
        .build();
  }

  public S3FileUploadResult uploadProfileImage(
      String orgSlug, String imageName, String type, String authUserId) {
    String uploadPath = createProfileImageUploadPath(orgSlug, imageName, type, authUserId);
    User user = profileRepository.getUserByAuthUserId(authUserId);
    user.setProfileImage(uploadPath);
    profileRepository.save(user);
    return S3FileUploadResult.builder()
        .path(uploadPath)
        .url(storageService.generatePreSignedUrlForUpload(uploadPath))
        .build();
  }

  public String getProfileImageUrl(String profileImagePath) {
    return storageService.generatePreSignedUrlForFetch(profileImagePath);
  }

  public String createProfileImageUploadPath(
      String orgSlug, String imageName, String imageType, String authUserId) {
    String role = "";
    User user = profileRepository.getUserByAuthUserId(authUserId);
    if (AuthUtil.isStudent(user)) {
      role = "STUDENT";
    } else if (AuthUtil.isTeacher(user)) {
      role = "TEACHER";
    }
    return "%s/%s/%s/%s/%s.%s"
        .formatted(orgSlug, PROFILE_IMAGE, role, authUserId, imageName, imageType);
  }

  public AnonymousProfileImageResponse createProfileImageUploadPathForSignup(
      String orgSlug, AnonymousProfileImageRequest request) {
    if (!validImageExtensions.contains(request.getExtension().trim())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidImageExtensions");
    }
    String randomUuid = String.valueOf(UUID.randomUUID());
    var signedUrl =
        storageService.generatePreSignedUrlForUpload(
            createAnonymousProfileImagePath(orgSlug, randomUuid, request.getExtension()));
    return AnonymousProfileImageResponse.builder().uuid(randomUuid).signedUrl(signedUrl).build();
  }

  public String createAnonymousProfileImagePath(
      String orgSlug, String reference, String extension) {
    return "%s/%s.%s".formatted(orgSlug, reference, extension);
  }

  public void resetPassword(String authUserId, String newPassword) {
    userIdpService.adminSetUserPassword(authUserId, newPassword);
  }

  public void editStudentProfile(String authUserId, ProfileEditRequest profileEditRequest) {
    User studentUser;
    try {
      studentUser = profileRepository.getUserByAuthUserId(authUserId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "User not found", e);
    }

    if (profileEditRequest.getFirstName() != null) {
      studentUser.setFirstName(profileEditRequest.getFirstName());
    }
    if (profileEditRequest.getLastName() != null) {
      studentUser.setLastName(profileEditRequest.getLastName());
    }

    profileRepository.save(studentUser);
  }
}
