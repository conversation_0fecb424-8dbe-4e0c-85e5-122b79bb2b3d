<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
  <fo:layout-master-set>
    <fo:simple-page-master master-name="invoice">
      <fo:region-body margin="18mm" />
    </fo:simple-page-master>
  </fo:layout-master-set>

  <fo:page-sequence master-reference="invoice">
    <fo:flow flow-name="xsl-region-body" >
      <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
        <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
          <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
            <fo:instream-foreign-object content-width="300%" content-height="300%">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                <defs>
                  <filter id="brightnessFilter">
                    <feComponentTransfer>
                      <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                      <feFuncG type="linear" slope="7"/>
                      <feFuncB type="linear" slope="7"/>
                    </feComponentTransfer>
                  </filter>
                </defs>
                <image filter="url(#brightnessFilter)"
                       x="0" y="0"
                       width="100%" height="100%"
                       th:if="${model.body.orgSlug == 'gil923272'}"
                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/GILLCO_SCHOOL_WATER_MARK.svg"/>
                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%"
                       th:if="${model.body.orgSlug != 'gil923272'}"
                       th:xlink:href="@{${model.header.schoolWaterMark}}"/>
                </svg>
            </fo:instream-foreign-object>
          </fo:block>
        </fo:block-container>
        <fo:table border="none">
          <fo:table-column column-width="26mm" />
          <fo:table-column column-width="120mm" />
          <fo:table-body>
            <fo:table-row>
              <fo:table-cell>
                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196', 'sai681502','gil923272'}, model.body.orgSlug))}">
                  <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                       content-width="75px" scaling="non-uniform"  />
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'sai681502'}">
                  <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/saisenior_logo.jpeg")'
                                       content-width="20mm" content-height="scale-to-fit"  />
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'gil923272'}">
                  <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/GILLCO_INTERNATIONAL_SCHOOL_LOGO.svg")'
                                       content-width="27mm" content-height="scale-to-fit"  />
                </fo:block>
                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                  <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                       content-width="75px" scaling="non-uniform"  />
                </fo:block>
                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                  <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                       content-width="75px" scaling="non-uniform"  />
                </fo:block>
                <fo:block margin-left="150mm" padding-top="-28mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                  <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                       content-width="75px" scaling="non-uniform"  />
                </fo:block>
              </fo:table-cell>
              <fo:table-cell>
                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center"
                          th:if="${model.body.orgSlug == 'pal454783'}">
                  PALLAVI INTERNATIONAL SCHOOL
                  <fo:block font-size="18">GANDIPET</fo:block>
                </fo:block>
                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center"
                          th:if="${model.body.orgSlug == 'pal332908'}">
                  PALLAVI INTERNATIONAL SCHOOL
                  <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                </fo:block>
                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                          th:if="${model.body.orgSlug != 'pal454783' and model.body.orgSlug != 'pal332908'}"
                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="4pt" padding-top="-3mm">
                  <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${ model.body.orgSlug == 'del189476' }">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                  <fo:block> School Code:56955 </fo:block>
                </fo:block>

                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${model.body.orgSlug == 'pal454783'}">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${model.body.orgSlug == 'pal988947'}">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                  </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${model.body.orgSlug == 'pal233196'}">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                  </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${model.body.orgSlug == 'pal174599'}">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                  </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${model.body.orgSlug == 'pal556078'}">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                  </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                          th:if="${ model.body.orgSlug == 'del765517' }">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                          th:if="${ model.body.orgSlug == 'gil923272' }">
                  <fo:block>Affiliated to CBSE, Affiliation No :1630949 </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                          th:if="${ model.body.orgSlug == 'del909850' }">
                  <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                  <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783|gil923272')}">
                  <fo:block th:text="${model.header.isoData}"></fo:block>
                </fo:block>

                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">Record
                  of
                  Academic Performance
                </fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                          th:text="${model.header.testType}">
                </fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt"
                          th:text="${model.header.testName}">
                </fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="6pt"
                          th:text="'Session : '+${model.header.academicYear}">
                </fo:block>
              </fo:table-cell>
            </fo:table-row>
          </fo:table-body>
        </fo:table>
        <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-before="5mm" space-after="2pt">
          <fo:table border="none">
            <fo:table-column column-width="33mm" />
            <fo:table-column column-width="85mm" />
            <fo:table-column column-width="26mm" />
            <fo:table-column column-width="30mm" />
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell font-weight="bold">
                  <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                </fo:table-cell>
                <fo:table-cell font-weight="bold">
                  <fo:block margin-bottom="2mm">Class &amp; Section &#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
              <fo:table-row>
                <fo:table-cell font-weight="bold">
                  <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                </fo:table-cell>
                <fo:table-cell font-weight="bold">
                  <fo:block margin-bottom="2mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
              <fo:table-row>
                <fo:table-cell font-weight="bold">
                  <fo:block margin-bottom="2mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block font-weight="bold" margin-bottom="2mm" th:text="${#strings.toUpperCase(model.body.mothersName)}"></fo:block>
                </fo:table-cell>
                <fo:table-cell font-weight="bold">
                  <fo:block margin-bottom="2mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.dateOfBirth}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>

          </fo:table>
        </fo:block>
        <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif">
          <fo:table border="none">
            <fo:table-column column-width="40mm" />
            <fo:table-column column-width="85mm" />
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell font-weight="bold">
                  <fo:block>Father's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block  font-weight="bold" th:text="${#strings.toUpperCase(model.body.fathersName)}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:block>
        <!-- Attendance -->
        <fo:block border-width="1mm" font-size="9pt" space-after="25pt" font-family="Times New Roman, serif" space-before="2.5mm">
          <fo:table border="none">
            <fo:table-column column-width="52mm" />
            <fo:table-column column-width="66mm" />
            <fo:table-column column-width="41mm" />
            <fo:table-column column-width="39mm" />
            <fo:table-body font-family="Times New Roman, serif" >
              <fo:table-row space-after="5pt">
                <fo:table-cell font-weight="bold">
                  <fo:block>Total Number of Working Days&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                </fo:table-cell>
                <fo:table-cell font-weight="bold">
                  <fo:block>Number of Days Present&#160;:</fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:block>
        <!-- Report Card Table -->
        <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
          <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
          <fo:table border="1pt solid black">
            <fo:table-column  column-width="9mm"  />
            <fo:table-column column-width="70mm" />
            <fo:table-column column-width="46.5mm"  />
            <fo:table-column column-width="46.5mm"  />
            <fo:table-header font-size="9pt" >
              <fo:table-row >
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                  <fo:block>S.NO</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                  <fo:block>SUBJECT</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                  <fo:block>Maximum Marks</fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                  <fo:block>Marks Obtained</fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-header>
            <fo:table-body>
              <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                <fo:table-cell border="1pt solid black" padding="1mm">
                  <fo:block th:text="${marks.sno}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                  <fo:block th:text="${marks.subject}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm">
                  <fo:block th:text="${marks.maximumMarks}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm">
                  <fo:block th:text="${marks.marksObtained}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
              <fo:table-row th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}" th:each="secondTableMarks : ${model.body.secondTable.marks}" >
                <fo:table-cell border="1pt solid black" padding="1mm">
                  <fo:block th:text="${secondTableMarks.sno}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                  <fo:block th:text="${secondTableMarks.subject}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm"  >
                  <fo:block th:text="${secondTableMarks.maximumMarks}"></fo:block>
                </fo:table-cell>
                <fo:table-cell border="1pt solid black" padding="1mm">
                  <fo:block th:text="${secondTableMarks.marksObtained}"></fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:block>

        <fo:block border-width="1mm" font-size="10pt" space-before="10mm">
          <fo:table border="none">
            <fo:table-body font-family="Times New Roman, serif">
              <fo:table-row>
                <fo:table-cell font-weight="bold">
                  <fo:block>Class Teacher Remarks&#160;:</fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:block>
        <fo:block space-after="20mm">
          <fo:inline>
            <fo:block-container>
              <fo:block border-bottom="0.2mm solid black" th:text="${model.body.attendance.remarks}">
              </fo:block>
            </fo:block-container>
          </fo:inline>
        </fo:block>

        <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
          <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="10mm">
            <fo:table border="none">
              <fo:table-column column-width="45mm" />
              <fo:table-column column-width="60mm" />
              <fo:table-column column-width="95mm" />
              <fo:table-body font-family="Times New Roman, serif">
                <fo:table-row>
                  <fo:table-cell>
                    <fo:block padding-top="-7mm"> </fo:block>
                  </fo:table-cell>
                  <fo:table-cell>
                    <fo:block padding-top="-7mm"> </fo:block>
                  </fo:table-cell>
                  <fo:table-cell>
                    <fo:block>
                      <fo:block-container padding-top="-5mm" width="100mm" height="35mm" display-align="center" text-align="center">
                        <fo:block>
                          <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                               scaling="non-uniform"
                                               content-width="35mm"
                                               content-height="15mm" />
                        </fo:block>
                      </fo:block-container>
                    </fo:block>
                  </fo:table-cell>
                </fo:table-row>

                <fo:table-row >
                  <fo:table-cell padding-top="-12mm" text-align="left" font-weight="bold">
                    <fo:block>Class Teacher Signature  </fo:block>
                  </fo:table-cell>
                  <fo:table-cell>
                    <fo:block padding-top="-12mm" > </fo:block>
                  </fo:table-cell>
                  <fo:table-cell padding-left="12mm" padding-top="-12mm"  text-align="center" font-weight="bold">
                    <fo:block>Principal</fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block>
        </fo:block>

        <fo:block th:if="${(model.body.orgSlug matches 'pal174599|pal233196|pal556078')}">
          <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
            <fo:table border="none">
              <fo:table-column column-width="50mm" />
              <fo:table-column column-width="210mm" />
              <fo:table-body font-family="Times New Roman, serif">
                <fo:table-row >
                  <fo:table-cell text-align="left" font-weight="bold">
                    <fo:block>Class Teacher</fo:block>
                  </fo:table-cell>
                  <fo:table-cell text-align="center" font-weight="bold">
                    <fo:block>Principal</fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block>
        </fo:block>

        <fo:block th:if="${(model.body.orgSlug matches 'pal332908')}">
          <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
            <fo:table border="none">
              <fo:table-column column-width="50mm" />
              <fo:table-column column-width="210mm" />
              <fo:table-body font-family="Times New Roman, serif">
                <fo:table-row >
                  <fo:table-cell text-align="left" font-weight="bold">
                    <fo:block>Principal / Headmaster</fo:block>
                  </fo:table-cell>
                  <fo:table-cell text-align="center" font-weight="bold">
                    <fo:block>Class Teacher</fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block>
        </fo:block>

        <!-- Signature Block-->
        <fo:block th:if="${model.body.orgSlug} == 'dps688668'">
          <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} == 'gil923272'">
          <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} == 'del909850'">
          <fo:block th:replace="report-card/dps/signatureForMahendra.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} == 'del217242'">
          <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} == 'del765517'">
          <fo:block th:replace="report-card/dps/nadergulSignature.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} == 'del189476'">
          <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${model.body.orgSlug} != 'dps688668' and ${model.body.orgSlug} != 'del909850' and ${model.body.orgSlug} != 'del217242'
                    and ${model.body.orgSlug} != 'del765517' and ${model.body.orgSlug} != 'del189476' and
                    ${model.body.orgSlug} != 'pal556078' and ${model.body.orgSlug} != 'pal454783' and ${model.body.orgSlug} != 'pal174599' and
                    ${model.body.orgSlug} != 'pal332908' and ${model.body.orgSlug} != 'pal988947' and ${model.body.orgSlug} != 'pal233196'
                    and ${model.body.orgSlug} != 'gil923272'">
          <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
        </fo:block>
        <fo:block th:if="${(model.body.orgSlug matches 'pal988947')}">
          <fo:block th:replace="report-card/dps/pallavi-signature.xml :: ${model.body.gradeSlug}"></fo:block>
        </fo:block>
        <!--<fo:block th:replace="~{${model.body.orgSlug matches 'pal556078|pal454783|pal174599|pal332908|pal988947|pal233196'}
    ? 'report-card/dps/pallavi-fragment.xml'
    : 'report-card/dps/fragment.xml'
    :: ${model.body.gradingScale}}">
        </fo:block>-->
      </fo:block-container>
    </fo:flow>
  </fo:page-sequence>
</fo:root>


